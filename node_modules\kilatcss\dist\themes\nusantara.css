/**
 * KilatCSS Nusantara Theme 🇮🇩
 * Indonesian cultural colors, batik patterns, and traditional elements
 */

[data-kilat-theme="nusantara"] {
  /* 🇮🇩 Nusantara Color Palette */
  --k-primary: #d4af37;        /* <PERSON><PERSON> (Gold) */
  --k-secondary: #8b4513;      /* <PERSON><PERSON><PERSON> (<PERSON>) */
  --k-accent: #dc143c;         /* <PERSON><PERSON> (Pomegranate Red) */
  --k-background: #2f1b14;     /* <PERSON><PERSON><PERSON> (Dark Brown) */
  --k-surface: #3d2817;       /* <PERSON><PERSON><PERSON> (Old Wood) */
  --k-text: #f5f5dc;          /* <PERSON>rem (Cream) */
  --k-text-muted: #d2b48c;    /* Tan */
  
  /* 🌺 Traditional Indonesian Colors */
  --k-garuda-gold: #d4af37;
  --k-batik-brown: #8b4513;
  --k-candi-stone: #696969;
  --k-padi-green: #228b22;
  --k-sunset-orange: #ff8c00;
  --k-ocean-blue: #4682b4;
  --k-spice-red: #dc143c;
  --k-bamboo-green: #9acd32;
  
  /* 🎨 Batik <PERSON>tern Colors */
  --k-batik-indigo: #4b0082;
  --k-batik-sogan: #8b4513;
  --k-batik-cream: #f5f5dc;
  --k-batik-gold: #ffd700;
  
  /* 🌅 Natural Indonesian Colors */
  --k-sunrise: linear-gradient(135deg, #ff8c00, #ffd700, #ff6347);
  --k-forest: linear-gradient(135deg, #228b22, #32cd32, #9acd32);
  --k-ocean: linear-gradient(135deg, #4682b4, #87ceeb, #b0e0e6);
  --k-volcano: linear-gradient(135deg, #dc143c, #ff6347, #ff8c00);
  
  /* 🔮 Glow Effects (Softer for Traditional Feel) */
  --k-glow-sm: 0 0 3px currentColor, 0 0 6px rgba(212, 175, 55, 0.3);
  --k-glow-md: 0 0 6px currentColor, 0 0 12px rgba(212, 175, 55, 0.4), 0 0 18px rgba(212, 175, 55, 0.2);
  --k-glow-lg: 0 0 9px currentColor, 0 0 18px rgba(212, 175, 55, 0.5), 0 0 27px rgba(212, 175, 55, 0.3);
  --k-glow-xl: 0 0 12px currentColor, 0 0 24px rgba(212, 175, 55, 0.6), 0 0 36px rgba(212, 175, 55, 0.4);
}

/* 🏛️ Nusantara Body Styling */

[data-kilat-theme="nusantara"] body,
[data-kilat-theme="nusantara"] .kilat {
  background: 
    radial-gradient(circle at 30% 70%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 70% 30%, rgba(139, 69, 19, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, #2f1b14, #3d2817, #2f1b14);
  color: var(--k-text);
  font-family: 'Poppins', 'Nunito Sans', var(--k-font-sans);
}

/* 🌟 Nusantara Text Effects */

[data-kilat-theme="nusantara"] .k-text-glow-garuda {
  color: var(--k-garuda-gold);
  text-shadow: 
    0 0 5px currentColor,
    0 0 10px rgba(212, 175, 55, 0.5),
    0 0 15px rgba(212, 175, 55, 0.3);
  animation: garuda-shine 3s ease-in-out infinite alternate;
}

[data-kilat-theme="nusantara"] .k-text-glow-batik {
  color: var(--k-batik-sogan);
  text-shadow: 
    0 0 3px currentColor,
    0 0 6px rgba(139, 69, 19, 0.4);
  animation: batik-weave 4s linear infinite;
}

[data-kilat-theme="nusantara"] .k-text-glow-spice {
  color: var(--k-spice-red);
  text-shadow: 
    0 0 4px currentColor,
    0 0 8px rgba(220, 20, 60, 0.4);
  animation: spice-warmth 2.5s ease-in-out infinite alternate;
}

/* 🎭 Nusantara Animations */

@keyframes garuda-shine {
  0% { 
    text-shadow: 
      0 0 5px currentColor,
      0 0 10px rgba(212, 175, 55, 0.5),
      0 0 15px rgba(212, 175, 55, 0.3);
  }
  100% { 
    text-shadow: 
      0 0 8px currentColor,
      0 0 16px rgba(212, 175, 55, 0.7),
      0 0 24px rgba(212, 175, 55, 0.5);
  }
}

@keyframes batik-weave {
  0%, 100% { opacity: 1; }
  25% { opacity: 0.9; }
  50% { opacity: 0.95; }
  75% { opacity: 0.85; }
}

@keyframes spice-warmth {
  0% { 
    text-shadow: 
      0 0 4px currentColor,
      0 0 8px rgba(220, 20, 60, 0.4);
  }
  100% { 
    text-shadow: 
      0 0 6px currentColor,
      0 0 12px rgba(220, 20, 60, 0.6);
  }
}

@keyframes candi-glow {
  0% { box-shadow: 0 0 10px rgba(212, 175, 55, 0.3); }
  50% { box-shadow: 0 0 20px rgba(212, 175, 55, 0.5); }
  100% { box-shadow: 0 0 10px rgba(212, 175, 55, 0.3); }
}

/* 🏛️ Nusantara Borders (Candi Style) */

[data-kilat-theme="nusantara"] .k-border-candi {
  border: 2px solid var(--k-garuda-gold);
  box-shadow: 
    0 0 15px rgba(212, 175, 55, 0.3),
    inset 0 0 15px rgba(212, 175, 55, 0.1);
  position: relative;
}

[data-kilat-theme="nusantara"] .k-border-candi::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--k-garuda-gold), var(--k-batik-sogan), var(--k-garuda-gold));
  z-index: -1;
  border-radius: inherit;
  opacity: 0.2;
  animation: candi-glow 3s ease-in-out infinite;
}

/* 🎯 Nusantara Buttons */

[data-kilat-theme="nusantara"] .k-btn-nusantara {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(139, 69, 19, 0.2));
  border: 1px solid var(--k-garuda-gold);
  color: var(--k-garuda-gold);
  padding: 0.75rem 1.5rem;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 
    0 4px 15px rgba(212, 175, 55, 0.2),
    inset 0 0 15px rgba(212, 175, 55, 0.1);
  border-radius: 8px;
}

[data-kilat-theme="nusantara"] .k-btn-nusantara::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.3), transparent);
  transition: left 0.5s ease;
}

[data-kilat-theme="nusantara"] .k-btn-nusantara:hover::before {
  left: 100%;
}

[data-kilat-theme="nusantara"] .k-btn-nusantara:hover {
  box-shadow: 
    0 6px 20px rgba(212, 175, 55, 0.4),
    inset 0 0 20px rgba(212, 175, 55, 0.2);
  transform: translateY(-1px);
}

/* 🎮 Nusantara Cards (Batik Style) */

[data-kilat-theme="nusantara"] .k-card-nusantara {
  background: 
    linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(139, 69, 19, 0.1)),
    rgba(61, 40, 23, 0.9);
  border: 1px solid rgba(212, 175, 55, 0.3);
  backdrop-filter: blur(8px);
  box-shadow: 
    0 8px 25px rgba(0, 0, 0, 0.3),
    0 0 15px rgba(212, 175, 55, 0.1),
    inset 0 0 15px rgba(212, 175, 55, 0.05);
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

[data-kilat-theme="nusantara"] .k-card-nusantara::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(139, 69, 19, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

/* 🔍 Nusantara Inputs */

[data-kilat-theme="nusantara"] .k-input-nusantara {
  background: rgba(47, 27, 20, 0.7);
  border: 1px solid rgba(212, 175, 55, 0.3);
  color: var(--k-text);
  padding: 0.75rem 1rem;
  font-family: 'Poppins', sans-serif;
  transition: all 0.3s ease;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
  border-radius: 6px;
}

[data-kilat-theme="nusantara"] .k-input-nusantara:focus {
  outline: none;
  border-color: var(--k-garuda-gold);
  box-shadow: 
    0 0 15px rgba(212, 175, 55, 0.3),
    inset 0 0 10px rgba(212, 175, 55, 0.1);
}

[data-kilat-theme="nusantara"] .k-input-nusantara::-moz-placeholder {
  color: var(--k-text-muted);
  opacity: 0.8;
}

[data-kilat-theme="nusantara"] .k-input-nusantara::placeholder {
  color: var(--k-text-muted);
  opacity: 0.8;
}

/* 🌐 Nusantara Scrollbar */

[data-kilat-theme="nusantara"] ::-webkit-scrollbar {
  width: 10px;
}

[data-kilat-theme="nusantara"] ::-webkit-scrollbar-track {
  background: rgba(47, 27, 20, 0.5);
  border-radius: 5px;
}

[data-kilat-theme="nusantara"] ::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--k-garuda-gold), var(--k-batik-sogan));
  border-radius: 5px;
  box-shadow: 0 0 8px rgba(212, 175, 55, 0.3);
}

[data-kilat-theme="nusantara"] ::-webkit-scrollbar-thumb:hover {
  box-shadow: 0 0 15px rgba(212, 175, 55, 0.5);
}

/* 🎨 Batik Pattern Utilities */

[data-kilat-theme="nusantara"] .k-pattern-batik {
  background-image: 
    radial-gradient(circle at 20% 20%, rgba(212, 175, 55, 0.1) 0%, transparent 25%),
    radial-gradient(circle at 80% 80%, rgba(139, 69, 19, 0.1) 0%, transparent 25%),
    radial-gradient(circle at 40% 60%, rgba(220, 20, 60, 0.05) 0%, transparent 25%);
  background-size: 40px 40px, 60px 60px, 80px 80px;
}

/* 🏛️ Traditional Indonesian Elements */

[data-kilat-theme="nusantara"] .k-accent-garuda {
  color: var(--k-garuda-gold);
  font-weight: 700;
  text-shadow: 0 0 8px rgba(212, 175, 55, 0.4);
}

[data-kilat-theme="nusantara"] .k-accent-batik {
  color: var(--k-batik-sogan);
  font-style: italic;
}

[data-kilat-theme="nusantara"] .k-accent-spice {
  color: var(--k-spice-red);
  font-weight: 600;
}
