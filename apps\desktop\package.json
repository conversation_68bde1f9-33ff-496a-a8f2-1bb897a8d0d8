{"name": "@kilat/desktop-demo", "version": "1.0.0", "description": "🖥️ Kilat.js Desktop Demo - Electron showcase application", "main": "dist/main.js", "homepage": "./", "scripts": {"dev": "concurrently \"npm run dev:renderer\" \"npm run dev:main\"", "dev:renderer": "vite --port 5174", "dev:main": "tsc -p tsconfig.main.json && electron dist/main.js --dev", "build": "npm run build:renderer && npm run build:main", "build:renderer": "vite build", "build:main": "tsc -p tsconfig.main.json", "package": "npm run build && electron-builder", "package:win": "npm run build && electron-builder --win", "package:mac": "npm run build && electron-builder --mac", "package:linux": "npm run build && electron-builder --linux", "dist": "npm run build && electron-builder --publish=never", "test": "vitest", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "kilat-core": "workspace:*", "kilat-router": "workspace:*", "kilat-utils": "workspace:*", "kilatcss": "workspace:*", "kilatanim.js": "workspace:*", "kilat-plugins": "workspace:*", "three": "^0.158.0", "@react-three/fiber": "^8.15.0", "@react-three/drei": "^9.88.0", "framer-motion": "^10.16.0", "lucide-react": "^0.294.0"}, "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.0", "concurrently": "^8.2.0", "wait-on": "^7.0.1", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/three": "^0.158.0", "@vitejs/plugin-react": "^4.0.0", "vite": "^5.0.0", "vitest": "^0.34.0", "typescript": "^5.0.0", "eslint": "^8.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0"}, "build": {"appId": "org.kilatjs.desktop.demo", "productName": "Kilat.js Desktop", "directories": {"output": "release"}, "files": ["dist/**/*", "assets/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.developer-tools", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}]}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}, "keywords": ["kilat", "kilatjs", "desktop", "electron", "cyberpunk", "nusantara", "demo"], "author": "KangPCode", "license": "MIT", "private": true}