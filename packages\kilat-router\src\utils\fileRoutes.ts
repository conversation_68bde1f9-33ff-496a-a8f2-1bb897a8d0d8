import { ComponentType } from 'react';
import type { <PERSON><PERSON><PERSON>out<PERSON>, FileRoute, RouteSegment } from '../types';

// 🎯 File-based Route Generation
export async function createFileRoutes(pagesDirectory: string): Promise<KilatRoute[]> {
  // In a real implementation, this would scan the file system
  // For now, we'll return a mock implementation that demonstrates the concept
  
  const mockFileRoutes: FileRoute[] = [
    {
      filePath: '/pages/index.tsx',
      routePath: '/',
      component: () => import('../../../apps/web/src/pages/index'),
      isDynamic: false,
      segments: [{ type: 'static', value: '' }]
    },
    {
      filePath: '/pages/about.tsx',
      routePath: '/about',
      component: () => import('../../../apps/web/src/pages/about'),
      isDynamic: false,
      segments: [{ type: 'static', value: 'about' }]
    },
    {
      filePath: '/pages/blog/index.tsx',
      routePath: '/blog',
      component: () => import('../../../apps/web/src/pages/blog/index'),
      isDynamic: false,
      segments: [{ type: 'static', value: 'blog' }]
    },
    {
      filePath: '/pages/blog/[slug].tsx',
      routePath: '/blog/:slug',
      component: () => import('../../../apps/web/src/pages/blog/[slug]'),
      isDynamic: true,
      segments: [
        { type: 'static', value: 'blog' },
        { type: 'dynamic', value: ':slug', name: 'slug' }
      ]
    },
    {
      filePath: '/pages/user/[id]/profile.tsx',
      routePath: '/user/:id/profile',
      component: () => import('../../../apps/web/src/pages/user/[id]/profile'),
      isDynamic: true,
      segments: [
        { type: 'static', value: 'user' },
        { type: 'dynamic', value: ':id', name: 'id' },
        { type: 'static', value: 'profile' }
      ]
    },
    {
      filePath: '/pages/docs/[...slug].tsx',
      routePath: '/docs/*',
      component: () => import('../../../apps/web/src/pages/docs/[...slug]'),
      isDynamic: true,
      segments: [
        { type: 'static', value: 'docs' },
        { type: 'catch-all', value: '*', name: 'slug' }
      ]
    }
  ];

  // Convert file routes to Kilat routes
  const routes: KilatRoute[] = [];
  
  for (const fileRoute of mockFileRoutes) {
    try {
      // Load component dynamically
      const componentModule = await fileRoute.component();
      const Component = componentModule.default || componentModule;
      
      // Check for layout
      const Layout = componentModule.Layout;
      
      // Check for middleware
      const middleware = componentModule.middleware || [];
      
      // Check for meta
      const meta = componentModule.meta || {};
      
      const route: KilatRoute = {
        path: fileRoute.routePath,
        component: Component as ComponentType<any>,
        layout: Layout,
        middleware,
        meta,
        exact: !fileRoute.isDynamic
      };
      
      routes.push(route);
    } catch (error) {
      console.warn(`Failed to load route: ${fileRoute.filePath}`, error);
      
      // Create a fallback component for failed routes
      const FallbackComponent = () => {
        return React.createElement('div',
          { className: 'k-p-8 k-text-center' },
          React.createElement('h2',
            { className: 'k-text-xl k-text-red-400 k-mb-4' },
            'Route Loading Error'
          ),
          React.createElement('p',
            { className: 'k-text-gray-400' },
            `Failed to load: ${fileRoute.filePath}`
          )
        );
      };
      
      const route: KilatRoute = {
        path: fileRoute.routePath,
        component: FallbackComponent,
        exact: !fileRoute.isDynamic
      };
      
      routes.push(route);
    }
  }
  
  return routes;
}

// 🔍 Parse file path to route path
export function parseFilePath(filePath: string): string {
  // Remove file extension
  let routePath = filePath.replace(/\.(tsx?|jsx?)$/, '');
  
  // Remove /pages prefix
  routePath = routePath.replace(/^\/pages/, '');
  
  // Handle index files
  if (routePath.endsWith('/index')) {
    routePath = routePath.replace('/index', '');
  }
  
  // Handle root index
  if (routePath === '/index' || routePath === '') {
    routePath = '/';
  }
  
  // Convert dynamic segments
  // [slug] -> :slug
  routePath = routePath.replace(/\[([^\]]+)\]/g, ':$1');
  
  // [...slug] -> *
  routePath = routePath.replace(/\[\.\.\.([^\]]+)\]/g, '*');
  
  // [[slug]] -> :slug? (optional)
  routePath = routePath.replace(/\[\[([^\]]+)\]\]/g, ':$1?');
  
  return routePath || '/';
}

// 🧩 Parse route segments
export function parseRouteSegments(routePath: string): RouteSegment[] {
  const segments: RouteSegment[] = [];
  const parts = routePath.split('/').filter(Boolean);
  
  for (const part of parts) {
    if (part.startsWith(':')) {
      // Dynamic segment
      const name = part.slice(1);
      const isOptional = name.endsWith('?');
      const cleanName = isOptional ? name.slice(0, -1) : name;
      
      segments.push({
        type: isOptional ? 'optional' : 'dynamic',
        value: part,
        name: cleanName
      });
    } else if (part === '*') {
      // Catch-all segment
      segments.push({
        type: 'catch-all',
        value: part,
        name: 'slug'
      });
    } else {
      // Static segment
      segments.push({
        type: 'static',
        value: part
      });
    }
  }
  
  return segments;
}

// 🔄 Generate route from file info
export function generateRouteFromFile(
  filePath: string,
  component: ComponentType<any>,
  options: {
    layout?: ComponentType<any>;
    middleware?: string[];
    meta?: Record<string, any>;
  } = {}
): KilatRoute {
  const routePath = parseFilePath(filePath);
  const segments = parseRouteSegments(routePath);
  const isDynamic = segments.some(s => s.type !== 'static');
  
  return {
    path: routePath,
    component,
    layout: options.layout,
    middleware: options.middleware || [],
    meta: options.meta || {},
    exact: !isDynamic
  };
}

// 📁 Scan directory for route files (mock implementation)
export async function scanPagesDirectory(directory: string): Promise<FileRoute[]> {
  // In a real implementation, this would use fs.readdir recursively
  // For now, return mock data
  
  const mockFiles = [
    '/pages/index.tsx',
    '/pages/about.tsx',
    '/pages/contact.tsx',
    '/pages/blog/index.tsx',
    '/pages/blog/[slug].tsx',
    '/pages/user/[id]/profile.tsx',
    '/pages/user/[id]/settings.tsx',
    '/pages/docs/[...slug].tsx',
    '/pages/api/auth/[...nextauth].tsx'
  ];
  
  const fileRoutes: FileRoute[] = [];
  
  for (const filePath of mockFiles) {
    // Skip API routes
    if (filePath.includes('/api/')) continue;
    
    const routePath = parseFilePath(filePath);
    const segments = parseRouteSegments(routePath);
    const isDynamic = segments.some(s => s.type !== 'static');
    
    fileRoutes.push({
      filePath,
      routePath,
      component: () => import(filePath.replace('/pages', '../../../apps/web/src/pages')),
      isDynamic,
      segments
    });
  }
  
  return fileRoutes;
}

// 🎯 Route priority calculation (for sorting)
export function calculateRoutePriority(route: KilatRoute): number {
  let priority = 0;
  const segments = parseRouteSegments(route.path);
  
  for (const segment of segments) {
    switch (segment.type) {
      case 'static':
        priority += 100; // Highest priority
        break;
      case 'dynamic':
        priority += 50;
        break;
      case 'optional':
        priority += 25;
        break;
      case 'catch-all':
        priority += 1; // Lowest priority
        break;
    }
  }
  
  return priority;
}

// 📊 Sort routes by priority
export function sortRoutesByPriority(routes: KilatRoute[]): KilatRoute[] {
  return routes.sort((a, b) => {
    const priorityA = calculateRoutePriority(a);
    const priorityB = calculateRoutePriority(b);
    return priorityB - priorityA; // Higher priority first
  });
}
