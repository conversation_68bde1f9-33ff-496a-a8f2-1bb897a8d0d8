import React from 'react';
import { BrowserRouter } from 'react-router-dom';

/**
 * 🚀 Main App Component
 * Kilat.js Web Demo Application
 */
function App() {
  return (
    <BrowserRouter>
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #000011 0%, #001122 50%, #000033 100%)',
        color: '#00ffff',
        fontFamily: 'system-ui, -apple-system, sans-serif'
      }}>
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          padding: '2rem',
          textAlign: 'center'
        }}>
          <h1 style={{
            fontSize: '4rem',
            marginBottom: '1rem',
            background: 'linear-gradient(45deg, #00ffff, #ff0040)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text'
          }}>
            ⚡ Kilat.js
          </h1>

          <h2 style={{
            fontSize: '1.5rem',
            marginBottom: '2rem',
            color: '#00ffff'
          }}>
            Modular Fullstack Framework
          </h2>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '1rem',
            maxWidth: '800px',
            width: '100%'
          }}>
            <div style={{
              background: 'rgba(0, 255, 255, 0.1)',
              border: '1px solid rgba(0, 255, 255, 0.3)',
              borderRadius: '8px',
              padding: '1.5rem',
              backdropFilter: 'blur(10px)'
            }}>
              <h3 style={{ color: '#00ffff', marginBottom: '0.5rem' }}>🌐 Web App</h3>
              <p style={{ color: '#cccccc', fontSize: '0.9rem' }}>
                React + Vite + TypeScript
              </p>
            </div>

            <div style={{
              background: 'rgba(255, 0, 64, 0.1)',
              border: '1px solid rgba(255, 0, 64, 0.3)',
              borderRadius: '8px',
              padding: '1.5rem',
              backdropFilter: 'blur(10px)'
            }}>
              <h3 style={{ color: '#ff0040', marginBottom: '0.5rem' }}>🖥️ Desktop App</h3>
              <p style={{ color: '#cccccc', fontSize: '0.9rem' }}>
                Electron + React
              </p>
            </div>

            <div style={{
              background: 'rgba(0, 255, 128, 0.1)',
              border: '1px solid rgba(0, 255, 128, 0.3)',
              borderRadius: '8px',
              padding: '1.5rem',
              backdropFilter: 'blur(10px)'
            }}>
              <h3 style={{ color: '#00ff80', marginBottom: '0.5rem' }}>📱 Mobile App</h3>
              <p style={{ color: '#cccccc', fontSize: '0.9rem' }}>
                React Native + Expo
              </p>
            </div>
          </div>

          <div style={{
            marginTop: '3rem',
            display: 'flex',
            gap: '1rem',
            flexWrap: 'wrap',
            justifyContent: 'center'
          }}>
            <button style={{
              background: 'linear-gradient(45deg, #00ffff, #0080ff)',
              border: 'none',
              borderRadius: '6px',
              padding: '12px 24px',
              color: '#000011',
              fontWeight: 'bold',
              cursor: 'pointer',
              fontSize: '1rem'
            }}>
              🚀 Get Started
            </button>

            <button style={{
              background: 'transparent',
              border: '2px solid #00ffff',
              borderRadius: '6px',
              padding: '10px 22px',
              color: '#00ffff',
              fontWeight: 'bold',
              cursor: 'pointer',
              fontSize: '1rem'
            }}>
              📚 Documentation
            </button>
          </div>

          <div style={{
            marginTop: '2rem',
            fontSize: '0.9rem',
            color: '#888888'
          }}>
            Built with ❤️ by KangPCode • Powered by Bun + TypeScript
          </div>
        </div>
      </div>
    </BrowserRouter>
  );
}

/**
 * 🎨 Theme Switcher Component
 */
interface ThemeSwitcherProps {
  currentTheme: string;
  onThemeChange: (theme: string) => void;
}

function ThemeSwitcher({ currentTheme, onThemeChange }: ThemeSwitcherProps) {
  const themes = [
    { name: 'cyberpunk', icon: '🌆', label: 'Cyberpunk' },
    { name: 'nusantara', icon: '🏝️', label: 'Nusantara' },
    { name: 'minimalist', icon: '⚪', label: 'Minimalist' },
    { name: 'retro', icon: '📼', label: 'Retro' },
    { name: 'aurora', icon: '🌌', label: 'Aurora' }
  ];

  return (
    <div className="k-bg-surface k-rounded-lg k-p-2 k-shadow-lg k-backdrop-blur">
      <div className="k-text-xs k-text-muted k-mb-2 k-text-center">Theme</div>
      <div className="k-flex k-gap-1">
        {themes.map((theme) => (
          <button
            key={theme.name}
            onClick={() => onThemeChange(theme.name)}
            className={`
              k-w-8 k-h-8 k-rounded k-flex k-items-center k-justify-center
              k-transition-all k-duration-200 k-text-sm
              ${currentTheme === theme.name 
                ? 'k-bg-primary k-text-background k-shadow-glow' 
                : 'k-bg-transparent hover:k-bg-primary/20'
              }
            `}
            title={theme.label}
          >
            {theme.icon}
          </button>
        ))}
      </div>
    </div>
  );
}

export default App;
