import React, { Suspense } from 'react';
import { Routes, Route, BrowserRouter } from 'react-router-dom';
// import { KilatScene } from 'kilatanim.js';
// import { useKilat, useTheme, usePlatform } from 'kilat-core';
import { ErrorBoundary } from './components/ErrorBoundary';
import { LoadingSpinner } from './components/LoadingSpinner';
import { Navigation } from './components/Navigation';
import { Footer } from './components/Footer';

// Import styles
import './styles/navigation.css';
import './styles/components.css';
import './styles/demo.css';
import './styles/playground.css';
import './styles/pages.css';

// Lazy load pages for better performance
const HomePage = React.lazy(() => import('./pages/HomePage'));
const AboutPage = React.lazy(() => import('./pages/AboutPage'));
const DemoPage = React.lazy(() => import('./pages/DemoPage'));
const DocsPage = React.lazy(() => import('./pages/DocsPage'));
const PlaygroundPage = React.lazy(() => import('./pages/PlaygroundPage'));
const NotFoundPage = React.lazy(() => import('./pages/NotFoundPage'));

/**
 * 🚀 Main App Component
 * Kilat.js Web Demo Application
 */
function App() {
  // const { config, plugins } = useKilat();
  // const { theme, setTheme } = useTheme();
  // const platform = usePlatform();

  // Mock config for now
  const config = {
    animation: {
      presetScene: 'galaxy',
      autoRotate: true,
      background: true
    }
  };

  return (
    <BrowserRouter>
      <ErrorBoundary>
        <div className="k-app k-min-h-screen k-bg-background k-text-text">
        {/* 🌌 Background Animation */}
        <div className="k-fixed k-inset-0 k-z-0">
          {/* <KilatScene
            preset={config.animation?.presetScene || 'galaxy'}
            autoRotate={config.animation?.autoRotate}
            background={config.animation?.background}
            className="k-w-full k-h-full"
          /> */}
        </div>

        {/* 📱 App Content */}
        <div className="k-relative k-z-10 k-flex k-flex-col k-min-h-screen">
          {/* 🧭 Navigation */}
          <Navigation />

          {/* 📄 Main Content */}
          <main className="k-flex-1 k-container k-mx-auto k-px-4 k-py-8">
            <Suspense fallback={<LoadingSpinner />}>
              <Routes>
                <Route path="/" element={<HomePage />} />
                <Route path="/about" element={<AboutPage />} />
                <Route path="/demo" element={<DemoPage />} />
                <Route path="/docs" element={<DocsPage />} />
                <Route path="/playground" element={<PlaygroundPage />} />
                <Route path="*" element={<NotFoundPage />} />
              </Routes>
            </Suspense>
          </main>

          {/* 🦶 Footer */}
          <Footer />
        </div>

        {/* 🎨 Theme Switcher */}
        {/* <div className="k-fixed k-bottom-4 k-right-4 k-z-50">
          <ThemeSwitcher currentTheme={theme} onThemeChange={setTheme} />
        </div> */}

        {/* 🐛 Debug Info (Development Only) */}
        {/* {import.meta.env.DEV && (
          <div className="k-fixed k-top-4 k-left-4 k-z-50 k-bg-surface k-p-2 k-rounded k-text-xs k-opacity-50 hover:k-opacity-100 k-transition-opacity">
            <div>Platform: {platform}</div>
            <div>Theme: {theme}</div>
            <div>Plugins: {plugins.length}</div>
          </div>
        )} */}
      </div>
    </ErrorBoundary>
  );
}

/**
 * 🎨 Theme Switcher Component
 */
interface ThemeSwitcherProps {
  currentTheme: string;
  onThemeChange: (theme: string) => void;
}

function ThemeSwitcher({ currentTheme, onThemeChange }: ThemeSwitcherProps) {
  const themes = [
    { name: 'cyberpunk', icon: '🌆', label: 'Cyberpunk' },
    { name: 'nusantara', icon: '🏝️', label: 'Nusantara' },
    { name: 'minimalist', icon: '⚪', label: 'Minimalist' },
    { name: 'retro', icon: '📼', label: 'Retro' },
    { name: 'aurora', icon: '🌌', label: 'Aurora' }
  ];

  return (
    <div className="k-bg-surface k-rounded-lg k-p-2 k-shadow-lg k-backdrop-blur">
      <div className="k-text-xs k-text-muted k-mb-2 k-text-center">Theme</div>
      <div className="k-flex k-gap-1">
        {themes.map((theme) => (
          <button
            key={theme.name}
            onClick={() => onThemeChange(theme.name)}
            className={`
              k-w-8 k-h-8 k-rounded k-flex k-items-center k-justify-center
              k-transition-all k-duration-200 k-text-sm
              ${currentTheme === theme.name 
                ? 'k-bg-primary k-text-background k-shadow-glow' 
                : 'k-bg-transparent hover:k-bg-primary/20'
              }
            `}
            title={theme.label}
          >
            {theme.icon}
          </button>
        ))}
      </div>
    </div>
  );
}

export default App;
