// 🌌 KilatAnim.js - 3D Animation Presets for Kilat.js Framework
// Export all types
export * from './types';

// Export main component
export { KilatScene } from './components/KilatScene';

// Export all presets
export { GalaxyPreset } from './presets/Galaxy';
export { MatrixPreset } from './presets/Matrix';
export { NeonTunnelPreset } from './presets/NeonTunnel';
export { CyberwavePreset } from './presets/Cyberwave';
export { GlowParticlesPreset } from './presets/GlowParticles';
export { HologramPreset } from './presets/Hologram';
export { PlasmaPreset } from './presets/Plasma';
export { NusantaraPreset } from './presets/Nusantara';

// 🎯 Preset factory registry
import { GalaxyPreset } from './presets/Galaxy';
import { MatrixPreset } from './presets/Matrix';
import { NeonTunnelPreset } from './presets/NeonTunnel';
import { CyberwavePreset } from './presets/Cyberwave';
import { GlowParticlesPreset } from './presets/GlowParticles';
import type { KilatAnimationPreset, PresetFactory } from './types';

export const PRESET_REGISTRY: Record<KilatAnimationPreset, PresetFactory> = {
  galaxy: GalaxyPreset,
  matrix: MatrixPreset,
  neonTunnel: NeonTunnelPreset,
  cyberwave: CyberwavePreset,
  glowParticles: GlowParticlesPreset
};

// 🎨 Available presets
export const AVAILABLE_PRESETS: KilatAnimationPreset[] = [
  'galaxy',
  'matrix', 
  'neonTunnel',
  'cyberwave',
  'glowParticles'
];

// 🔧 Utility functions
export function getPreset(name: KilatAnimationPreset): PresetFactory {
  const preset = PRESET_REGISTRY[name];
  if (!preset) {
    console.warn(`Preset "${name}" not found, falling back to galaxy`);
    return PRESET_REGISTRY.galaxy;
  }
  return preset;
}

export function isValidPreset(name: string): name is KilatAnimationPreset {
  return AVAILABLE_PRESETS.includes(name as KilatAnimationPreset);
}

// 🌟 Default configurations for each preset
export const DEFAULT_CONFIGS = {
  galaxy: {
    starCount: 10000,
    galaxyRadius: 15,
    spiralArms: 4,
    coreSize: 2,
    colors: ['#ffffff', '#ffaa00', '#ff6600', '#0099ff', '#aa00ff']
  },
  matrix: {
    columns: 50,
    fallSpeed: 2,
    characters: 'アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ',
    fontSize: 0.5,
    trailLength: 20,
    colors: ['#00ff41', '#008f11', '#004400']
  },
  neonTunnel: {
    tunnelLength: 100,
    ringCount: 50,
    ringRadius: 5,
    glowIntensity: 1,
    pulseBeat: 1,
    colors: ['#00ffff', '#ff00ff', '#ffff00', '#00ff00']
  },
  cyberwave: {
    waveHeight: 2,
    waveFrequency: 0.5,
    gridSize: 50,
    wireframe: true,
    glitchEffect: true,
    colors: ['#ff00ff', '#00ffff', '#ffff00']
  },
  glowParticles: {
    particleCount: 500,
    particleSize: 0.1,
    floatSpeed: 1,
    glowRadius: 2,
    connectionDistance: 3,
    showConnections: true,
    colors: ['#00ffff', '#ff00ff', '#ffff00', '#00ff00', '#ff6600']
  },
  hologram: {
    gridSize: 20,
    scanLineSpeed: 2,
    glitchIntensity: 0.1,
    hologramColor: '#00ffff',
    opacity: 0.7,
    wireframe: true,
    scanLines: true,
    glitchEffect: true
  },
  plasma: {
    intensity: 1.0,
    speed: 1.0,
    colors: ['#ff0080', '#8000ff', '#0080ff', '#00ff80'],
    resolution: 128,
    turbulence: 0.5,
    electricArcs: true,
    pulseEffect: true
  }
};

// 🎮 Performance monitoring utilities
export function getPerformanceMetrics() {
  return {
    fps: 0, // Will be updated by the scene
    memory: (performance as any).memory?.usedJSHeapSize || 0,
    timestamp: performance.now()
  };
}

// 🔍 WebGL capability detection
export function checkWebGLSupport(): boolean {
  try {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    return !!gl;
  } catch (e) {
    return false;
  }
}

export function getWebGLInfo() {
  if (!checkWebGLSupport()) {
    return null;
  }

  const canvas = document.createElement('canvas');
  const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
  
  if (!gl) return null;

  return {
    vendor: gl.getParameter(gl.VENDOR),
    renderer: gl.getParameter(gl.RENDERER),
    version: gl.getParameter(gl.VERSION),
    shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION),
    maxTextureSize: gl.getParameter(gl.MAX_TEXTURE_SIZE),
    maxVertexAttribs: gl.getParameter(gl.MAX_VERTEX_ATTRIBS),
    maxFragmentUniforms: gl.getParameter(gl.MAX_FRAGMENT_UNIFORM_VECTORS)
  };
}

// 🎵 Audio utilities (for future ambient sound support)
export function loadAudioForPreset(preset: KilatAnimationPreset): Promise<AudioBuffer | null> {
  // TODO: Implement audio loading for each preset
  return Promise.resolve(null);
}

// 📊 Version info
export const KILATANIM_VERSION = '1.0.0';

// 🎯 Main export - convenient access to everything
export default {
  KilatScene,
  presets: PRESET_REGISTRY,
  availablePresets: AVAILABLE_PRESETS,
  defaultConfigs: DEFAULT_CONFIGS,
  getPreset,
  isValidPreset,
  checkWebGLSupport,
  getWebGLInfo,
  getPerformanceMetrics,
  version: KILATANIM_VERSION
};
