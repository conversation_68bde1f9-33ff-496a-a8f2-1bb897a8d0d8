/**
 * 🧭 Kilat.js Router - Simple and powerful routing
 */

// Re-export React Router components for convenience
export {
  BrowserRouter,
  Routes,
  Route,
  Link,
  Navigate,
  Outlet,
  useNavigate,
  useLocation,
  useParams,
  useSearchParams
} from 'react-router-dom';

// Export Kilat-specific components (simple versions)
export { <PERSON><PERSON>Route } from './components/KilatRoute.js';
export { KilatLink } from './components/KilatLink.js';
export { KilatOutlet } from './components/KilatOutlet.js';
export { NestedLayout } from './components/NestedLayout.js';

// Version info
export const KILAT_ROUTER_VERSION = '1.0.0';
