 /**
 * KilatCSS ⚡ - Utility-first CSS Framework with Glow Effects
 * Framework Glow Futuristik Nusantara
 */

/* 📦 Import Utilities */

/**
 * KilatCSS Spacing Utilities
 * Padding, Margin, Gap utilities with k- prefix
 */

/* 📦 Padding Utilities */

.k-p-0 { padding: 0; }

.k-p-1 { padding: 0.25rem; padding: var(--k-space-1); }

.k-p-2 { padding: 0.5rem; padding: var(--k-space-2); }

.k-p-3 { padding: 0.75rem; padding: var(--k-space-3); }

.k-p-4 { padding: 1rem; padding: var(--k-space-4); }

.k-p-5 { padding: 1.25rem; padding: var(--k-space-5); }

.k-p-6 { padding: 1.5rem; padding: var(--k-space-6); }

.k-p-8 { padding: 2rem; padding: var(--k-space-8); }

.k-p-10 { padding: 2.5rem; padding: var(--k-space-10); }

.k-p-12 { padding: 3rem; padding: var(--k-space-12); }

.k-p-16 { padding: 4rem; padding: var(--k-space-16); }

.k-p-20 { padding: 5rem; padding: var(--k-space-20); }

.k-p-24 { padding: 6rem; padding: var(--k-space-24); }

/* 📦 Padding X (Horizontal) */

.k-px-0 { padding-left: 0; padding-right: 0; }

.k-px-1 { padding-left: 0.25rem; padding-left: var(--k-space-1); padding-right: 0.25rem; padding-right: var(--k-space-1); }

.k-px-2 { padding-left: 0.5rem; padding-left: var(--k-space-2); padding-right: 0.5rem; padding-right: var(--k-space-2); }

.k-px-3 { padding-left: 0.75rem; padding-left: var(--k-space-3); padding-right: 0.75rem; padding-right: var(--k-space-3); }

.k-px-4 { padding-left: 1rem; padding-left: var(--k-space-4); padding-right: 1rem; padding-right: var(--k-space-4); }

.k-px-5 { padding-left: 1.25rem; padding-left: var(--k-space-5); padding-right: 1.25rem; padding-right: var(--k-space-5); }

.k-px-6 { padding-left: 1.5rem; padding-left: var(--k-space-6); padding-right: 1.5rem; padding-right: var(--k-space-6); }

.k-px-8 { padding-left: 2rem; padding-left: var(--k-space-8); padding-right: 2rem; padding-right: var(--k-space-8); }

/* 📦 Padding Y (Vertical) */

.k-py-0 { padding-top: 0; padding-bottom: 0; }

.k-py-1 { padding-top: 0.25rem; padding-top: var(--k-space-1); padding-bottom: 0.25rem; padding-bottom: var(--k-space-1); }

.k-py-2 { padding-top: 0.5rem; padding-top: var(--k-space-2); padding-bottom: 0.5rem; padding-bottom: var(--k-space-2); }

.k-py-3 { padding-top: 0.75rem; padding-top: var(--k-space-3); padding-bottom: 0.75rem; padding-bottom: var(--k-space-3); }

.k-py-4 { padding-top: 1rem; padding-top: var(--k-space-4); padding-bottom: 1rem; padding-bottom: var(--k-space-4); }

.k-py-5 { padding-top: 1.25rem; padding-top: var(--k-space-5); padding-bottom: 1.25rem; padding-bottom: var(--k-space-5); }

.k-py-6 { padding-top: 1.5rem; padding-top: var(--k-space-6); padding-bottom: 1.5rem; padding-bottom: var(--k-space-6); }

.k-py-8 { padding-top: 2rem; padding-top: var(--k-space-8); padding-bottom: 2rem; padding-bottom: var(--k-space-8); }

/* 📦 Individual Padding */

.k-pt-0 { padding-top: 0; }

.k-pt-1 { padding-top: 0.25rem; padding-top: var(--k-space-1); }

.k-pt-2 { padding-top: 0.5rem; padding-top: var(--k-space-2); }

.k-pt-3 { padding-top: 0.75rem; padding-top: var(--k-space-3); }

.k-pt-4 { padding-top: 1rem; padding-top: var(--k-space-4); }

.k-pt-6 { padding-top: 1.5rem; padding-top: var(--k-space-6); }

.k-pt-8 { padding-top: 2rem; padding-top: var(--k-space-8); }

.k-pr-0 { padding-right: 0; }

.k-pr-1 { padding-right: 0.25rem; padding-right: var(--k-space-1); }

.k-pr-2 { padding-right: 0.5rem; padding-right: var(--k-space-2); }

.k-pr-3 { padding-right: 0.75rem; padding-right: var(--k-space-3); }

.k-pr-4 { padding-right: 1rem; padding-right: var(--k-space-4); }

.k-pr-6 { padding-right: 1.5rem; padding-right: var(--k-space-6); }

.k-pr-8 { padding-right: 2rem; padding-right: var(--k-space-8); }

.k-pb-0 { padding-bottom: 0; }

.k-pb-1 { padding-bottom: 0.25rem; padding-bottom: var(--k-space-1); }

.k-pb-2 { padding-bottom: 0.5rem; padding-bottom: var(--k-space-2); }

.k-pb-3 { padding-bottom: 0.75rem; padding-bottom: var(--k-space-3); }

.k-pb-4 { padding-bottom: 1rem; padding-bottom: var(--k-space-4); }

.k-pb-6 { padding-bottom: 1.5rem; padding-bottom: var(--k-space-6); }

.k-pb-8 { padding-bottom: 2rem; padding-bottom: var(--k-space-8); }

.k-pl-0 { padding-left: 0; }

.k-pl-1 { padding-left: 0.25rem; padding-left: var(--k-space-1); }

.k-pl-2 { padding-left: 0.5rem; padding-left: var(--k-space-2); }

.k-pl-3 { padding-left: 0.75rem; padding-left: var(--k-space-3); }

.k-pl-4 { padding-left: 1rem; padding-left: var(--k-space-4); }

.k-pl-6 { padding-left: 1.5rem; padding-left: var(--k-space-6); }

.k-pl-8 { padding-left: 2rem; padding-left: var(--k-space-8); }

/* 📏 Margin Utilities */

.k-m-0 { margin: 0; }

.k-m-1 { margin: 0.25rem; margin: var(--k-space-1); }

.k-m-2 { margin: 0.5rem; margin: var(--k-space-2); }

.k-m-3 { margin: 0.75rem; margin: var(--k-space-3); }

.k-m-4 { margin: 1rem; margin: var(--k-space-4); }

.k-m-5 { margin: 1.25rem; margin: var(--k-space-5); }

.k-m-6 { margin: 1.5rem; margin: var(--k-space-6); }

.k-m-8 { margin: 2rem; margin: var(--k-space-8); }

.k-m-10 { margin: 2.5rem; margin: var(--k-space-10); }

.k-m-12 { margin: 3rem; margin: var(--k-space-12); }

.k-m-16 { margin: 4rem; margin: var(--k-space-16); }

.k-m-20 { margin: 5rem; margin: var(--k-space-20); }

.k-m-24 { margin: 6rem; margin: var(--k-space-24); }

.k-m-auto { margin: auto; }

/* 📏 Margin X (Horizontal) */

.k-mx-0 { margin-left: 0; margin-right: 0; }

.k-mx-1 { margin-left: 0.25rem; margin-left: var(--k-space-1); margin-right: 0.25rem; margin-right: var(--k-space-1); }

.k-mx-2 { margin-left: 0.5rem; margin-left: var(--k-space-2); margin-right: 0.5rem; margin-right: var(--k-space-2); }

.k-mx-3 { margin-left: 0.75rem; margin-left: var(--k-space-3); margin-right: 0.75rem; margin-right: var(--k-space-3); }

.k-mx-4 { margin-left: 1rem; margin-left: var(--k-space-4); margin-right: 1rem; margin-right: var(--k-space-4); }

.k-mx-5 { margin-left: 1.25rem; margin-left: var(--k-space-5); margin-right: 1.25rem; margin-right: var(--k-space-5); }

.k-mx-6 { margin-left: 1.5rem; margin-left: var(--k-space-6); margin-right: 1.5rem; margin-right: var(--k-space-6); }

.k-mx-8 { margin-left: 2rem; margin-left: var(--k-space-8); margin-right: 2rem; margin-right: var(--k-space-8); }

.k-mx-auto { margin-left: auto; margin-right: auto; }

/* 📏 Margin Y (Vertical) */

.k-my-0 { margin-top: 0; margin-bottom: 0; }

.k-my-1 { margin-top: 0.25rem; margin-top: var(--k-space-1); margin-bottom: 0.25rem; margin-bottom: var(--k-space-1); }

.k-my-2 { margin-top: 0.5rem; margin-top: var(--k-space-2); margin-bottom: 0.5rem; margin-bottom: var(--k-space-2); }

.k-my-3 { margin-top: 0.75rem; margin-top: var(--k-space-3); margin-bottom: 0.75rem; margin-bottom: var(--k-space-3); }

.k-my-4 { margin-top: 1rem; margin-top: var(--k-space-4); margin-bottom: 1rem; margin-bottom: var(--k-space-4); }

.k-my-5 { margin-top: 1.25rem; margin-top: var(--k-space-5); margin-bottom: 1.25rem; margin-bottom: var(--k-space-5); }

.k-my-6 { margin-top: 1.5rem; margin-top: var(--k-space-6); margin-bottom: 1.5rem; margin-bottom: var(--k-space-6); }

.k-my-8 { margin-top: 2rem; margin-top: var(--k-space-8); margin-bottom: 2rem; margin-bottom: var(--k-space-8); }

/* 📏 Individual Margin */

.k-mt-0 { margin-top: 0; }

.k-mt-1 { margin-top: 0.25rem; margin-top: var(--k-space-1); }

.k-mt-2 { margin-top: 0.5rem; margin-top: var(--k-space-2); }

.k-mt-3 { margin-top: 0.75rem; margin-top: var(--k-space-3); }

.k-mt-4 { margin-top: 1rem; margin-top: var(--k-space-4); }

.k-mt-6 { margin-top: 1.5rem; margin-top: var(--k-space-6); }

.k-mt-8 { margin-top: 2rem; margin-top: var(--k-space-8); }

.k-mt-10 { margin-top: 2.5rem; margin-top: var(--k-space-10); }

.k-mt-12 { margin-top: 3rem; margin-top: var(--k-space-12); }

.k-mt-16 { margin-top: 4rem; margin-top: var(--k-space-16); }

.k-mt-20 { margin-top: 5rem; margin-top: var(--k-space-20); }

.k-mt-24 { margin-top: 6rem; margin-top: var(--k-space-24); }

.k-mr-0 { margin-right: 0; }

.k-mr-1 { margin-right: 0.25rem; margin-right: var(--k-space-1); }

.k-mr-2 { margin-right: 0.5rem; margin-right: var(--k-space-2); }

.k-mr-3 { margin-right: 0.75rem; margin-right: var(--k-space-3); }

.k-mr-4 { margin-right: 1rem; margin-right: var(--k-space-4); }

.k-mr-6 { margin-right: 1.5rem; margin-right: var(--k-space-6); }

.k-mr-8 { margin-right: 2rem; margin-right: var(--k-space-8); }

.k-mb-0 { margin-bottom: 0; }

.k-mb-1 { margin-bottom: 0.25rem; margin-bottom: var(--k-space-1); }

.k-mb-2 { margin-bottom: 0.5rem; margin-bottom: var(--k-space-2); }

.k-mb-3 { margin-bottom: 0.75rem; margin-bottom: var(--k-space-3); }

.k-mb-4 { margin-bottom: 1rem; margin-bottom: var(--k-space-4); }

.k-mb-6 { margin-bottom: 1.5rem; margin-bottom: var(--k-space-6); }

.k-mb-8 { margin-bottom: 2rem; margin-bottom: var(--k-space-8); }

.k-mb-10 { margin-bottom: 2.5rem; margin-bottom: var(--k-space-10); }

.k-mb-12 { margin-bottom: 3rem; margin-bottom: var(--k-space-12); }

.k-mb-16 { margin-bottom: 4rem; margin-bottom: var(--k-space-16); }

.k-mb-20 { margin-bottom: 5rem; margin-bottom: var(--k-space-20); }

.k-mb-24 { margin-bottom: 6rem; margin-bottom: var(--k-space-24); }

.k-ml-0 { margin-left: 0; }

.k-ml-1 { margin-left: 0.25rem; margin-left: var(--k-space-1); }

.k-ml-2 { margin-left: 0.5rem; margin-left: var(--k-space-2); }

.k-ml-3 { margin-left: 0.75rem; margin-left: var(--k-space-3); }

.k-ml-4 { margin-left: 1rem; margin-left: var(--k-space-4); }

.k-ml-6 { margin-left: 1.5rem; margin-left: var(--k-space-6); }

.k-ml-8 { margin-left: 2rem; margin-left: var(--k-space-8); }

/* 🔗 Gap Utilities (for Flexbox/Grid) */

.k-gap-0 { gap: 0; }

.k-gap-1 { gap: 0.25rem; gap: var(--k-space-1); }

.k-gap-2 { gap: 0.5rem; gap: var(--k-space-2); }

.k-gap-3 { gap: 0.75rem; gap: var(--k-space-3); }

.k-gap-4 { gap: 1rem; gap: var(--k-space-4); }

.k-gap-5 { gap: 1.25rem; gap: var(--k-space-5); }

.k-gap-6 { gap: 1.5rem; gap: var(--k-space-6); }

.k-gap-8 { gap: 2rem; gap: var(--k-space-8); }

.k-gap-10 { gap: 2.5rem; gap: var(--k-space-10); }

.k-gap-12 { gap: 3rem; gap: var(--k-space-12); }

.k-gap-x-0 { -moz-column-gap: 0; column-gap: 0; }

.k-gap-x-1 { -moz-column-gap: 0.25rem; column-gap: 0.25rem; -moz-column-gap: var(--k-space-1); column-gap: var(--k-space-1); }

.k-gap-x-2 { -moz-column-gap: 0.5rem; column-gap: 0.5rem; -moz-column-gap: var(--k-space-2); column-gap: var(--k-space-2); }

.k-gap-x-3 { -moz-column-gap: 0.75rem; column-gap: 0.75rem; -moz-column-gap: var(--k-space-3); column-gap: var(--k-space-3); }

.k-gap-x-4 { -moz-column-gap: 1rem; column-gap: 1rem; -moz-column-gap: var(--k-space-4); column-gap: var(--k-space-4); }

.k-gap-x-6 { -moz-column-gap: 1.5rem; column-gap: 1.5rem; -moz-column-gap: var(--k-space-6); column-gap: var(--k-space-6); }

.k-gap-x-8 { -moz-column-gap: 2rem; column-gap: 2rem; -moz-column-gap: var(--k-space-8); column-gap: var(--k-space-8); }

.k-gap-y-0 { row-gap: 0; }

.k-gap-y-1 { row-gap: 0.25rem; row-gap: var(--k-space-1); }

.k-gap-y-2 { row-gap: 0.5rem; row-gap: var(--k-space-2); }

.k-gap-y-3 { row-gap: 0.75rem; row-gap: var(--k-space-3); }

.k-gap-y-4 { row-gap: 1rem; row-gap: var(--k-space-4); }

.k-gap-y-6 { row-gap: 1.5rem; row-gap: var(--k-space-6); }

.k-gap-y-8 { row-gap: 2rem; row-gap: var(--k-space-8); }

/**
 * KilatCSS Color Utilities
 * Background, Text, Border colors with glow effects
 */

/* 🎨 Background Colors */

.k-bg-transparent { background-color: transparent; }

.k-bg-current { background-color: currentColor; }

/* Base Colors */

.k-bg-black { background-color: #000000; }

.k-bg-white { background-color: #ffffff; }

.k-bg-gray-50 { background-color: #f9fafb; }

.k-bg-gray-100 { background-color: #f3f4f6; }

.k-bg-gray-200 { background-color: #e5e7eb; }

.k-bg-gray-300 { background-color: #d1d5db; }

.k-bg-gray-400 { background-color: #9ca3af; }

.k-bg-gray-500 { background-color: #6b7280; }

.k-bg-gray-600 { background-color: #4b5563; }

.k-bg-gray-700 { background-color: #374151; }

.k-bg-gray-800 { background-color: #1f2937; }

.k-bg-gray-900 { background-color: #111827; }

/* Theme Colors */

.k-bg-primary { background-color: #00ffff; background-color: var(--k-primary); }

.k-bg-secondary { background-color: #ff00ff; background-color: var(--k-secondary); }

.k-bg-accent { background-color: #ffff00; background-color: var(--k-accent); }

.k-bg-background { background-color: #000000; background-color: var(--k-background); }

.k-bg-surface { background-color: #111111; background-color: var(--k-surface); }

/* Neon Colors */

.k-bg-neon-blue { background-color: #00ffff; background-color: var(--k-neon-blue); }

.k-bg-neon-pink { background-color: #ff00ff; background-color: var(--k-neon-pink); }

.k-bg-neon-green { background-color: #00ff00; background-color: var(--k-neon-green); }

.k-bg-neon-yellow { background-color: #ffff00; background-color: var(--k-neon-yellow); }

.k-bg-neon-purple { background-color: #8000ff; background-color: var(--k-neon-purple); }

.k-bg-neon-orange { background-color: #ff8000; background-color: var(--k-neon-orange); }

.k-bg-neon-red { background-color: #ff0040; background-color: var(--k-neon-red); }

/* Semantic Colors */

.k-bg-success { background-color: #10b981; }

.k-bg-warning { background-color: #f59e0b; }

.k-bg-error { background-color: #ef4444; }

.k-bg-info { background-color: #3b82f6; }

/* Dark/Light Mode Adaptive */

.k-bg-dark { background-color: #000000; background-color: var(--k-background); }

.k-bg-light { background-color: #111111; background-color: var(--k-surface); }

/* 🔤 Text Colors */

.k-text-transparent { color: transparent; }

.k-text-current { color: currentColor; }

/* Base Text Colors */

.k-text-black { color: #000000; }

.k-text-white { color: #ffffff; }

.k-text-gray-50 { color: #f9fafb; }

.k-text-gray-100 { color: #f3f4f6; }

.k-text-gray-200 { color: #e5e7eb; }

.k-text-gray-300 { color: #d1d5db; }

.k-text-gray-400 { color: #9ca3af; }

.k-text-gray-500 { color: #6b7280; }

.k-text-gray-600 { color: #4b5563; }

.k-text-gray-700 { color: #374151; }

.k-text-gray-800 { color: #1f2937; }

.k-text-gray-900 { color: #111827; }

/* Theme Text Colors */

.k-text-primary { color: #00ffff; color: var(--k-primary); }

.k-text-secondary { color: #ff00ff; color: var(--k-secondary); }

.k-text-accent { color: #ffff00; color: var(--k-accent); }

.k-text-text { color: #ffffff; color: var(--k-text); }

.k-text-muted { color: #888888; color: var(--k-text-muted); }

/* Neon Text Colors */

.k-text-neon-blue { color: #00ffff; color: var(--k-neon-blue); }

.k-text-neon-pink { color: #ff00ff; color: var(--k-neon-pink); }

.k-text-neon-green { color: #00ff00; color: var(--k-neon-green); }

.k-text-neon-yellow { color: #ffff00; color: var(--k-neon-yellow); }

.k-text-neon-purple { color: #8000ff; color: var(--k-neon-purple); }

.k-text-neon-orange { color: #ff8000; color: var(--k-neon-orange); }

.k-text-neon-red { color: #ff0040; color: var(--k-neon-red); }

/* Semantic Text Colors */

.k-text-success { color: #10b981; }

.k-text-warning { color: #f59e0b; }

.k-text-error { color: #ef4444; }

.k-text-info { color: #3b82f6; }

/* Dark/Light Mode Adaptive */

.k-text-dark { color: #ffffff; color: var(--k-text); }

.k-text-light { color: #888888; color: var(--k-text-muted); }

/* 🔲 Border Colors */

.k-border-transparent { border-color: transparent; }

.k-border-current { border-color: currentColor; }

/* Base Border Colors */

.k-border-black { border-color: #000000; }

.k-border-white { border-color: #ffffff; }

.k-border-gray-200 { border-color: #e5e7eb; }

.k-border-gray-300 { border-color: #d1d5db; }

.k-border-gray-400 { border-color: #9ca3af; }

.k-border-gray-500 { border-color: #6b7280; }

.k-border-gray-600 { border-color: #4b5563; }

.k-border-gray-700 { border-color: #374151; }

.k-border-gray-800 { border-color: #1f2937; }

/* Theme Border Colors */

.k-border-primary { border-color: #00ffff; border-color: var(--k-primary); }

.k-border-secondary { border-color: #ff00ff; border-color: var(--k-secondary); }

.k-border-accent { border-color: #ffff00; border-color: var(--k-accent); }

/* Neon Border Colors */

.k-border-neon-blue { border-color: #00ffff; border-color: var(--k-neon-blue); }

.k-border-neon-pink { border-color: #ff00ff; border-color: var(--k-neon-pink); }

.k-border-neon-green { border-color: #00ff00; border-color: var(--k-neon-green); }

.k-border-neon-yellow { border-color: #ffff00; border-color: var(--k-neon-yellow); }

.k-border-neon-purple { border-color: #8000ff; border-color: var(--k-neon-purple); }

.k-border-neon-orange { border-color: #ff8000; border-color: var(--k-neon-orange); }

.k-border-neon-red { border-color: #ff0040; border-color: var(--k-neon-red); }

/* Neon Border Colors with Glow */

.k-border-cyan { 
  border-color: #00ffff; 
  border-color: var(--k-neon-blue); 
  box-shadow: 0 0 10px #00ffff; 
  box-shadow: 0 0 10px var(--k-neon-blue);
}

.k-border-magenta { 
  border-color: #ff00ff; 
  border-color: var(--k-neon-pink); 
  box-shadow: 0 0 10px #ff00ff; 
  box-shadow: 0 0 10px var(--k-neon-pink);
}

.k-border-lime { 
  border-color: #00ff00; 
  border-color: var(--k-neon-green); 
  box-shadow: 0 0 10px #00ff00; 
  box-shadow: 0 0 10px var(--k-neon-green);
}

/* 🌈 Gradient Backgrounds */

.k-bg-gradient-cyber {
  background: linear-gradient(135deg, #00ffff, #ff00ff);
  background: linear-gradient(135deg, var(--k-neon-blue), var(--k-neon-pink));
}

.k-bg-gradient-neon {
  background: linear-gradient(135deg, #00ff00, #ffff00);
  background: linear-gradient(135deg, var(--k-neon-green), var(--k-neon-yellow));
}

.k-bg-gradient-sunset {
  background: linear-gradient(135deg, #ff8000, #ff0040);
  background: linear-gradient(135deg, var(--k-neon-orange), var(--k-neon-red));
}

.k-bg-gradient-aurora {
  background: linear-gradient(135deg, #8000ff, #00ffff, #00ff00);
  background: linear-gradient(135deg, var(--k-neon-purple), var(--k-neon-blue), var(--k-neon-green));
}

.k-bg-gradient-dark {
  background: linear-gradient(135deg, #000000, #1a1a1a, #333333);
}

/* 🔮 Shadow Effects */

.k-shadow-none { box-shadow: none; }

.k-shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }

.k-shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }

.k-shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }

.k-shadow-xl { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }

/* Cyber Shadow Effects */

.k-shadow-cyber {
  box-shadow: 
    0 0 20px rgba(0, 255, 255, 0.3),
    0 0 40px rgba(255, 0, 255, 0.2),
    0 4px 8px rgba(0, 0, 0, 0.3);
}

.k-shadow-neon {
  box-shadow: 
    0 0 15px currentColor,
    0 0 30px currentColor,
    0 4px 8px rgba(0, 0, 0, 0.3);
}

.k-shadow-glow {
  box-shadow: 
    0 0 10px currentColor,
    0 0 20px currentColor,
    0 0 30px currentColor;
}

/**
 * KilatCSS Typography Utilities ⚡
 * Font families, sizes, weights, and text effects
 */

/* 🔤 Font Families */

.k-font-sans { font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; font-family: var(--k-font-sans); }

.k-font-mono { font-family: 'Fira Code', 'JetBrains Mono', Consolas, monospace; font-family: var(--k-font-mono); }

.k-font-display { font-family: 'Orbitron', 'Exo 2', sans-serif; font-family: var(--k-font-display); }

/* 📏 Font Sizes */

.k-text-xs { font-size: 0.75rem; line-height: 1rem; }

.k-text-sm { font-size: 0.875rem; line-height: 1.25rem; }

.k-text-base { font-size: 1rem; line-height: 1.5rem; }

.k-text-lg { font-size: 1.125rem; line-height: 1.75rem; }

.k-text-xl { font-size: 1.25rem; line-height: 1.75rem; }

.k-text-2xl { font-size: 1.5rem; line-height: 2rem; }

.k-text-3xl { font-size: 1.875rem; line-height: 2.25rem; }

.k-text-4xl { font-size: 2.25rem; line-height: 2.5rem; }

.k-text-5xl { font-size: 3rem; line-height: 1; }

.k-text-6xl { font-size: 3.75rem; line-height: 1; }

.k-text-7xl { font-size: 4.5rem; line-height: 1; }

.k-text-8xl { font-size: 6rem; line-height: 1; }

.k-text-9xl { font-size: 8rem; line-height: 1; }

/* ⚖️ Font Weights */

.k-font-thin { font-weight: 100; }

.k-font-extralight { font-weight: 200; }

.k-font-light { font-weight: 300; }

.k-font-normal { font-weight: 400; }

.k-font-medium { font-weight: 500; }

.k-font-semibold { font-weight: 600; }

.k-font-bold { font-weight: 700; }

.k-font-extrabold { font-weight: 800; }

.k-font-black { font-weight: 900; }

/* 📐 Text Alignment */

.k-text-left { text-align: left; }

.k-text-center { text-align: center; }

.k-text-right { text-align: right; }

.k-text-justify { text-align: justify; }

/* 🎨 Text Colors */

.k-text-primary { color: #00ffff; color: var(--k-primary); }

.k-text-secondary { color: #ff00ff; color: var(--k-secondary); }

.k-text-accent { color: #ffff00; color: var(--k-accent); }

.k-text-light { color: #ffffff; color: var(--k-text); }

.k-text-muted { color: #888888; color: var(--k-text-muted); }

.k-text-white { color: #ffffff; }

.k-text-black { color: #000000; }

.k-text-gray-100 { color: #f7fafc; }

.k-text-gray-200 { color: #edf2f7; }

.k-text-gray-300 { color: #e2e8f0; }

.k-text-gray-400 { color: #cbd5e0; }

.k-text-gray-500 { color: #a0aec0; }

.k-text-gray-600 { color: #718096; }

.k-text-gray-700 { color: #4a5568; }

.k-text-gray-800 { color: #2d3748; }

.k-text-gray-900 { color: #1a202c; }

/* 🌈 Neon Text Colors */

.k-text-neon-blue { color: #00ffff; color: var(--k-neon-blue); }

.k-text-neon-pink { color: #ff00ff; color: var(--k-neon-pink); }

.k-text-neon-green { color: #00ff00; color: var(--k-neon-green); }

.k-text-neon-yellow { color: #ffff00; color: var(--k-neon-yellow); }

.k-text-neon-purple { color: #8000ff; color: var(--k-neon-purple); }

.k-text-neon-orange { color: #ff8000; color: var(--k-neon-orange); }

.k-text-neon-red { color: #ff0040; color: var(--k-neon-red); }

/* 📏 Line Height */

.k-leading-none { line-height: 1; }

.k-leading-tight { line-height: 1.25; }

.k-leading-snug { line-height: 1.375; }

.k-leading-normal { line-height: 1.5; }

.k-leading-relaxed { line-height: 1.625; }

.k-leading-loose { line-height: 2; }

/* 📝 Text Decoration */

.k-underline { text-decoration: underline; }

.k-line-through { text-decoration: line-through; }

.k-no-underline { text-decoration: none; }

/* 🔤 Text Transform */

.k-uppercase { text-transform: uppercase; }

.k-lowercase { text-transform: lowercase; }

.k-capitalize { text-transform: capitalize; }

.k-normal-case { text-transform: none; }

/* 📖 Text Overflow */

.k-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.k-text-ellipsis { text-overflow: ellipsis; }

.k-text-clip { text-overflow: clip; }

/* 🔤 White Space */

.k-whitespace-normal { white-space: normal; }

.k-whitespace-nowrap { white-space: nowrap; }

.k-whitespace-pre { white-space: pre; }

.k-whitespace-pre-line { white-space: pre-line; }

.k-whitespace-pre-wrap { white-space: pre-wrap; }

/* 📏 Letter Spacing */

.k-tracking-tighter { letter-spacing: -0.05em; }

.k-tracking-tight { letter-spacing: -0.025em; }

.k-tracking-normal { letter-spacing: 0em; }

.k-tracking-wide { letter-spacing: 0.025em; }

.k-tracking-wider { letter-spacing: 0.05em; }

.k-tracking-widest { letter-spacing: 0.1em; }

/* 🎭 Text Effects */

.k-text-shadow-sm { text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); }

.k-text-shadow { text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06); }

.k-text-shadow-md { text-shadow: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06); }

.k-text-shadow-lg { text-shadow: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05); }

.k-text-shadow-xl { text-shadow: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04); }

.k-text-shadow-none { text-shadow: none; }

/* 🔮 Cyberpunk Text Effects */

.k-text-cyber {
  font-family: 'Orbitron', 'Exo 2', sans-serif;
  font-family: var(--k-font-display);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  text-shadow: 
    0 0 5px currentColor,
    0 0 10px currentColor,
    0 0 15px currentColor;
}

.k-text-glitch {
  position: relative;
  font-family: 'Orbitron', 'Exo 2', sans-serif;
  font-family: var(--k-font-display);
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.k-text-glitch::before,
.k-text-glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.k-text-glitch::before {
  animation: k-glitch-1 0.5s infinite;
  color: #ff00ff;
  color: var(--k-neon-pink);
  z-index: -1;
}

.k-text-glitch::after {
  animation: k-glitch-2 0.5s infinite;
  color: #00ffff;
  color: var(--k-neon-blue);
  z-index: -2;
}

@keyframes k-glitch-1 {
  0%, 14%, 15%, 49%, 50%, 99%, 100% { transform: translate(0, 0); }
  15%, 49% { transform: translate(-2px, -1px); }
}

@keyframes k-glitch-2 {
  0%, 20%, 21%, 62%, 63%, 99%, 100% { transform: translate(0, 0); }
  21%, 62% { transform: translate(2px, 1px); }
}

/* 🌟 Hologram Text Effect */

.k-text-hologram {
  background: linear-gradient(
    45deg,
    #00ffff,
    #ff00ff,
    #00ff00,
    #ffff00
  );
  background: linear-gradient(
    45deg,
    var(--k-neon-blue),
    var(--k-neon-pink),
    var(--k-neon-green),
    var(--k-neon-yellow)
  );
  background-size: 400% 400%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: k-hologram 3s ease-in-out infinite;
}

@keyframes k-hologram {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* 📱 Responsive Typography */

@media (max-width: 640px) {
  .k-sm\:text-xs { font-size: 0.75rem; line-height: 1rem; }
  .k-sm\:text-sm { font-size: 0.875rem; line-height: 1.25rem; }
  .k-sm\:text-base { font-size: 1rem; line-height: 1.5rem; }
  .k-sm\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .k-sm\:text-xl { font-size: 1.25rem; line-height: 1.75rem; }
  .k-sm\:text-2xl { font-size: 1.5rem; line-height: 2rem; }
  .k-sm\:text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
}

@media (min-width: 768px) {
  .k-md\:text-xs { font-size: 0.75rem; line-height: 1rem; }
  .k-md\:text-sm { font-size: 0.875rem; line-height: 1.25rem; }
  .k-md\:text-base { font-size: 1rem; line-height: 1.5rem; }
  .k-md\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .k-md\:text-xl { font-size: 1.25rem; line-height: 1.75rem; }
  .k-md\:text-2xl { font-size: 1.5rem; line-height: 2rem; }
  .k-md\:text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
  .k-md\:text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
  .k-md\:text-5xl { font-size: 3rem; line-height: 1; }
}

@media (min-width: 1024px) {
  .k-lg\:text-xs { font-size: 0.75rem; line-height: 1rem; }
  .k-lg\:text-sm { font-size: 0.875rem; line-height: 1.25rem; }
  .k-lg\:text-base { font-size: 1rem; line-height: 1.5rem; }
  .k-lg\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .k-lg\:text-xl { font-size: 1.25rem; line-height: 1.75rem; }
  .k-lg\:text-2xl { font-size: 1.5rem; line-height: 2rem; }
  .k-lg\:text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
  .k-lg\:text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
  .k-lg\:text-5xl { font-size: 3rem; line-height: 1; }
  .k-lg\:text-6xl { font-size: 3.75rem; line-height: 1; }
}

/**
 * KilatCSS Layout Utilities ⚡
 * Display, positioning, flexbox, grid, and layout utilities
 */

/* 📦 Display */

.k-block { display: block; }

.k-inline-block { display: inline-block; }

.k-inline { display: inline; }

.k-flex { display: flex; }

.k-inline-flex { display: inline-flex; }

.k-grid { display: grid; }

.k-inline-grid { display: inline-grid; }

.k-table { display: table; }

.k-table-row { display: table-row; }

.k-table-cell { display: table-cell; }

.k-hidden { display: none; }

/* 🎯 Position */

.k-static { position: static; }

.k-fixed { position: fixed; }

.k-absolute { position: absolute; }

.k-relative { position: relative; }

.k-sticky { position: sticky; }

/* 📍 Top, Right, Bottom, Left */

.k-inset-0 { top: 0; right: 0; bottom: 0; left: 0; }

.k-inset-x-0 { left: 0; right: 0; }

.k-inset-y-0 { top: 0; bottom: 0; }

.k-top-0 { top: 0; }

.k-right-0 { right: 0; }

.k-bottom-0 { bottom: 0; }

.k-left-0 { left: 0; }

.k-top-1 { top: 0.25rem; }

.k-right-1 { right: 0.25rem; }

.k-bottom-1 { bottom: 0.25rem; }

.k-left-1 { left: 0.25rem; }

.k-top-2 { top: 0.5rem; }

.k-right-2 { right: 0.5rem; }

.k-bottom-2 { bottom: 0.5rem; }

.k-left-2 { left: 0.5rem; }

.k-top-4 { top: 1rem; }

.k-right-4 { right: 1rem; }

.k-bottom-4 { bottom: 1rem; }

.k-left-4 { left: 1rem; }

/* 🔄 Flexbox */

.k-flex-row { flex-direction: row; }

.k-flex-row-reverse { flex-direction: row-reverse; }

.k-flex-col { flex-direction: column; }

.k-flex-col-reverse { flex-direction: column-reverse; }

.k-flex-wrap { flex-wrap: wrap; }

.k-flex-wrap-reverse { flex-wrap: wrap-reverse; }

.k-flex-nowrap { flex-wrap: nowrap; }

.k-flex-1 { flex: 1 1 0%; }

.k-flex-auto { flex: 1 1 auto; }

.k-flex-initial { flex: 0 1 auto; }

.k-flex-none { flex: none; }

.k-flex-grow { flex-grow: 1; }

.k-flex-grow-0 { flex-grow: 0; }

.k-flex-shrink { flex-shrink: 1; }

.k-flex-shrink-0 { flex-shrink: 0; }

/* 🎯 Justify Content */

.k-justify-start { justify-content: flex-start; }

.k-justify-end { justify-content: flex-end; }

.k-justify-center { justify-content: center; }

.k-justify-between { justify-content: space-between; }

.k-justify-around { justify-content: space-around; }

.k-justify-evenly { justify-content: space-evenly; }

/* 🎯 Align Items */

.k-items-start { align-items: flex-start; }

.k-items-end { align-items: flex-end; }

.k-items-center { align-items: center; }

.k-items-baseline { align-items: baseline; }

.k-items-stretch { align-items: stretch; }

/* 🎯 Align Content */

.k-content-start { align-content: flex-start; }

.k-content-end { align-content: flex-end; }

.k-content-center { align-content: center; }

.k-content-between { align-content: space-between; }

.k-content-around { align-content: space-around; }

.k-content-evenly { align-content: space-evenly; }

/* 🎯 Align Self */

.k-self-auto { align-self: auto; }

.k-self-start { align-self: flex-start; }

.k-self-end { align-self: flex-end; }

.k-self-center { align-self: center; }

.k-self-stretch { align-self: stretch; }

.k-self-baseline { align-self: baseline; }

/* 🔲 Grid */

.k-grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }

.k-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }

.k-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }

.k-grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

.k-grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }

.k-grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }

.k-grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

.k-grid-rows-1 { grid-template-rows: repeat(1, minmax(0, 1fr)); }

.k-grid-rows-2 { grid-template-rows: repeat(2, minmax(0, 1fr)); }

.k-grid-rows-3 { grid-template-rows: repeat(3, minmax(0, 1fr)); }

.k-grid-rows-4 { grid-template-rows: repeat(4, minmax(0, 1fr)); }

.k-grid-rows-5 { grid-template-rows: repeat(5, minmax(0, 1fr)); }

.k-grid-rows-6 { grid-template-rows: repeat(6, minmax(0, 1fr)); }

.k-col-span-1 { grid-column: span 1 / span 1; }

.k-col-span-2 { grid-column: span 2 / span 2; }

.k-col-span-3 { grid-column: span 3 / span 3; }

.k-col-span-4 { grid-column: span 4 / span 4; }

.k-col-span-5 { grid-column: span 5 / span 5; }

.k-col-span-6 { grid-column: span 6 / span 6; }

.k-col-span-full { grid-column: 1 / -1; }

.k-row-span-1 { grid-row: span 1 / span 1; }

.k-row-span-2 { grid-row: span 2 / span 2; }

.k-row-span-3 { grid-row: span 3 / span 3; }

.k-row-span-4 { grid-row: span 4 / span 4; }

.k-row-span-5 { grid-row: span 5 / span 5; }

.k-row-span-6 { grid-row: span 6 / span 6; }

.k-row-span-full { grid-row: 1 / -1; }

.k-gap-0 { gap: 0; }

.k-gap-1 { gap: 0.25rem; }

.k-gap-2 { gap: 0.5rem; }

.k-gap-3 { gap: 0.75rem; }

.k-gap-4 { gap: 1rem; }

.k-gap-5 { gap: 1.25rem; }

.k-gap-6 { gap: 1.5rem; }

.k-gap-8 { gap: 2rem; }

/* 📏 Width */

.k-w-0 { width: 0; }

.k-w-1 { width: 0.25rem; }

.k-w-2 { width: 0.5rem; }

.k-w-3 { width: 0.75rem; }

.k-w-4 { width: 1rem; }

.k-w-5 { width: 1.25rem; }

.k-w-6 { width: 1.5rem; }

.k-w-8 { width: 2rem; }

.k-w-10 { width: 2.5rem; }

.k-w-12 { width: 3rem; }

.k-w-16 { width: 4rem; }

.k-w-20 { width: 5rem; }

.k-w-24 { width: 6rem; }

.k-w-32 { width: 8rem; }

.k-w-40 { width: 10rem; }

.k-w-48 { width: 12rem; }

.k-w-56 { width: 14rem; }

.k-w-64 { width: 16rem; }

.k-w-auto { width: auto; }

.k-w-1\/2 { width: 50%; }

.k-w-1\/3 { width: 33.333333%; }

.k-w-2\/3 { width: 66.666667%; }

.k-w-1\/4 { width: 25%; }

.k-w-2\/4 { width: 50%; }

.k-w-3\/4 { width: 75%; }

.k-w-full { width: 100%; }

.k-w-screen { width: 100vw; }

/* 📏 Height */

.k-h-0 { height: 0; }

.k-h-1 { height: 0.25rem; }

.k-h-2 { height: 0.5rem; }

.k-h-3 { height: 0.75rem; }

.k-h-4 { height: 1rem; }

.k-h-5 { height: 1.25rem; }

.k-h-6 { height: 1.5rem; }

.k-h-8 { height: 2rem; }

.k-h-10 { height: 2.5rem; }

.k-h-12 { height: 3rem; }

.k-h-16 { height: 4rem; }

.k-h-20 { height: 5rem; }

.k-h-24 { height: 6rem; }

.k-h-32 { height: 8rem; }

.k-h-40 { height: 10rem; }

.k-h-48 { height: 12rem; }

.k-h-56 { height: 14rem; }

.k-h-64 { height: 16rem; }

.k-h-auto { height: auto; }

.k-h-1\/2 { height: 50%; }

.k-h-1\/3 { height: 33.333333%; }

.k-h-2\/3 { height: 66.666667%; }

.k-h-1\/4 { height: 25%; }

.k-h-2\/4 { height: 50%; }

.k-h-3\/4 { height: 75%; }

.k-h-full { height: 100%; }

.k-h-screen { height: 100vh; }

/* 📏 Min/Max Width */

.k-min-w-0 { min-width: 0; }

.k-min-w-full { min-width: 100%; }

.k-min-w-min { min-width: -moz-min-content; min-width: min-content; }

.k-min-w-max { min-width: -moz-max-content; min-width: max-content; }

.k-max-w-0 { max-width: 0; }

.k-max-w-xs { max-width: 20rem; }

.k-max-w-sm { max-width: 24rem; }

.k-max-w-md { max-width: 28rem; }

.k-max-w-lg { max-width: 32rem; }

.k-max-w-xl { max-width: 36rem; }

.k-max-w-2xl { max-width: 42rem; }

.k-max-w-3xl { max-width: 48rem; }

.k-max-w-4xl { max-width: 56rem; }

.k-max-w-5xl { max-width: 64rem; }

.k-max-w-6xl { max-width: 72rem; }

.k-max-w-7xl { max-width: 80rem; }

.k-max-w-full { max-width: 100%; }

.k-max-w-screen-sm { max-width: 640px; }

.k-max-w-screen-md { max-width: 768px; }

.k-max-w-screen-lg { max-width: 1024px; }

.k-max-w-screen-xl { max-width: 1280px; }

/* 📏 Min/Max Height */

.k-min-h-0 { min-height: 0; }

.k-min-h-full { min-height: 100%; }

.k-min-h-screen { min-height: 100vh; }

.k-max-h-0 { max-height: 0; }

.k-max-h-full { max-height: 100%; }

.k-max-h-screen { max-height: 100vh; }

/* 🔄 Overflow */

.k-overflow-auto { overflow: auto; }

.k-overflow-hidden { overflow: hidden; }

.k-overflow-visible { overflow: visible; }

.k-overflow-scroll { overflow: scroll; }

.k-overflow-x-auto { overflow-x: auto; }

.k-overflow-y-auto { overflow-y: auto; }

.k-overflow-x-hidden { overflow-x: hidden; }

.k-overflow-y-hidden { overflow-y: hidden; }

.k-overflow-x-visible { overflow-x: visible; }

.k-overflow-y-visible { overflow-y: visible; }

.k-overflow-x-scroll { overflow-x: scroll; }

.k-overflow-y-scroll { overflow-y: scroll; }

/* 🎯 Z-Index */

.k-z-0 { z-index: 0; }

.k-z-10 { z-index: 10; }

.k-z-20 { z-index: 20; }

.k-z-30 { z-index: 30; }

.k-z-40 { z-index: 40; }

.k-z-50 { z-index: 50; }

.k-z-auto { z-index: auto; }

/* 📱 Responsive Layout */

@media (max-width: 640px) {
  .k-sm\:block { display: block; }
  .k-sm\:flex { display: flex; }
  .k-sm\:grid { display: grid; }
  .k-sm\:hidden { display: none; }
  .k-sm\:flex-col { flex-direction: column; }
  .k-sm\:flex-row { flex-direction: row; }
}

@media (min-width: 768px) {
  .k-md\:block { display: block; }
  .k-md\:flex { display: flex; }
  .k-md\:grid { display: grid; }
  .k-md\:hidden { display: none; }
  .k-md\:flex-col { flex-direction: column; }
  .k-md\:flex-row { flex-direction: row; }
}

@media (min-width: 1024px) {
  .k-lg\:block { display: block; }
  .k-lg\:flex { display: flex; }
  .k-lg\:grid { display: grid; }
  .k-lg\:hidden { display: none; }
  .k-lg\:flex-col { flex-direction: column; }
  .k-lg\:flex-row { flex-direction: row; }
}

/**
 * KilatCSS Effects Utilities ⚡
 * Shadows, borders, filters, and visual effects
 */

/* 🌫️ Box Shadow */

.k-shadow-none { box-shadow: none; }

.k-shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }

.k-shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }

.k-shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }

.k-shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }

.k-shadow-xl { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }

.k-shadow-2xl { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }

.k-shadow-inner { box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06); }

/* 🔮 Neon Shadows */

.k-shadow-neon-blue { 
  box-shadow: 
    0 0 5px #00ffff,
    0 0 10px #00ffff,
    0 0 15px #00ffff; 
  box-shadow: 
    0 0 5px var(--k-neon-blue),
    0 0 10px var(--k-neon-blue),
    0 0 15px var(--k-neon-blue);
}

.k-shadow-neon-pink { 
  box-shadow: 
    0 0 5px #ff00ff,
    0 0 10px #ff00ff,
    0 0 15px #ff00ff; 
  box-shadow: 
    0 0 5px var(--k-neon-pink),
    0 0 10px var(--k-neon-pink),
    0 0 15px var(--k-neon-pink);
}

.k-shadow-neon-green { 
  box-shadow: 
    0 0 5px #00ff00,
    0 0 10px #00ff00,
    0 0 15px #00ff00; 
  box-shadow: 
    0 0 5px var(--k-neon-green),
    0 0 10px var(--k-neon-green),
    0 0 15px var(--k-neon-green);
}

.k-shadow-neon-yellow { 
  box-shadow: 
    0 0 5px #ffff00,
    0 0 10px #ffff00,
    0 0 15px #ffff00; 
  box-shadow: 
    0 0 5px var(--k-neon-yellow),
    0 0 10px var(--k-neon-yellow),
    0 0 15px var(--k-neon-yellow);
}

.k-shadow-neon-purple { 
  box-shadow: 
    0 0 5px #8000ff,
    0 0 10px #8000ff,
    0 0 15px #8000ff; 
  box-shadow: 
    0 0 5px var(--k-neon-purple),
    0 0 10px var(--k-neon-purple),
    0 0 15px var(--k-neon-purple);
}

.k-shadow-neon-orange { 
  box-shadow: 
    0 0 5px #ff8000,
    0 0 10px #ff8000,
    0 0 15px #ff8000; 
  box-shadow: 
    0 0 5px var(--k-neon-orange),
    0 0 10px var(--k-neon-orange),
    0 0 15px var(--k-neon-orange);
}

.k-shadow-neon-red { 
  box-shadow: 
    0 0 5px #ff0040,
    0 0 10px #ff0040,
    0 0 15px #ff0040; 
  box-shadow: 
    0 0 5px var(--k-neon-red),
    0 0 10px var(--k-neon-red),
    0 0 15px var(--k-neon-red);
}

/* 🔲 Borders */

.k-border-0 { border-width: 0; }

.k-border { border-width: 1px; }

.k-border-2 { border-width: 2px; }

.k-border-4 { border-width: 4px; }

.k-border-8 { border-width: 8px; }

.k-border-t-0 { border-top-width: 0; }

.k-border-t { border-top-width: 1px; }

.k-border-t-2 { border-top-width: 2px; }

.k-border-t-4 { border-top-width: 4px; }

.k-border-r-0 { border-right-width: 0; }

.k-border-r { border-right-width: 1px; }

.k-border-r-2 { border-right-width: 2px; }

.k-border-r-4 { border-right-width: 4px; }

.k-border-b-0 { border-bottom-width: 0; }

.k-border-b { border-bottom-width: 1px; }

.k-border-b-2 { border-bottom-width: 2px; }

.k-border-b-4 { border-bottom-width: 4px; }

.k-border-l-0 { border-left-width: 0; }

.k-border-l { border-left-width: 1px; }

.k-border-l-2 { border-left-width: 2px; }

.k-border-l-4 { border-left-width: 4px; }

/* 🎨 Border Colors */

.k-border-transparent { border-color: transparent; }

.k-border-current { border-color: currentColor; }

.k-border-black { border-color: #000000; }

.k-border-white { border-color: #ffffff; }

.k-border-gray-100 { border-color: #f7fafc; }

.k-border-gray-200 { border-color: #edf2f7; }

.k-border-gray-300 { border-color: #e2e8f0; }

.k-border-gray-400 { border-color: #cbd5e0; }

.k-border-gray-500 { border-color: #a0aec0; }

.k-border-gray-600 { border-color: #718096; }

.k-border-gray-700 { border-color: #4a5568; }

.k-border-gray-800 { border-color: #2d3748; }

.k-border-gray-900 { border-color: #1a202c; }

.k-border-primary { border-color: #00ffff; border-color: var(--k-primary); }

.k-border-secondary { border-color: #ff00ff; border-color: var(--k-secondary); }

.k-border-accent { border-color: #ffff00; border-color: var(--k-accent); }

.k-border-neon-blue { border-color: #00ffff; border-color: var(--k-neon-blue); }

.k-border-neon-pink { border-color: #ff00ff; border-color: var(--k-neon-pink); }

.k-border-neon-green { border-color: #00ff00; border-color: var(--k-neon-green); }

.k-border-neon-yellow { border-color: #ffff00; border-color: var(--k-neon-yellow); }

.k-border-neon-purple { border-color: #8000ff; border-color: var(--k-neon-purple); }

.k-border-neon-orange { border-color: #ff8000; border-color: var(--k-neon-orange); }

.k-border-neon-red { border-color: #ff0040; border-color: var(--k-neon-red); }

/* 🔄 Border Style */

.k-border-solid { border-style: solid; }

.k-border-dashed { border-style: dashed; }

.k-border-dotted { border-style: dotted; }

.k-border-double { border-style: double; }

.k-border-none { border-style: none; }

/* 🌊 Border Radius */

.k-rounded-none { border-radius: 0; }

.k-rounded-sm { border-radius: 0.125rem; border-radius: var(--k-radius-sm); }

.k-rounded { border-radius: 0.375rem; border-radius: var(--k-radius-md); }

.k-rounded-md { border-radius: 0.375rem; border-radius: var(--k-radius-md); }

.k-rounded-lg { border-radius: 0.5rem; border-radius: var(--k-radius-lg); }

.k-rounded-xl { border-radius: 0.75rem; border-radius: var(--k-radius-xl); }

.k-rounded-2xl { border-radius: 1rem; }

.k-rounded-3xl { border-radius: 1.5rem; }

.k-rounded-full { border-radius: 9999px; border-radius: var(--k-radius-full); }

.k-rounded-t-none { border-top-left-radius: 0; border-top-right-radius: 0; }

.k-rounded-t-sm { border-top-left-radius: 0.125rem; border-top-left-radius: var(--k-radius-sm); border-top-right-radius: 0.125rem; border-top-right-radius: var(--k-radius-sm); }

.k-rounded-t { border-top-left-radius: 0.375rem; border-top-left-radius: var(--k-radius-md); border-top-right-radius: 0.375rem; border-top-right-radius: var(--k-radius-md); }

.k-rounded-t-lg { border-top-left-radius: 0.5rem; border-top-left-radius: var(--k-radius-lg); border-top-right-radius: 0.5rem; border-top-right-radius: var(--k-radius-lg); }

.k-rounded-r-none { border-top-right-radius: 0; border-bottom-right-radius: 0; }

.k-rounded-r-sm { border-top-right-radius: 0.125rem; border-top-right-radius: var(--k-radius-sm); border-bottom-right-radius: 0.125rem; border-bottom-right-radius: var(--k-radius-sm); }

.k-rounded-r { border-top-right-radius: 0.375rem; border-top-right-radius: var(--k-radius-md); border-bottom-right-radius: 0.375rem; border-bottom-right-radius: var(--k-radius-md); }

.k-rounded-r-lg { border-top-right-radius: 0.5rem; border-top-right-radius: var(--k-radius-lg); border-bottom-right-radius: 0.5rem; border-bottom-right-radius: var(--k-radius-lg); }

.k-rounded-b-none { border-bottom-right-radius: 0; border-bottom-left-radius: 0; }

.k-rounded-b-sm { border-bottom-right-radius: 0.125rem; border-bottom-right-radius: var(--k-radius-sm); border-bottom-left-radius: 0.125rem; border-bottom-left-radius: var(--k-radius-sm); }

.k-rounded-b { border-bottom-right-radius: 0.375rem; border-bottom-right-radius: var(--k-radius-md); border-bottom-left-radius: 0.375rem; border-bottom-left-radius: var(--k-radius-md); }

.k-rounded-b-lg { border-bottom-right-radius: 0.5rem; border-bottom-right-radius: var(--k-radius-lg); border-bottom-left-radius: 0.5rem; border-bottom-left-radius: var(--k-radius-lg); }

.k-rounded-l-none { border-top-left-radius: 0; border-bottom-left-radius: 0; }

.k-rounded-l-sm { border-top-left-radius: 0.125rem; border-top-left-radius: var(--k-radius-sm); border-bottom-left-radius: 0.125rem; border-bottom-left-radius: var(--k-radius-sm); }

.k-rounded-l { border-top-left-radius: 0.375rem; border-top-left-radius: var(--k-radius-md); border-bottom-left-radius: 0.375rem; border-bottom-left-radius: var(--k-radius-md); }

.k-rounded-l-lg { border-top-left-radius: 0.5rem; border-top-left-radius: var(--k-radius-lg); border-bottom-left-radius: 0.5rem; border-bottom-left-radius: var(--k-radius-lg); }

/* 🎭 Opacity */

.k-opacity-0 { opacity: 0; }

.k-opacity-5 { opacity: 0.05; }

.k-opacity-10 { opacity: 0.1; }

.k-opacity-20 { opacity: 0.2; }

.k-opacity-25 { opacity: 0.25; }

.k-opacity-30 { opacity: 0.3; }

.k-opacity-40 { opacity: 0.4; }

.k-opacity-50 { opacity: 0.5; }

.k-opacity-60 { opacity: 0.6; }

.k-opacity-70 { opacity: 0.7; }

.k-opacity-75 { opacity: 0.75; }

.k-opacity-80 { opacity: 0.8; }

.k-opacity-90 { opacity: 0.9; }

.k-opacity-95 { opacity: 0.95; }

.k-opacity-100 { opacity: 1; }

/* 🔍 Filters */

.k-blur-none { filter: blur(0); }

.k-blur-sm { filter: blur(4px); }

.k-blur { filter: blur(8px); }

.k-blur-md { filter: blur(12px); }

.k-blur-lg { filter: blur(16px); }

.k-blur-xl { filter: blur(24px); }

.k-blur-2xl { filter: blur(40px); }

.k-blur-3xl { filter: blur(64px); }

.k-brightness-0 { filter: brightness(0); }

.k-brightness-50 { filter: brightness(0.5); }

.k-brightness-75 { filter: brightness(0.75); }

.k-brightness-90 { filter: brightness(0.9); }

.k-brightness-95 { filter: brightness(0.95); }

.k-brightness-100 { filter: brightness(1); }

.k-brightness-105 { filter: brightness(1.05); }

.k-brightness-110 { filter: brightness(1.1); }

.k-brightness-125 { filter: brightness(1.25); }

.k-brightness-150 { filter: brightness(1.5); }

.k-brightness-200 { filter: brightness(2); }

.k-contrast-0 { filter: contrast(0); }

.k-contrast-50 { filter: contrast(0.5); }

.k-contrast-75 { filter: contrast(0.75); }

.k-contrast-100 { filter: contrast(1); }

.k-contrast-125 { filter: contrast(1.25); }

.k-contrast-150 { filter: contrast(1.5); }

.k-contrast-200 { filter: contrast(2); }

.k-grayscale-0 { filter: grayscale(0); }

.k-grayscale { filter: grayscale(100%); }

.k-invert-0 { filter: invert(0); }

.k-invert { filter: invert(100%); }

.k-saturate-0 { filter: saturate(0); }

.k-saturate-50 { filter: saturate(0.5); }

.k-saturate-100 { filter: saturate(1); }

.k-saturate-150 { filter: saturate(1.5); }

.k-saturate-200 { filter: saturate(2); }

.k-sepia-0 { filter: sepia(0); }

.k-sepia { filter: sepia(100%); }

/* 🌈 Backdrop Filters */

.k-backdrop-blur-none { backdrop-filter: blur(0); }

.k-backdrop-blur-sm { backdrop-filter: blur(4px); }

.k-backdrop-blur { backdrop-filter: blur(8px); }

.k-backdrop-blur-md { backdrop-filter: blur(12px); }

.k-backdrop-blur-lg { backdrop-filter: blur(16px); }

.k-backdrop-blur-xl { backdrop-filter: blur(24px); }

.k-backdrop-blur-2xl { backdrop-filter: blur(40px); }

.k-backdrop-blur-3xl { backdrop-filter: blur(64px); }

.k-backdrop-brightness-50 { backdrop-filter: brightness(0.5); }

.k-backdrop-brightness-75 { backdrop-filter: brightness(0.75); }

.k-backdrop-brightness-90 { backdrop-filter: brightness(0.9); }

.k-backdrop-brightness-95 { backdrop-filter: brightness(0.95); }

.k-backdrop-brightness-100 { backdrop-filter: brightness(1); }

.k-backdrop-brightness-105 { backdrop-filter: brightness(1.05); }

.k-backdrop-brightness-110 { backdrop-filter: brightness(1.1); }

.k-backdrop-brightness-125 { backdrop-filter: brightness(1.25); }

/* 🔮 Cyberpunk Effects */

.k-cyber-border {
  border: 2px solid #00ffff;
  border: 2px solid var(--k-neon-blue);
  box-shadow: 
    0 0 5px #00ffff,
    inset 0 0 5px #00ffff;
  box-shadow: 
    0 0 5px var(--k-neon-blue),
    inset 0 0 5px var(--k-neon-blue);
  position: relative;
}

.k-cyber-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #00ffff, #ff00ff, #00ff00);
  background: linear-gradient(45deg, var(--k-neon-blue), var(--k-neon-pink), var(--k-neon-green));
  z-index: -1;
  border-radius: inherit;
  opacity: 0.3;
  animation: k-cyber-glow 2s ease-in-out infinite alternate;
}

@keyframes k-cyber-glow {
  0% { opacity: 0.3; }
  100% { opacity: 0.8; }
}

.k-hologram-effect {
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 70%
  );
  background-size: 200% 200%;
  animation: k-hologram-scan 3s linear infinite;
}

@keyframes k-hologram-scan {
  0% { background-position: -200% -200%; }
  100% { background-position: 200% 200%; }
}

.k-matrix-effect {
  background: 
    radial-gradient(circle at 20% 50%, #00ff00 0%, transparent 50%),
    radial-gradient(circle at 40% 20%, #00ffff 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, #ff00ff 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, #ffff00 0%, transparent 50%);
  background: 
    radial-gradient(circle at 20% 50%, var(--k-neon-green) 0%, transparent 50%),
    radial-gradient(circle at 40% 20%, var(--k-neon-blue) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, var(--k-neon-pink) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, var(--k-neon-yellow) 0%, transparent 50%);
  background-size: 300% 300%;
  animation: k-matrix-flow 4s ease-in-out infinite;
}

@keyframes k-matrix-flow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/**
 * KilatCSS Animation Utilities ⚡
 * Transitions, transforms, and keyframe animations
 */

/* 🎭 Transitions */

.k-transition-none { transition: none; }

.k-transition-all { transition: all 300ms ease; transition: all var(--k-transition-normal); }

.k-transition { transition: color 300ms ease, background-color 300ms ease, border-color 300ms ease, text-decoration-color 300ms ease, fill 300ms ease, stroke 300ms ease, opacity 300ms ease, box-shadow 300ms ease, transform 300ms ease, filter 300ms ease, backdrop-filter 300ms ease; transition: color var(--k-transition-normal), background-color var(--k-transition-normal), border-color var(--k-transition-normal), text-decoration-color var(--k-transition-normal), fill var(--k-transition-normal), stroke var(--k-transition-normal), opacity var(--k-transition-normal), box-shadow var(--k-transition-normal), transform var(--k-transition-normal), filter var(--k-transition-normal), backdrop-filter var(--k-transition-normal); }

.k-transition-colors { transition: color 300ms ease, background-color 300ms ease, border-color 300ms ease, text-decoration-color 300ms ease, fill 300ms ease, stroke 300ms ease; transition: color var(--k-transition-normal), background-color var(--k-transition-normal), border-color var(--k-transition-normal), text-decoration-color var(--k-transition-normal), fill var(--k-transition-normal), stroke var(--k-transition-normal); }

.k-transition-opacity { transition: opacity 300ms ease; transition: opacity var(--k-transition-normal); }

.k-transition-shadow { transition: box-shadow 300ms ease; transition: box-shadow var(--k-transition-normal); }

.k-transition-transform { transition: transform 300ms ease; transition: transform var(--k-transition-normal); }

/* ⏱️ Transition Duration */

.k-duration-75 { transition-duration: 75ms; }

.k-duration-100 { transition-duration: 100ms; }

.k-duration-150 { transition-duration: 150ms; }

.k-duration-200 { transition-duration: 200ms; }

.k-duration-300 { transition-duration: 300ms; }

.k-duration-500 { transition-duration: 500ms; }

.k-duration-700 { transition-duration: 700ms; }

.k-duration-1000 { transition-duration: 1000ms; }

/* 📈 Transition Timing Function */

.k-ease-linear { transition-timing-function: linear; }

.k-ease-in { transition-timing-function: cubic-bezier(0.4, 0, 1, 1); }

.k-ease-out { transition-timing-function: cubic-bezier(0, 0, 0.2, 1); }

.k-ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }

/* ⏰ Transition Delay */

.k-delay-75 { transition-delay: 75ms; }

.k-delay-100 { transition-delay: 100ms; }

.k-delay-150 { transition-delay: 150ms; }

.k-delay-200 { transition-delay: 200ms; }

.k-delay-300 { transition-delay: 300ms; }

.k-delay-500 { transition-delay: 500ms; }

.k-delay-700 { transition-delay: 700ms; }

.k-delay-1000 { transition-delay: 1000ms; }

/* 🔄 Transforms */

.k-transform { transform: none; transform: var(--k-transform-value, none); }

.k-transform-gpu { transform: translate3d(0, 0, 0) rotate(0) skewX(0) skewY(0) scaleX(1) scaleY(1); transform: translate3d(var(--k-translate-x, 0), var(--k-translate-y, 0), 0) rotate(var(--k-rotate, 0)) skewX(var(--k-skew-x, 0)) skewY(var(--k-skew-y, 0)) scaleX(var(--k-scale-x, 1)) scaleY(var(--k-scale-y, 1)); }

.k-transform-none { transform: none; }

/* 📍 Translate */

.k-translate-x-0 { --k-translate-x: 0px; }

.k-translate-x-1 { --k-translate-x: 0.25rem; }

.k-translate-x-2 { --k-translate-x: 0.5rem; }

.k-translate-x-3 { --k-translate-x: 0.75rem; }

.k-translate-x-4 { --k-translate-x: 1rem; }

.k-translate-x-5 { --k-translate-x: 1.25rem; }

.k-translate-x-6 { --k-translate-x: 1.5rem; }

.k-translate-x-8 { --k-translate-x: 2rem; }

.k-translate-x-10 { --k-translate-x: 2.5rem; }

.k-translate-x-12 { --k-translate-x: 3rem; }

.k-translate-x-16 { --k-translate-x: 4rem; }

.k-translate-x-20 { --k-translate-x: 5rem; }

.k-translate-x-24 { --k-translate-x: 6rem; }

.k-translate-x-32 { --k-translate-x: 8rem; }

.k-translate-x-40 { --k-translate-x: 10rem; }

.k-translate-x-48 { --k-translate-x: 12rem; }

.k-translate-x-56 { --k-translate-x: 14rem; }

.k-translate-x-64 { --k-translate-x: 16rem; }

.k-translate-x-1\/2 { --k-translate-x: 50%; }

.k-translate-x-1\/3 { --k-translate-x: 33.333333%; }

.k-translate-x-2\/3 { --k-translate-x: 66.666667%; }

.k-translate-x-1\/4 { --k-translate-x: 25%; }

.k-translate-x-2\/4 { --k-translate-x: 50%; }

.k-translate-x-3\/4 { --k-translate-x: 75%; }

.k-translate-x-full { --k-translate-x: 100%; }

.k-translate-y-0 { --k-translate-y: 0px; }

.k-translate-y-1 { --k-translate-y: 0.25rem; }

.k-translate-y-2 { --k-translate-y: 0.5rem; }

.k-translate-y-3 { --k-translate-y: 0.75rem; }

.k-translate-y-4 { --k-translate-y: 1rem; }

.k-translate-y-5 { --k-translate-y: 1.25rem; }

.k-translate-y-6 { --k-translate-y: 1.5rem; }

.k-translate-y-8 { --k-translate-y: 2rem; }

.k-translate-y-10 { --k-translate-y: 2.5rem; }

.k-translate-y-12 { --k-translate-y: 3rem; }

.k-translate-y-16 { --k-translate-y: 4rem; }

.k-translate-y-20 { --k-translate-y: 5rem; }

.k-translate-y-24 { --k-translate-y: 6rem; }

.k-translate-y-32 { --k-translate-y: 8rem; }

.k-translate-y-40 { --k-translate-y: 10rem; }

.k-translate-y-48 { --k-translate-y: 12rem; }

.k-translate-y-56 { --k-translate-y: 14rem; }

.k-translate-y-64 { --k-translate-y: 16rem; }

.k-translate-y-1\/2 { --k-translate-y: 50%; }

.k-translate-y-1\/3 { --k-translate-y: 33.333333%; }

.k-translate-y-2\/3 { --k-translate-y: 66.666667%; }

.k-translate-y-1\/4 { --k-translate-y: 25%; }

.k-translate-y-2\/4 { --k-translate-y: 50%; }

.k-translate-y-3\/4 { --k-translate-y: 75%; }

.k-translate-y-full { --k-translate-y: 100%; }

/* 🔄 Rotate */

.k-rotate-0 { --k-rotate: 0deg; }

.k-rotate-1 { --k-rotate: 1deg; }

.k-rotate-2 { --k-rotate: 2deg; }

.k-rotate-3 { --k-rotate: 3deg; }

.k-rotate-6 { --k-rotate: 6deg; }

.k-rotate-12 { --k-rotate: 12deg; }

.k-rotate-45 { --k-rotate: 45deg; }

.k-rotate-90 { --k-rotate: 90deg; }

.k-rotate-180 { --k-rotate: 180deg; }

/* 📏 Scale */

.k-scale-0 { --k-scale-x: 0; --k-scale-y: 0; }

.k-scale-50 { --k-scale-x: 0.5; --k-scale-y: 0.5; }

.k-scale-75 { --k-scale-x: 0.75; --k-scale-y: 0.75; }

.k-scale-90 { --k-scale-x: 0.9; --k-scale-y: 0.9; }

.k-scale-95 { --k-scale-x: 0.95; --k-scale-y: 0.95; }

.k-scale-100 { --k-scale-x: 1; --k-scale-y: 1; }

.k-scale-105 { --k-scale-x: 1.05; --k-scale-y: 1.05; }

.k-scale-110 { --k-scale-x: 1.1; --k-scale-y: 1.1; }

.k-scale-125 { --k-scale-x: 1.25; --k-scale-y: 1.25; }

.k-scale-150 { --k-scale-x: 1.5; --k-scale-y: 1.5; }

.k-scale-x-0 { --k-scale-x: 0; }

.k-scale-x-50 { --k-scale-x: 0.5; }

.k-scale-x-75 { --k-scale-x: 0.75; }

.k-scale-x-90 { --k-scale-x: 0.9; }

.k-scale-x-95 { --k-scale-x: 0.95; }

.k-scale-x-100 { --k-scale-x: 1; }

.k-scale-x-105 { --k-scale-x: 1.05; }

.k-scale-x-110 { --k-scale-x: 1.1; }

.k-scale-x-125 { --k-scale-x: 1.25; }

.k-scale-x-150 { --k-scale-x: 1.5; }

.k-scale-y-0 { --k-scale-y: 0; }

.k-scale-y-50 { --k-scale-y: 0.5; }

.k-scale-y-75 { --k-scale-y: 0.75; }

.k-scale-y-90 { --k-scale-y: 0.9; }

.k-scale-y-95 { --k-scale-y: 0.95; }

.k-scale-y-100 { --k-scale-y: 1; }

.k-scale-y-105 { --k-scale-y: 1.05; }

.k-scale-y-110 { --k-scale-y: 1.1; }

.k-scale-y-125 { --k-scale-y: 1.25; }

.k-scale-y-150 { --k-scale-y: 1.5; }

/* 🎭 Skew */

.k-skew-x-0 { --k-skew-x: 0deg; }

.k-skew-x-1 { --k-skew-x: 1deg; }

.k-skew-x-2 { --k-skew-x: 2deg; }

.k-skew-x-3 { --k-skew-x: 3deg; }

.k-skew-x-6 { --k-skew-x: 6deg; }

.k-skew-x-12 { --k-skew-x: 12deg; }

.k-skew-y-0 { --k-skew-y: 0deg; }

.k-skew-y-1 { --k-skew-y: 1deg; }

.k-skew-y-2 { --k-skew-y: 2deg; }

.k-skew-y-3 { --k-skew-y: 3deg; }

.k-skew-y-6 { --k-skew-y: 6deg; }

.k-skew-y-12 { --k-skew-y: 12deg; }

/* 🎬 Keyframe Animations */

.k-animate-none { animation: none; }

.k-animate-spin { animation: k-spin 1s linear infinite; }

.k-animate-ping { animation: k-ping 1s cubic-bezier(0, 0, 0.2, 1) infinite; }

.k-animate-pulse { animation: k-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }

.k-animate-bounce { animation: k-bounce 1s infinite; }

.k-animate-glitch { animation: k-glitch 0.3s ease-in-out infinite; }

.k-animate-glow { animation: k-glow 2s ease-in-out infinite alternate; }

.k-animate-float { animation: k-float 3s ease-in-out infinite; }

.k-animate-slide-up { animation: k-slide-up 0.5s ease-out; }

.k-animate-slide-down { animation: k-slide-down 0.5s ease-out; }

.k-animate-slide-left { animation: k-slide-left 0.5s ease-out; }

.k-animate-slide-right { animation: k-slide-right 0.5s ease-out; }

.k-animate-fade-in { animation: k-fade-in 0.5s ease-out; }

.k-animate-fade-out { animation: k-fade-out 0.5s ease-out; }

.k-animate-zoom-in { animation: k-zoom-in 0.5s ease-out; }

.k-animate-zoom-out { animation: k-zoom-out 0.5s ease-out; }

/* 🎯 Keyframes */

@keyframes k-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes k-ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes k-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes k-bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

@keyframes k-glitch {
  0% { transform: translate(0); }
  20% { transform: translate(-2px, 2px); }
  40% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  80% { transform: translate(2px, -2px); }
  100% { transform: translate(0); }
}

@keyframes k-glow {
  0% { 
    box-shadow: 0 0 5px currentColor;
    filter: brightness(1);
  }
  100% { 
    box-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
    filter: brightness(1.2);
  }
}

@keyframes k-float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes k-slide-up {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes k-slide-down {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes k-slide-left {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes k-slide-right {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes k-fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes k-fade-out {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes k-zoom-in {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes k-zoom-out {
  from {
    transform: scale(1.1);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 🌟 Cyberpunk Animations */

@keyframes k-cyber-pulse {
  0%, 100% {
    box-shadow: 
      0 0 5px #00ffff,
      0 0 10px #00ffff,
      0 0 15px #00ffff;
    box-shadow: 
      0 0 5px var(--k-neon-blue),
      0 0 10px var(--k-neon-blue),
      0 0 15px var(--k-neon-blue);
  }
  50% {
    box-shadow: 
      0 0 10px #00ffff,
      0 0 20px #00ffff,
      0 0 30px #00ffff,
      0 0 40px #00ffff;
    box-shadow: 
      0 0 10px var(--k-neon-blue),
      0 0 20px var(--k-neon-blue),
      0 0 30px var(--k-neon-blue),
      0 0 40px var(--k-neon-blue);
  }
}

@keyframes k-neon-flicker {
  0%, 100% { opacity: 1; }
  2% { opacity: 0.8; }
  4% { opacity: 1; }
  8% { opacity: 0.8; }
  10% { opacity: 1; }
  12% { opacity: 0.6; }
  14% { opacity: 1; }
  16% { opacity: 0.4; }
  18% { opacity: 1; }
  20% { opacity: 0.8; }
  22% { opacity: 1; }
}

@keyframes k-matrix-rain {
  0% { transform: translateY(-100vh); }
  100% { transform: translateY(100vh); }
}

.k-animate-cyber-pulse { animation: k-cyber-pulse 2s ease-in-out infinite; }

.k-animate-neon-flicker { animation: k-neon-flicker 3s linear infinite; }

.k-animate-matrix-rain { animation: k-matrix-rain 2s linear infinite; }

/* 🎮 Hover Animations */

.k-hover\:scale-105:hover { transform: scale(1.05); }

.k-hover\:scale-110:hover { transform: scale(1.1); }

.k-hover\:scale-125:hover { transform: scale(1.25); }

.k-hover\:-translate-y-1:hover { transform: translateY(-0.25rem); }

.k-hover\:-translate-y-2:hover { transform: translateY(-0.5rem); }

.k-hover\:rotate-6:hover { transform: rotate(6deg); }

.k-hover\:rotate-12:hover { transform: rotate(12deg); }

/* 📱 Responsive Animations */

@media (prefers-reduced-motion: reduce) {
  .k-animate-spin,
  .k-animate-ping,
  .k-animate-pulse,
  .k-animate-bounce,
  .k-animate-glitch,
  .k-animate-glow,
  .k-animate-float,
  .k-animate-cyber-pulse,
  .k-animate-neon-flicker,
  .k-animate-matrix-rain {
    animation: none;
  }
}

/* 🧱 Import Components */

/**
 * KilatCSS Components
 * Ready-to-use UI components with glow effects
 */

/* 🎯 Button Components */

.k-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.25rem;
  border-radius: 0.375rem;
  border-radius: var(--k-radius-md);
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 300ms ease;
  transition: all var(--k-transition-normal);
  text-decoration: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  position: relative;
  overflow: hidden;
}

.k-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Button Variants */

.k-btn-primary {
  background-color: #00ffff;
  background-color: var(--k-primary);
  color: #000000;
  color: var(--k-background);
  border-color: #00ffff;
  border-color: var(--k-primary);
}

.k-btn-primary:hover {
  box-shadow: 0 0 20px #00ffff;
  box-shadow: 0 0 20px var(--k-primary);
  transform: translateY(-1px);
}

.k-btn-secondary {
  background-color: transparent;
  color: #ff00ff;
  color: var(--k-secondary);
  border-color: #ff00ff;
  border-color: var(--k-secondary);
}

.k-btn-secondary:hover {
  background-color: #ff00ff;
  background-color: var(--k-secondary);
  color: #000000;
  color: var(--k-background);
  box-shadow: 0 0 15px #ff00ff;
  box-shadow: 0 0 15px var(--k-secondary);
}

.k-btn-ghost {
  background-color: transparent;
  color: #ffffff;
  color: var(--k-text);
  border-color: #888888;
  border-color: var(--k-text-muted);
}

.k-btn-ghost:hover {
  background-color: #111111;
  background-color: var(--k-surface);
  border-color: #ffffff;
  border-color: var(--k-text);
  box-shadow: 0 0 10px #888888;
  box-shadow: 0 0 10px var(--k-text-muted);
}

/* Button Sizes */

.k-btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
}

.k-btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

.k-btn-xl {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

/* 🎴 Card Components */

.k-card {
  background-color: #111111;
  background-color: var(--k-surface);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  border-radius: var(--k-radius-lg);
  padding: 1.5rem;
  box-shadow: var(--k-shadow-md);
  transition: all 300ms ease;
  transition: all var(--k-transition-normal);
}

.k-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--k-shadow-lg);
}

.k-card-header {
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.k-card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: #ffffff;
  color: var(--k-text);
}

.k-card-description {
  color: #888888;
  color: var(--k-text-muted);
  margin: 0.5rem 0 0 0;
}

.k-card-content {
  margin-bottom: 1rem;
}

.k-card-footer {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  gap: 0.5rem;
}

/* 📝 Input Components */

.k-input {
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #ffffff;
  color: var(--k-text);
  background-color: #111111;
  background-color: var(--k-surface);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.375rem;
  border-radius: var(--k-radius-md);
  transition: all 300ms ease;
  transition: all var(--k-transition-normal);
}

.k-input:focus {
  outline: none;
  border-color: #00ffff;
  border-color: var(--k-primary);
  box-shadow: 0 0 0 3px rgba(#00ffff, 0.1);
  box-shadow: 0 0 0 3px rgba(var(--k-primary), 0.1);
}

.k-input::-moz-placeholder {
  color: #888888;
  color: var(--k-text-muted);
}

.k-input::placeholder {
  color: #888888;
  color: var(--k-text-muted);
}

.k-input-group {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.k-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #ffffff;
  color: var(--k-text);
}

.k-input-error {
  border-color: #ff0040;
  border-color: var(--k-neon-red);
  box-shadow: 0 0 0 3px rgba(255, 0, 64, 0.1);
}

.k-error-message {
  font-size: 0.75rem;
  color: #ff0040;
  color: var(--k-neon-red);
  margin-top: 0.25rem;
}

/* 🏷️ Badge Components */

.k-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 9999px;
  border-radius: var(--k-radius-full);
  background-color: #00ffff;
  background-color: var(--k-primary);
  color: #000000;
  color: var(--k-background);
}

.k-badge-secondary {
  background-color: #ff00ff;
  background-color: var(--k-secondary);
}

.k-badge-success {
  background-color: #00ff00;
  background-color: var(--k-neon-green);
}

.k-badge-warning {
  background-color: #ffff00;
  background-color: var(--k-neon-yellow);
  color: #000000;
  color: var(--k-background);
}

.k-badge-error {
  background-color: #ff0040;
  background-color: var(--k-neon-red);
}

.k-badge-outline {
  background-color: transparent;
  border: 1px solid currentColor;
  color: #ffffff;
  color: var(--k-text);
}

/* 🎛️ Modal Components */

.k-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1040;
  z-index: var(--k-z-modal);
  animation: k-fade-in 0.2s ease-out;
}

.k-modal {
  background-color: #111111;
  background-color: var(--k-surface);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  border-radius: var(--k-radius-lg);
  box-shadow: var(--k-shadow-xl);
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  animation: k-scale-in 0.2s ease-out;
}

.k-modal-header {
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.k-modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.k-modal-close {
  background: none;
  border: none;
  color: #888888;
  color: var(--k-text-muted);
  cursor: pointer;
  font-size: 1.5rem;
  padding: 0;
  transition: color 150ms ease;
  transition: color var(--k-transition-fast);
}

.k-modal-close:hover {
  color: #ffffff;
  color: var(--k-text);
}

.k-modal-content {
  padding: 1.5rem;
}

.k-modal-footer {
  padding: 1rem 1.5rem 1.5rem 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

/* 🧭 Navigation Components */

.k-nav {
  display: flex;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.k-nav-brand {
  font-size: 1.25rem;
  font-weight: 700;
  color: #00ffff;
  color: var(--k-primary);
  text-decoration: none;
  margin-right: auto;
}

.k-nav-links {
  display: flex;
  gap: 1.5rem;
  list-style: none;
  margin: 0;
  padding: 0;
}

.k-nav-link {
  color: #888888;
  color: var(--k-text-muted);
  text-decoration: none;
  transition: color 150ms ease;
  transition: color var(--k-transition-fast);
  position: relative;
}

.k-nav-link:hover,
.k-nav-link.active {
  color: #00ffff;
  color: var(--k-primary);
}

.k-nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #00ffff;
  background-color: var(--k-primary);
  box-shadow: 0 0 8px #00ffff;
  box-shadow: 0 0 8px var(--k-primary);
}

/* 📱 Sidebar Components */

.k-sidebar {
  width: 16rem;
  height: 100vh;
  background-color: #111111;
  background-color: var(--k-surface);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1.5rem 1rem;
  position: fixed;
  left: 0;
  top: 0;
  overflow-y: auto;
  transition: transform 300ms ease;
  transition: transform var(--k-transition-normal);
}

.k-sidebar-hidden {
  transform: translateX(-100%);
}

.k-sidebar-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.k-sidebar-nav {
  list-style: none;
  margin: 0;
  padding: 0;
}

.k-sidebar-nav-item {
  margin-bottom: 0.5rem;
}

.k-sidebar-nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: #888888;
  color: var(--k-text-muted);
  text-decoration: none;
  border-radius: 0.375rem;
  border-radius: var(--k-radius-md);
  transition: all 150ms ease;
  transition: all var(--k-transition-fast);
}

.k-sidebar-nav-link:hover,
.k-sidebar-nav-link.active {
  background-color: rgba(255, 255, 255, 0.05);
  color: #00ffff;
  color: var(--k-primary);
}

/* 🎭 Animation Keyframes */

@keyframes k-fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes k-scale-in {
  from { 
    opacity: 0; 
    transform: scale(0.95); 
  }
  to { 
    opacity: 1; 
    transform: scale(1); 
  }
}

/* 🎯 CSS Custom Properties (CSS Variables) */

:root {
  /* 🎨 Color Palette */
  --k-primary: #00ffff;
  --k-secondary: #ff00ff;
  --k-accent: #ffff00;
  --k-background: #000000;
  --k-surface: #111111;
  --k-text: #ffffff;
  --k-text-muted: #888888;
  
  /* 🌈 Neon Colors */
  --k-neon-blue: #00ffff;
  --k-neon-pink: #ff00ff;
  --k-neon-green: #00ff00;
  --k-neon-yellow: #ffff00;
  --k-neon-purple: #8000ff;
  --k-neon-orange: #ff8000;
  --k-neon-red: #ff0040;
  
  /* 🔮 Glow Effects */
  --k-glow-sm: 0 0 5px currentColor;
  --k-glow-md: 0 0 10px currentColor, 0 0 20px currentColor;
  --k-glow-lg: 0 0 15px currentColor, 0 0 30px currentColor, 0 0 45px currentColor;
  --k-glow-xl: 0 0 20px currentColor, 0 0 40px currentColor, 0 0 60px currentColor, 0 0 80px currentColor;
  
  /* 📏 Spacing */
  --k-space-1: 0.25rem;
  --k-space-2: 0.5rem;
  --k-space-3: 0.75rem;
  --k-space-4: 1rem;
  --k-space-5: 1.25rem;
  --k-space-6: 1.5rem;
  --k-space-8: 2rem;
  --k-space-10: 2.5rem;
  --k-space-12: 3rem;
  --k-space-16: 4rem;
  --k-space-20: 5rem;
  --k-space-24: 6rem;
  
  /* 🔤 Typography */
  --k-font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --k-font-mono: 'Fira Code', 'JetBrains Mono', Consolas, monospace;
  --k-font-display: 'Orbitron', 'Exo 2', sans-serif;
  
  /* 🎭 Animations */
  --k-transition-fast: 150ms ease;
  --k-transition-normal: 300ms ease;
  --k-transition-slow: 500ms ease;
  
  /* 🌊 Border Radius */
  --k-radius-sm: 0.125rem;
  --k-radius-md: 0.375rem;
  --k-radius-lg: 0.5rem;
  --k-radius-xl: 0.75rem;
  --k-radius-full: 9999px;
  
  /* 🎯 Z-Index */
  --k-z-dropdown: 1000;
  --k-z-sticky: 1020;
  --k-z-fixed: 1030;
  --k-z-modal: 1040;
  --k-z-popover: 1050;
  --k-z-tooltip: 1060;
}

/* 🌙 Dark Mode Variables */

[data-kilat-mode="dark"] {
  --k-background: #000000;
  --k-surface: #111111;
  --k-text: #ffffff;
  --k-text-muted: #888888;
}

/* ☀️ Light Mode Variables */

[data-kilat-mode="light"] {
  --k-background: #ffffff;
  --k-surface: #f8f9fa;
  --k-text: #000000;
  --k-text-muted: #666666;
}

/* 🎯 Base Styles */

.kilat,
.kilat * {
  box-sizing: border-box;
}

.kilat {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-family: var(--k-font-sans);
  line-height: 1.5;
  color: #ffffff;
  color: var(--k-text);
  background-color: #000000;
  background-color: var(--k-background);
  transition: background-color 300ms ease, color 300ms ease;
  transition: background-color var(--k-transition-normal), color var(--k-transition-normal);
}

/* 🎨 Core Themes (main themes only) */

/* 🔮 Glow Utilities */

.k-glow {
  filter: drop-shadow(0 0 10px currentColor, 0 0 20px currentColor);
  filter: drop-shadow(var(--k-glow-md));
}

.k-glow-sm {
  filter: drop-shadow(0 0 5px currentColor);
  filter: drop-shadow(var(--k-glow-sm));
}

.k-glow-lg {
  filter: drop-shadow(0 0 15px currentColor, 0 0 30px currentColor, 0 0 45px currentColor);
  filter: drop-shadow(var(--k-glow-lg));
}

.k-glow-xl {
  filter: drop-shadow(0 0 20px currentColor, 0 0 40px currentColor, 0 0 60px currentColor, 0 0 80px currentColor);
  filter: drop-shadow(var(--k-glow-xl));
}

/* 🌈 Text Glow Colors */

.k-text-glow {
  text-shadow: 0 0 10px currentColor, 0 0 20px currentColor;
  text-shadow: var(--k-glow-md);
}

.k-text-glow-blue {
  color: #00ffff;
  color: var(--k-neon-blue);
  text-shadow: 0 0 10px currentColor, 0 0 20px currentColor;
  text-shadow: var(--k-glow-md);
}

.k-text-glow-pink {
  color: #ff00ff;
  color: var(--k-neon-pink);
  text-shadow: 0 0 10px currentColor, 0 0 20px currentColor;
  text-shadow: var(--k-glow-md);
}

.k-text-glow-green {
  color: #00ff00;
  color: var(--k-neon-green);
  text-shadow: 0 0 10px currentColor, 0 0 20px currentColor;
  text-shadow: var(--k-glow-md);
}

.k-text-glow-yellow {
  color: #ffff00;
  color: var(--k-neon-yellow);
  text-shadow: 0 0 10px currentColor, 0 0 20px currentColor;
  text-shadow: var(--k-glow-md);
}

.k-text-glow-purple {
  color: #8000ff;
  color: var(--k-neon-purple);
  text-shadow: 0 0 10px currentColor, 0 0 20px currentColor;
  text-shadow: var(--k-glow-md);
}

.k-text-glow-orange {
  color: #ff8000;
  color: var(--k-neon-orange);
  text-shadow: 0 0 10px currentColor, 0 0 20px currentColor;
  text-shadow: var(--k-glow-md);
}

.k-text-glow-red {
  color: #ff0040;
  color: var(--k-neon-red);
  text-shadow: 0 0 10px currentColor, 0 0 20px currentColor;
  text-shadow: var(--k-glow-md);
}

/* 🎭 Animations */

@keyframes k-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes k-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes k-bounce {
  0%, 100% { transform: translateY(-25%); animation-timing-function: cubic-bezier(0.8, 0, 1, 1); }
  50% { transform: translateY(0); animation-timing-function: cubic-bezier(0, 0, 0.2, 1); }
}

@keyframes k-glitch {
  0% { transform: translate(0); }
  20% { transform: translate(-2px, 2px); }
  40% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  80% { transform: translate(2px, -2px); }
  100% { transform: translate(0); }
}

.k-animate-pulse { animation: k-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }

.k-animate-spin { animation: k-spin 1s linear infinite; }

.k-animate-bounce { animation: k-bounce 1s infinite; }

.k-animate-glitch { animation: k-glitch 0.3s ease-in-out infinite; }

/* 🎯 Responsive Design */

@media (max-width: 640px) {
  .k-sm\:hidden { display: none; }
}

@media (max-width: 768px) {
  .k-md\:hidden { display: none; }
}

@media (max-width: 1024px) {
  .k-lg\:hidden { display: none; }
}

@media (max-width: 1280px) {
  .k-xl\:hidden { display: none; }
}

/* 🎨 Additional Themes */
