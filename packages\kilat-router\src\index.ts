// 🧭 Kilat.js Router - File-based routing system
// Export all types
export * from './types';

// Export core router components
export { <PERSON><PERSON>Router } from './components/KilatRouter';
export { KilatRoute } from './components/KilatRoute';
export { KilatLink } from './components/KilatLink';
export { KilatOutlet } from './components/KilatOutlet';

// Export layout components
export { NestedLayout } from './components/NestedLayout';
export { RouteTransition } from './components/RouteTransition';

// Export hooks
export { useKilatRouter } from './hooks/useKilatRouter';
export { useKilatParams } from './hooks/useKilatParams';
export { useKilatQuery } from './hooks/useKilatQuery';
export { useKilatNavigate } from './hooks/useKilatNavigate';
export { useRouteLoader } from './hooks/useRouteLoader';
export { useRouteTransition } from './hooks/useRouteTransition';

// Export utilities
export { createFileRoutes } from './utils/fileRoutes';
export { matchRoute } from './utils/routeMatcher';
export { parseRoutePath } from './utils/pathParser';
export { createRouterConfig } from './utils/config';

// Export middleware
export { authMiddleware } from './middleware/auth';
export { cacheMiddleware } from './middleware/cache';
export { analyticsMiddleware } from './middleware/analytics';
export { i18nMiddleware } from './middleware/i18n';

// Version info
export const KILAT_ROUTER_VERSION = '1.0.0';

// Default export
export default {
  version: KILAT_ROUTER_VERSION
};
