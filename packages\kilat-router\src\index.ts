// 🧭 Kilat.js Router - File-based routing system

// Re-export React Router components for convenience
export {
  BrowserRouter,
  Routes,
  Route,
  Link,
  Navigate,
  Outlet,
  useNavigate,
  useLocation,
  useParams,
  useSearchParams
} from 'react-router-dom';

// Export Kilat-specific components (simple versions)
export { <PERSON><PERSON>Route } from './components/KilatRoute';
export { KilatLink } from './components/KilatLink';
export { KilatOutlet } from './components/KilatOutlet';
export { NestedLayout } from './components/NestedLayout';

// Version info
export const KILAT_ROUTER_VERSION = '1.0.0';

// Default export
export default {
  version: KILAT_ROUTER_VERSION
};
