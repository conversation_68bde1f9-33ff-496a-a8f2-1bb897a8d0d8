{"name": "kilat-core", "version": "1.0.0", "description": "⚡ Kilat.js Core Engine - Layout Manager, SSR, Context Providers", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.esm.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./types": {"import": "./dist/types.esm.js", "require": "./dist/types.js", "types": "./dist/types.d.ts"}}, "files": ["dist", "README.md"], "scripts": {"build": "bun run build:esm && bun run build:cjs && bun run build:types", "build:esm": "esbuild src/index.ts --bundle --format=esm --outfile=dist/index.esm.js --external:react --external:react-dom", "build:cjs": "esbuild src/index.ts --bundle --format=cjs --outfile=dist/index.js --external:react --external:react-dom", "build:types": "tsc --emitDeclarationOnly --outDir dist", "dev": "bun run build --watch", "test": "bun test", "clean": "rm -rf dist"}, "keywords": ["kilat", "core", "react", "ssr", "layout", "context", "framework"], "author": "KangPCode", "license": "MIT", "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dependencies": {"zustand": "^4.4.7", "react-error-boundary": "^4.0.11"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "esbuild": "^0.19.0", "typescript": "^5.3.0"}}