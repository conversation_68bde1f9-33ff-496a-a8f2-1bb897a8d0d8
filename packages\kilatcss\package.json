{"name": "kilatcss", "version": "1.0.0", "description": "⚡ KilatCSS - Utility-first CSS framework with glow effects and futuristic themes", "main": "dist/kilat.css", "style": "dist/kilat.css", "files": ["dist", "src", "themes", "README.md"], "exports": {".": "./dist/kilat.css", "./themes/*": "./dist/themes/*.css", "./components": "./dist/components.css", "./utilities": "./dist/utilities.css"}, "scripts": {"build": "bun run build:css && bun run build:themes && bun run build:components", "build:css": "postcss src/kilat.css -o dist/kilat.css", "build:themes": "bun run build:theme:cyberpunk && bun run build:theme:nusantara && bun run build:theme:retro", "build:theme:cyberpunk": "postcss src/themes/cyberpunk.css -o dist/themes/cyberpunk.css", "build:theme:nusantara": "postcss src/themes/nusantara.css -o dist/themes/nusantara.css", "build:theme:retro": "postcss src/themes/retro.css -o dist/themes/retro.css", "build:components": "postcss src/components/index.css -o dist/components.css", "dev": "bun run build --watch", "test": "bun test", "clean": "rm -rf dist"}, "keywords": ["css", "utility", "glow", "cyberpunk", "nusantara", "futuristic", "framework", "kilat"], "author": "KangPCode", "license": "MIT", "devDependencies": {"postcss": "^8.4.32", "postcss-cli": "^11.0.0", "postcss-import": "^15.1.0", "postcss-nested": "^6.0.1", "postcss-custom-properties": "^13.3.2", "postcss-color-function": "^4.1.0", "autoprefixer": "^10.4.16", "cssnano": "^6.0.1"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}