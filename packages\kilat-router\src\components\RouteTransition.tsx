import React, { ReactNode, useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { CSSTransition, TransitionGroup } from 'react-transition-group';
import type { RouteTransition as RouteTransitionConfig } from '../types';

// 🎭 Route Transition Component
interface RouteTransitionProps {
  children: ReactNode;
  config: RouteTransitionConfig;
}

export function RouteTransition({ children, config }: RouteTransitionProps) {
  const location = useLocation();
  const [isTransitioning, setIsTransitioning] = useState(false);

  // 🎨 Generate CSS classes based on transition type
  const getTransitionClasses = (type: string) => {
    const baseClass = 'kilat-route-transition';
    
    switch (type) {
      case 'fade':
        return {
          enter: `${baseClass}-fade-enter`,
          enterActive: `${baseClass}-fade-enter-active`,
          exit: `${baseClass}-fade-exit`,
          exitActive: `${baseClass}-fade-exit-active`
        };
      case 'slide':
        return {
          enter: `${baseClass}-slide-enter`,
          enterActive: `${baseClass}-slide-enter-active`,
          exit: `${baseClass}-slide-exit`,
          exitActive: `${baseClass}-slide-exit-active`
        };
      case 'scale':
        return {
          enter: `${baseClass}-scale-enter`,
          enterActive: `${baseClass}-scale-enter-active`,
          exit: `${baseClass}-scale-exit`,
          exitActive: `${baseClass}-scale-exit-active`
        };
      case 'flip':
        return {
          enter: `${baseClass}-flip-enter`,
          enterActive: `${baseClass}-flip-enter-active`,
          exit: `${baseClass}-flip-exit`,
          exitActive: `${baseClass}-flip-exit-active`
        };
      default:
        return {
          enter: `${baseClass}-fade-enter`,
          enterActive: `${baseClass}-fade-enter-active`,
          exit: `${baseClass}-fade-exit`,
          exitActive: `${baseClass}-fade-exit-active`
        };
    }
  };

  const transitionClasses = getTransitionClasses(config.type || 'fade');

  // 🎯 Inject CSS styles
  useEffect(() => {
    const styleId = 'kilat-route-transitions';
    let styleElement = document.getElementById(styleId) as HTMLStyleElement;
    
    if (!styleElement) {
      styleElement = document.createElement('style');
      styleElement.id = styleId;
      document.head.appendChild(styleElement);
    }

    const duration = config.duration || 300;
    const easing = config.easing || 'ease-in-out';

    styleElement.textContent = `
      /* Base transition styles */
      .kilat-route-transition {
        position: relative;
        width: 100%;
        height: 100%;
      }

      /* Fade transitions */
      .kilat-route-transition-fade-enter {
        opacity: 0;
      }
      .kilat-route-transition-fade-enter-active {
        opacity: 1;
        transition: opacity ${duration}ms ${easing};
      }
      .kilat-route-transition-fade-exit {
        opacity: 1;
      }
      .kilat-route-transition-fade-exit-active {
        opacity: 0;
        transition: opacity ${duration}ms ${easing};
      }

      /* Slide transitions */
      .kilat-route-transition-slide-enter {
        transform: translateX(100%);
      }
      .kilat-route-transition-slide-enter-active {
        transform: translateX(0);
        transition: transform ${duration}ms ${easing};
      }
      .kilat-route-transition-slide-exit {
        transform: translateX(0);
      }
      .kilat-route-transition-slide-exit-active {
        transform: translateX(-100%);
        transition: transform ${duration}ms ${easing};
      }

      /* Scale transitions */
      .kilat-route-transition-scale-enter {
        transform: scale(0.9);
        opacity: 0;
      }
      .kilat-route-transition-scale-enter-active {
        transform: scale(1);
        opacity: 1;
        transition: transform ${duration}ms ${easing}, opacity ${duration}ms ${easing};
      }
      .kilat-route-transition-scale-exit {
        transform: scale(1);
        opacity: 1;
      }
      .kilat-route-transition-scale-exit-active {
        transform: scale(1.1);
        opacity: 0;
        transition: transform ${duration}ms ${easing}, opacity ${duration}ms ${easing};
      }

      /* Flip transitions */
      .kilat-route-transition-flip-enter {
        transform: rotateY(-90deg);
        opacity: 0;
      }
      .kilat-route-transition-flip-enter-active {
        transform: rotateY(0deg);
        opacity: 1;
        transition: transform ${duration}ms ${easing}, opacity ${duration}ms ${easing};
      }
      .kilat-route-transition-flip-exit {
        transform: rotateY(0deg);
        opacity: 1;
      }
      .kilat-route-transition-flip-exit-active {
        transform: rotateY(90deg);
        opacity: 0;
        transition: transform ${duration}ms ${easing}, opacity ${duration}ms ${easing};
      }

      /* Cyberpunk glow effects */
      .kilat-route-transition-glow-enter {
        opacity: 0;
        filter: blur(10px) brightness(0.5);
        box-shadow: 0 0 0 rgba(0, 255, 255, 0);
      }
      .kilat-route-transition-glow-enter-active {
        opacity: 1;
        filter: blur(0px) brightness(1);
        box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
        transition: all ${duration}ms ${easing};
      }
      .kilat-route-transition-glow-exit {
        opacity: 1;
        filter: blur(0px) brightness(1);
        box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
      }
      .kilat-route-transition-glow-exit-active {
        opacity: 0;
        filter: blur(10px) brightness(0.5);
        box-shadow: 0 0 0 rgba(0, 255, 255, 0);
        transition: all ${duration}ms ${easing};
      }

      /* Matrix digital rain effect */
      .kilat-route-transition-matrix-enter {
        opacity: 0;
        transform: translateY(-20px);
        filter: hue-rotate(0deg);
      }
      .kilat-route-transition-matrix-enter-active {
        opacity: 1;
        transform: translateY(0);
        filter: hue-rotate(360deg);
        transition: all ${duration}ms ${easing};
      }
      .kilat-route-transition-matrix-exit {
        opacity: 1;
        transform: translateY(0);
        filter: hue-rotate(360deg);
      }
      .kilat-route-transition-matrix-exit-active {
        opacity: 0;
        transform: translateY(20px);
        filter: hue-rotate(0deg);
        transition: all ${duration}ms ${easing};
      }

      /* Nusantara traditional transition */
      .kilat-route-transition-nusantara-enter {
        opacity: 0;
        transform: scale(0.8) rotate(-5deg);
        filter: sepia(100%) hue-rotate(30deg);
      }
      .kilat-route-transition-nusantara-enter-active {
        opacity: 1;
        transform: scale(1) rotate(0deg);
        filter: sepia(0%) hue-rotate(0deg);
        transition: all ${duration}ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
      }
      .kilat-route-transition-nusantara-exit {
        opacity: 1;
        transform: scale(1) rotate(0deg);
        filter: sepia(0%) hue-rotate(0deg);
      }
      .kilat-route-transition-nusantara-exit-active {
        opacity: 0;
        transform: scale(1.2) rotate(5deg);
        filter: sepia(100%) hue-rotate(30deg);
        transition: all ${duration}ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
      }
    `;

    return () => {
      // Cleanup styles when component unmounts
      if (styleElement && styleElement.parentNode) {
        styleElement.parentNode.removeChild(styleElement);
      }
    };
  }, [config.duration, config.easing, config.type]);

  // 🎭 Handle transition events
  const handleEnter = () => {
    setIsTransitioning(true);
    config.onEnter?.();
  };

  const handleEntered = () => {
    setIsTransitioning(false);
    config.onEntered?.();
  };

  const handleExit = () => {
    setIsTransitioning(true);
    config.onExit?.();
  };

  const handleExited = () => {
    setIsTransitioning(false);
    config.onExited?.();
  };

  return (
    <TransitionGroup className="kilat-route-transition-group">
      <CSSTransition
        key={location.pathname}
        classNames={transitionClasses}
        timeout={config.duration || 300}
        onEnter={handleEnter}
        onEntered={handleEntered}
        onExit={handleExit}
        onExited={handleExited}
        unmountOnExit
      >
        <div className="kilat-route-transition">
          {children}
        </div>
      </CSSTransition>
    </TransitionGroup>
  );
}

// 🎨 Preset transition configurations
export const transitionPresets = {
  fade: {
    type: 'fade',
    duration: 300,
    easing: 'ease-in-out'
  },
  slide: {
    type: 'slide',
    duration: 400,
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
  },
  scale: {
    type: 'scale',
    duration: 350,
    easing: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
  },
  flip: {
    type: 'flip',
    duration: 500,
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
  },
  cyberpunk: {
    type: 'glow',
    duration: 400,
    easing: 'ease-out'
  },
  matrix: {
    type: 'matrix',
    duration: 600,
    easing: 'ease-in-out'
  },
  nusantara: {
    type: 'nusantara',
    duration: 500,
    easing: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
  }
} as const;
