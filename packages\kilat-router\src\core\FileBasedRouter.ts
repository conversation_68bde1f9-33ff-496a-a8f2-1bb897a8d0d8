import { join, relative, extname, basename, dirname } from 'path';
import { existsSync, readdirSync, statSync, watchFile, unwatchFile } from 'fs';
import { createLogger } from 'kilat-utils';
import type { KilatRoute, KilatRouterConfig, RouteMeta } from '../types';

/**
 * 📁 FileBasedRouter - Automatic route generation from file system
 * Scans pages directory and generates routes automatically
 */
export class FileBasedRouter {
  private config: KilatRouterConfig;
  private logger = createLogger({ prefix: 'FileBasedRouter' });
  private routes: KilatRoute[] = [];
  private watchers = new Set<string>();
  private callbacks = new Set<(routes: KilatRoute[]) => void>();

  constructor(config: KilatRouterConfig) {
    this.config = config;
  }

  // 🔍 Generate routes from file system
  async generateRoutes(pagesDir: string): Promise<KilatRoute[]> {
    if (!existsSync(pagesDir)) {
      this.logger.warn(`Pages directory not found: ${pagesDir}`);
      return [];
    }

    this.logger.info(`Generating routes from: ${pagesDir}`);
    
    const routes = await this.scanDirectory(pagesDir, pagesDir);
    this.routes = this.processRoutes(routes);
    
    this.logger.success(`Generated ${this.routes.length} routes`);
    return this.routes;
  }

  // 📁 Scan directory recursively
  private async scanDirectory(dir: string, baseDir: string): Promise<KilatRoute[]> {
    const routes: KilatRoute[] = [];
    
    try {
      const entries = readdirSync(dir);
      
      for (const entry of entries) {
        const fullPath = join(dir, entry);
        const stat = statSync(fullPath);
        
        if (stat.isDirectory()) {
          // Handle nested directories
          const nestedRoutes = await this.scanDirectory(fullPath, baseDir);
          routes.push(...nestedRoutes);
        } else if (stat.isFile()) {
          // Handle page files
          const route = await this.createRouteFromFile(fullPath, baseDir);
          if (route) {
            routes.push(route);
          }
        }
      }
    } catch (error) {
      this.logger.error(`Failed to scan directory: ${dir}`, error);
    }
    
    return routes;
  }

  // 📄 Create route from file
  private async createRouteFromFile(filePath: string, baseDir: string): Promise<KilatRoute | null> {
    const ext = extname(filePath);
    const supportedExtensions = ['.tsx', '.ts', '.jsx', '.js'];
    
    if (!supportedExtensions.includes(ext)) {
      return null;
    }
    
    const relativePath = relative(baseDir, filePath);
    const routePath = this.filePathToRoutePath(relativePath);
    
    // Skip if file should be excluded
    if (this.shouldExcludeFile(relativePath)) {
      return null;
    }
    
    try {
      // Dynamic import for the component
      const componentModule = await import(filePath);
      const component = componentModule.default || componentModule[basename(filePath, ext)];
      
      if (!component) {
        this.logger.warn(`No default export found in: ${filePath}`);
        return null;
      }
      
      // Extract metadata from component or file
      const meta = await this.extractMetadata(filePath, componentModule);
      
      // Determine layout
      const layout = await this.resolveLayout(filePath, meta);
      
      // Determine middleware
      const middleware = await this.resolveMiddleware(filePath, meta);
      
      const route: KilatRoute = {
        path: routePath,
        component,
        layout,
        middleware,
        meta,
        exact: !routePath.includes(':') && !routePath.includes('*'),
        caseSensitive: this.config.caseSensitive
      };
      
      this.logger.debug(`Created route: ${routePath} -> ${relativePath}`);
      return route;
      
    } catch (error) {
      this.logger.error(`Failed to create route from file: ${filePath}`, error);
      return null;
    }
  }

  // 🛤️ Convert file path to route path
  private filePathToRoutePath(filePath: string): string {
    let routePath = filePath
      .replace(/\\/g, '/') // Normalize path separators
      .replace(/\.(tsx?|jsx?)$/, '') // Remove file extensions
      .replace(/\/index$/, '') // Remove index from path
      .replace(/^\/?/, '/'); // Ensure leading slash
    
    // Handle dynamic routes
    routePath = routePath
      .replace(/\[([^\]]+)\]/g, ':$1') // [id] -> :id
      .replace(/\[\.\.\.([^\]]+)\]/g, '*$1'); // [...slug] -> *slug
    
    // Handle root index
    if (routePath === '/') {
      return '/';
    }
    
    // Remove trailing slash unless it's root
    return routePath.replace(/\/$/, '') || '/';
  }

  // 🚫 Check if file should be excluded
  private shouldExcludeFile(filePath: string): boolean {
    const excludePatterns = [
      /^_/, // Files starting with underscore
      /\.test\.(tsx?|jsx?)$/, // Test files
      /\.spec\.(tsx?|jsx?)$/, // Spec files
      /\.stories\.(tsx?|jsx?)$/, // Storybook files
      /\.d\.ts$/, // Type definition files
      /node_modules/, // Node modules
    ];
    
    return excludePatterns.some(pattern => pattern.test(filePath));
  }

  // 📊 Extract metadata from file
  private async extractMetadata(filePath: string, moduleExports: any): Promise<RouteMeta> {
    const meta: RouteMeta = {};
    
    // Check for exported metadata
    if (moduleExports.meta) {
      Object.assign(meta, moduleExports.meta);
    }
    
    // Check for getStaticProps or getServerSideProps
    if (moduleExports.getStaticProps) {
      meta.preload = true;
    }
    
    // Extract from component properties
    const component = moduleExports.default;
    if (component && component.meta) {
      Object.assign(meta, component.meta);
    }
    
    // Extract from file name patterns
    const fileName = basename(filePath);
    
    if (fileName.includes('auth') || fileName.includes('login') || fileName.includes('register')) {
      meta.auth = true;
    }
    
    if (fileName.includes('admin')) {
      meta.roles = ['admin'];
    }
    
    return meta;
  }

  // 🎨 Resolve layout for route
  private async resolveLayout(filePath: string, meta: RouteMeta): Promise<any> {
    // Check meta for explicit layout
    if (meta.layout) {
      return await this.loadLayout(meta.layout);
    }
    
    // Check for layout files in directory hierarchy
    let currentDir = dirname(filePath);
    
    while (currentDir !== dirname(currentDir)) {
      const layoutPath = join(currentDir, '_layout.tsx');
      
      if (existsSync(layoutPath)) {
        try {
          const layoutModule = await import(layoutPath);
          return layoutModule.default;
        } catch (error) {
          this.logger.warn(`Failed to load layout: ${layoutPath}`, error);
        }
      }
      
      currentDir = dirname(currentDir);
    }
    
    // Return default layout
    return this.config.layouts?.default;
  }

  // 🔒 Resolve middleware for route
  private async resolveMiddleware(filePath: string, meta: RouteMeta): Promise<any[]> {
    const middleware: any[] = [];
    
    // Add global middleware
    if (this.config.middleware) {
      middleware.push(...this.config.middleware);
    }
    
    // Add auth middleware if required
    if (meta.auth) {
      const authMiddleware = await this.loadMiddleware('auth');
      if (authMiddleware) {
        middleware.push(authMiddleware);
      }
    }
    
    // Add role-based middleware
    if (meta.roles && meta.roles.length > 0) {
      const roleMiddleware = await this.loadMiddleware('roles');
      if (roleMiddleware) {
        middleware.push(roleMiddleware);
      }
    }
    
    // Check for middleware files in directory hierarchy
    let currentDir = dirname(filePath);
    
    while (currentDir !== dirname(currentDir)) {
      const middlewarePath = join(currentDir, '_middleware.tsx');
      
      if (existsSync(middlewarePath)) {
        try {
          const middlewareModule = await import(middlewarePath);
          if (middlewareModule.default) {
            middleware.push(middlewareModule.default);
          }
        } catch (error) {
          this.logger.warn(`Failed to load middleware: ${middlewarePath}`, error);
        }
      }
      
      currentDir = dirname(currentDir);
    }
    
    return middleware;
  }

  // 🎨 Load layout by name
  private async loadLayout(layoutName: string): Promise<any> {
    const layoutsDir = this.config.layoutsDir || 'src/layouts';
    const layoutPath = join(layoutsDir, `${layoutName}.tsx`);
    
    if (existsSync(layoutPath)) {
      try {
        const layoutModule = await import(layoutPath);
        return layoutModule.default;
      } catch (error) {
        this.logger.error(`Failed to load layout: ${layoutName}`, error);
      }
    }
    
    return null;
  }

  // 🔒 Load middleware by name
  private async loadMiddleware(middlewareName: string): Promise<any> {
    const middlewareDir = this.config.middlewareDir || 'src/middleware';
    const middlewarePath = join(middlewareDir, `${middlewareName}.tsx`);
    
    if (existsSync(middlewarePath)) {
      try {
        const middlewareModule = await import(middlewarePath);
        return middlewareModule.default;
      } catch (error) {
        this.logger.error(`Failed to load middleware: ${middlewareName}`, error);
      }
    }
    
    return null;
  }

  // 🔄 Process and optimize routes
  private processRoutes(routes: KilatRoute[]): KilatRoute[] {
    // Sort routes by specificity (more specific routes first)
    routes.sort((a, b) => {
      const aSpecificity = this.calculateRouteSpecificity(a.path);
      const bSpecificity = this.calculateRouteSpecificity(b.path);
      return bSpecificity - aSpecificity;
    });
    
    // Group routes by parent-child relationships
    const processedRoutes = this.buildRouteTree(routes);
    
    return processedRoutes;
  }

  // 📊 Calculate route specificity for sorting
  private calculateRouteSpecificity(path: string): number {
    let specificity = 0;
    
    // Static segments are more specific
    const segments = path.split('/').filter(Boolean);
    
    for (const segment of segments) {
      if (segment.startsWith(':')) {
        specificity += 1; // Dynamic segment
      } else if (segment.startsWith('*')) {
        specificity += 0.5; // Wildcard segment
      } else {
        specificity += 2; // Static segment
      }
    }
    
    return specificity;
  }

  // 🌳 Build route tree with parent-child relationships
  private buildRouteTree(routes: KilatRoute[]): KilatRoute[] {
    const routeMap = new Map<string, KilatRoute>();
    const rootRoutes: KilatRoute[] = [];
    
    // First pass: create route map
    for (const route of routes) {
      routeMap.set(route.path, { ...route, children: [] });
    }
    
    // Second pass: build tree structure
    for (const route of routes) {
      const segments = route.path.split('/').filter(Boolean);
      
      if (segments.length <= 1) {
        // Root level route
        rootRoutes.push(routeMap.get(route.path)!);
      } else {
        // Find parent route
        const parentPath = '/' + segments.slice(0, -1).join('/');
        const parent = routeMap.get(parentPath);
        
        if (parent) {
          parent.children!.push(routeMap.get(route.path)!);
        } else {
          // No parent found, add to root
          rootRoutes.push(routeMap.get(route.path)!);
        }
      }
    }
    
    return rootRoutes;
  }

  // 👀 Watch for file changes
  watchFiles(callback: (routes: KilatRoute[]) => void): () => void {
    this.callbacks.add(callback);
    
    const pagesDir = this.config.pagesDir || 'src/pages';
    
    if (existsSync(pagesDir)) {
      this.watchDirectory(pagesDir);
    }
    
    // Return cleanup function
    return () => {
      this.callbacks.delete(callback);
      this.stopWatching();
    };
  }

  // 👀 Watch directory for changes
  private watchDirectory(dir: string): void {
    try {
      const entries = readdirSync(dir);
      
      for (const entry of entries) {
        const fullPath = join(dir, entry);
        const stat = statSync(fullPath);
        
        if (stat.isDirectory()) {
          this.watchDirectory(fullPath);
        } else if (stat.isFile()) {
          this.watchFile(fullPath);
        }
      }
    } catch (error) {
      this.logger.error(`Failed to watch directory: ${dir}`, error);
    }
  }

  // 👀 Watch individual file
  private watchFile(filePath: string): void {
    if (this.watchers.has(filePath)) {
      return;
    }
    
    this.watchers.add(filePath);
    
    watchFile(filePath, async () => {
      this.logger.debug(`File changed: ${filePath}`);
      
      // Regenerate routes
      const pagesDir = this.config.pagesDir || 'src/pages';
      const newRoutes = await this.generateRoutes(pagesDir);
      
      // Notify callbacks
      this.callbacks.forEach(callback => callback(newRoutes));
    });
  }

  // 🛑 Stop watching files
  private stopWatching(): void {
    this.watchers.forEach(filePath => {
      unwatchFile(filePath);
    });
    this.watchers.clear();
  }

  // 📊 Get current routes
  getRoutes(): KilatRoute[] {
    return [...this.routes];
  }

  // 🔍 Find route by path
  findRoute(path: string): KilatRoute | undefined {
    return this.findRouteInTree(this.routes, path);
  }

  // 🔍 Find route in tree recursively
  private findRouteInTree(routes: KilatRoute[], path: string): KilatRoute | undefined {
    for (const route of routes) {
      if (route.path === path) {
        return route;
      }
      
      if (route.children && route.children.length > 0) {
        const found = this.findRouteInTree(route.children, path);
        if (found) {
          return found;
        }
      }
    }
    
    return undefined;
  }
}
