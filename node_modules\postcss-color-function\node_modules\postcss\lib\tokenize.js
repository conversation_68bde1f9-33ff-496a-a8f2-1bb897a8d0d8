'use strict';

exports.__esModule = true;
exports.default = tokenizer;
var SINGLE_QUOTE = 39;
var DOUBLE_QUOTE = 34;
var BACKSLASH = 92;
var SLASH = 47;
var NEWLINE = 10;
var SPACE = 32;
var FEED = 12;
var TAB = 9;
var CR = 13;
var OPEN_SQUARE = 91;
var CLOSE_SQUARE = 93;
var OPEN_PARENTHESES = 40;
var CLOSE_PARENTHESES = 41;
var OPEN_CURLY = 123;
var CLOSE_CURLY = 125;
var SEMICOLON = 59;
var ASTERISK = 42;
var COLON = 58;
var AT = 64;

var RE_AT_END = /[ \n\t\r\f\{\}\(\)'"\\;/\[\]#]/g;
var RE_WORD_END = /[ \n\t\r\f\(\)\{\}:;@!'"\\\]\[#]|\/(?=\*)/g;
var RE_BAD_BRACKET = /.[\\\/\("'\n]/;
var RE_HEX_ESCAPE = /[a-f0-9]/i;

function tokenizer(input) {
    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};

    var css = input.css.valueOf();
    var ignore = options.ignoreErrors;

    var code = void 0,
        next = void 0,
        quote = void 0,
        lines = void 0,
        last = void 0,
        content = void 0,
        escape = void 0,
        nextLine = void 0,
        nextOffset = void 0,
        escaped = void 0,
        escapePos = void 0,
        prev = void 0,
        n = void 0,
        currentToken = void 0;

    var length = css.length;
    var offset = -1;
    var line = 1;
    var pos = 0;
    var buffer = [];
    var returned = [];

    function unclosed(what) {
        throw input.error('Unclosed ' + what, line, pos - offset);
    }

    function endOfFile() {
        return returned.length === 0 && pos >= length;
    }

    function nextToken() {
        if (returned.length) return returned.pop();
        if (pos >= length) return;

        code = css.charCodeAt(pos);
        if (code === NEWLINE || code === FEED || code === CR && css.charCodeAt(pos + 1) !== NEWLINE) {
            offset = pos;
            line += 1;
        }

        switch (code) {
            case NEWLINE:
            case SPACE:
            case TAB:
            case CR:
            case FEED:
                next = pos;
                do {
                    next += 1;
                    code = css.charCodeAt(next);
                    if (code === NEWLINE) {
                        offset = next;
                        line += 1;
                    }
                } while (code === SPACE || code === NEWLINE || code === TAB || code === CR || code === FEED);

                currentToken = ['space', css.slice(pos, next)];
                pos = next - 1;
                break;

            case OPEN_SQUARE:
                currentToken = ['[', '[', line, pos - offset];
                break;

            case CLOSE_SQUARE:
                currentToken = [']', ']', line, pos - offset];
                break;

            case OPEN_CURLY:
                currentToken = ['{', '{', line, pos - offset];
                break;

            case CLOSE_CURLY:
                currentToken = ['}', '}', line, pos - offset];
                break;

            case COLON:
                currentToken = [':', ':', line, pos - offset];
                break;

            case SEMICOLON:
                currentToken = [';', ';', line, pos - offset];
                break;

            case OPEN_PARENTHESES:
                prev = buffer.length ? buffer.pop()[1] : '';
                n = css.charCodeAt(pos + 1);
                if (prev === 'url' && n !== SINGLE_QUOTE && n !== DOUBLE_QUOTE && n !== SPACE && n !== NEWLINE && n !== TAB && n !== FEED && n !== CR) {
                    next = pos;
                    do {
                        escaped = false;
                        next = css.indexOf(')', next + 1);
                        if (next === -1) {
                            if (ignore) {
                                next = pos;
                                break;
                            } else {
                                unclosed('bracket');
                            }
                        }
                        escapePos = next;
                        while (css.charCodeAt(escapePos - 1) === BACKSLASH) {
                            escapePos -= 1;
                            escaped = !escaped;
                        }
                    } while (escaped);

                    currentToken = ['brackets', css.slice(pos, next + 1), line, pos - offset, line, next - offset];

                    pos = next;
                } else {
                    next = css.indexOf(')', pos + 1);
                    content = css.slice(pos, next + 1);

                    if (next === -1 || RE_BAD_BRACKET.test(content)) {
                        currentToken = ['(', '(', line, pos - offset];
                    } else {
                        currentToken = ['brackets', content, line, pos - offset, line, next - offset];
                        pos = next;
                    }
                }

                break;

            case CLOSE_PARENTHESES:
                currentToken = [')', ')', line, pos - offset];
                break;

            case SINGLE_QUOTE:
            case DOUBLE_QUOTE:
                quote = code === SINGLE_QUOTE ? '\'' : '"';
                next = pos;
                do {
                    escaped = false;
                    next = css.indexOf(quote, next + 1);
                    if (next === -1) {
                        if (ignore) {
                            next = pos + 1;
                            break;
                        } else {
                            unclosed('string');
                        }
                    }
                    escapePos = next;
                    while (css.charCodeAt(escapePos - 1) === BACKSLASH) {
                        escapePos -= 1;
                        escaped = !escaped;
                    }
                } while (escaped);

                content = css.slice(pos, next + 1);
                lines = content.split('\n');
                last = lines.length - 1;

                if (last > 0) {
                    nextLine = line + last;
                    nextOffset = next - lines[last].length;
                } else {
                    nextLine = line;
                    nextOffset = offset;
                }

                currentToken = ['string', css.slice(pos, next + 1), line, pos - offset, nextLine, next - nextOffset];

                offset = nextOffset;
                line = nextLine;
                pos = next;
                break;

            case AT:
                RE_AT_END.lastIndex = pos + 1;
                RE_AT_END.test(css);
                if (RE_AT_END.lastIndex === 0) {
                    next = css.length - 1;
                } else {
                    next = RE_AT_END.lastIndex - 2;
                }

                currentToken = ['at-word', css.slice(pos, next + 1), line, pos - offset, line, next - offset];

                pos = next;
                break;

            case BACKSLASH:
                next = pos;
                escape = true;
                while (css.charCodeAt(next + 1) === BACKSLASH) {
                    next += 1;
                    escape = !escape;
                }
                code = css.charCodeAt(next + 1);
                if (escape && code !== SLASH && code !== SPACE && code !== NEWLINE && code !== TAB && code !== CR && code !== FEED) {
                    next += 1;
                    if (RE_HEX_ESCAPE.test(css.charAt(next))) {
                        while (RE_HEX_ESCAPE.test(css.charAt(next + 1))) {
                            next += 1;
                        }
                        if (css.charCodeAt(next + 1) === SPACE) {
                            next += 1;
                        }
                    }
                }

                currentToken = ['word', css.slice(pos, next + 1), line, pos - offset, line, next - offset];

                pos = next;
                break;

            default:
                if (code === SLASH && css.charCodeAt(pos + 1) === ASTERISK) {
                    next = css.indexOf('*/', pos + 2) + 1;
                    if (next === 0) {
                        if (ignore) {
                            next = css.length;
                        } else {
                            unclosed('comment');
                        }
                    }

                    content = css.slice(pos, next + 1);
                    lines = content.split('\n');
                    last = lines.length - 1;

                    if (last > 0) {
                        nextLine = line + last;
                        nextOffset = next - lines[last].length;
                    } else {
                        nextLine = line;
                        nextOffset = offset;
                    }

                    currentToken = ['comment', content, line, pos - offset, nextLine, next - nextOffset];

                    offset = nextOffset;
                    line = nextLine;
                    pos = next;
                } else {
                    RE_WORD_END.lastIndex = pos + 1;
                    RE_WORD_END.test(css);
                    if (RE_WORD_END.lastIndex === 0) {
                        next = css.length - 1;
                    } else {
                        next = RE_WORD_END.lastIndex - 2;
                    }

                    currentToken = ['word', css.slice(pos, next + 1), line, pos - offset, line, next - offset];

                    buffer.push(currentToken);

                    pos = next;
                }

                break;
        }

        pos++;
        return currentToken;
    }

    function back(token) {
        returned.push(token);
    }

    return {
        back: back,
        nextToken: nextToken,
        endOfFile: endOfFile
    };
}
module.exports = exports['default'];
//# sourceMappingURL=data:application/json;charset=utf8;base64,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
