{"name": "signal-exit", "version": "3.0.7", "description": "when you want to fire an event no matter how a process exits.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "files": ["index.js", "signals.js"], "repository": {"type": "git", "url": "https://github.com/tapjs/signal-exit.git"}, "keywords": ["signal", "exit"], "author": "<PERSON> <<EMAIL>>", "license": "ISC", "bugs": {"url": "https://github.com/tapjs/signal-exit/issues"}, "homepage": "https://github.com/tapjs/signal-exit", "devDependencies": {"chai": "^3.5.0", "coveralls": "^3.1.1", "nyc": "^15.1.0", "standard-version": "^9.3.1", "tap": "^15.1.1"}}