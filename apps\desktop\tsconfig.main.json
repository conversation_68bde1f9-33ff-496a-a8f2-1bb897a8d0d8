{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "dist", "module": "CommonJS", "target": "ES2020", "lib": ["ES2020"], "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "sourceMap": true, "types": ["node", "electron"]}, "include": ["src/main/**/*"], "exclude": ["node_modules", "dist", "src/renderer"]}