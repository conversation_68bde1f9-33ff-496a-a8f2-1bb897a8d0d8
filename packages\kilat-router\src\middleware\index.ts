import type { KilatMiddleware, MiddlewareContext } from '../types';

// 🔐 Authentication Middleware
export const authMiddleware: KilatMiddleware = async (context: MiddlewareContext) => {
  const { route, navigate, abort } = context;
  
  // Check if route requires authentication
  if (route.meta?.requiresAuth) {
    const token = localStorage.getItem('auth_token');
    const user = localStorage.getItem('user');
    
    if (!token || !user) {
      // Redirect to login page
      navigate('/login', { 
        state: { 
          from: context.pathname,
          message: 'Please log in to access this page'
        }
      });
      return false;
    }
    
    // Validate token (you can add more sophisticated validation)
    try {
      const userData = JSON.parse(user);
      if (!userData.id) {
        throw new Error('Invalid user data');
      }
    } catch (error) {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user');
      navigate('/login', { 
        state: { 
          from: context.pathname,
          message: 'Session expired. Please log in again.'
        }
      });
      return false;
    }
  }
  
  return true;
};

// 🛡️ Role-based Access Control Middleware
export const rbacMiddleware: KilatMiddleware = async (context: MiddlewareContext) => {
  const { route, navigate } = context;
  
  if (route.meta?.roles) {
    const user = localStorage.getItem('user');
    
    if (!user) {
      navigate('/login');
      return false;
    }
    
    try {
      const userData = JSON.parse(user);
      const userRoles = userData.roles || [];
      const requiredRoles = route.meta.roles;
      
      const hasRequiredRole = requiredRoles.some((role: string) => 
        userRoles.includes(role)
      );
      
      if (!hasRequiredRole) {
        navigate('/unauthorized', {
          state: {
            message: 'You do not have permission to access this page',
            requiredRoles,
            userRoles
          }
        });
        return false;
      }
    } catch (error) {
      navigate('/login');
      return false;
    }
  }
  
  return true;
};

// 🌐 Internationalization Middleware
export const i18nMiddleware: KilatMiddleware = async (context: MiddlewareContext) => {
  const { route, params } = context;
  
  // Extract locale from URL params or query
  const locale = params.locale || new URLSearchParams(context.search).get('lang') || 'en';
  
  // Validate locale
  const supportedLocales = ['en', 'id', 'ms', 'th', 'vi', 'ph'];
  if (!supportedLocales.includes(locale)) {
    // Redirect to default locale
    const newPath = context.pathname.replace(/^\/[a-z]{2}/, '/en');
    context.redirect(newPath);
    return false;
  }
  
  // Set locale in global state or context
  if (typeof window !== 'undefined') {
    window.localStorage.setItem('preferred_locale', locale);
    document.documentElement.lang = locale;
    
    // Dispatch locale change event
    window.dispatchEvent(new CustomEvent('kilat:locale-change', {
      detail: { locale, previousLocale: window.localStorage.getItem('current_locale') }
    }));
    
    window.localStorage.setItem('current_locale', locale);
  }
  
  return true;
};

// 📊 Analytics Middleware
export const analyticsMiddleware: KilatMiddleware = async (context: MiddlewareContext) => {
  const { route, pathname } = context;
  
  // Track page view
  if (typeof window !== 'undefined') {
    // Google Analytics
    if (window.gtag) {
      window.gtag('config', 'GA_MEASUREMENT_ID', {
        page_path: pathname,
        page_title: route.meta?.title || document.title
      });
    }
    
    // Custom analytics
    const analyticsData = {
      path: pathname,
      title: route.meta?.title,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      referrer: document.referrer,
      sessionId: sessionStorage.getItem('session_id') || generateSessionId()
    };
    
    // Send to analytics service
    try {
      await fetch('/api/analytics/pageview', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(analyticsData)
      });
    } catch (error) {
      console.warn('Failed to send analytics data:', error);
    }
  }
  
  return true;
};

// 🔄 Cache Middleware
export const cacheMiddleware: KilatMiddleware = async (context: MiddlewareContext) => {
  const { route, pathname } = context;
  
  // Check if route should be cached
  if (route.meta?.cache !== false) {
    const cacheKey = `route_cache_${pathname}`;
    const cached = sessionStorage.getItem(cacheKey);
    
    if (cached) {
      try {
        const cacheData = JSON.parse(cached);
        const now = Date.now();
        const ttl = route.meta?.cacheTTL || 300000; // 5 minutes default
        
        if (now - cacheData.timestamp < ttl) {
          // Use cached data
          console.log('Using cached route data for:', pathname);
          return true;
        } else {
          // Cache expired, remove it
          sessionStorage.removeItem(cacheKey);
        }
      } catch (error) {
        sessionStorage.removeItem(cacheKey);
      }
    }
  }
  
  return true;
};

// 🚦 Rate Limiting Middleware
export const rateLimitMiddleware: KilatMiddleware = async (context: MiddlewareContext) => {
  const { route, pathname, abort } = context;
  
  if (route.meta?.rateLimit) {
    const { requests, window: timeWindow } = route.meta.rateLimit;
    const key = `rate_limit_${pathname}`;
    const now = Date.now();
    
    let requestLog = JSON.parse(localStorage.getItem(key) || '[]');
    
    // Remove old requests outside the time window
    requestLog = requestLog.filter((timestamp: number) => 
      now - timestamp < timeWindow
    );
    
    if (requestLog.length >= requests) {
      abort('Rate limit exceeded. Please try again later.');
      return false;
    }
    
    // Add current request
    requestLog.push(now);
    localStorage.setItem(key, JSON.stringify(requestLog));
  }
  
  return true;
};

// 🔍 SEO Middleware
export const seoMiddleware: KilatMiddleware = async (context: MiddlewareContext) => {
  const { route } = context;
  
  if (typeof window !== 'undefined' && route.meta) {
    // Update document title
    if (route.meta.title) {
      document.title = route.meta.title;
    }
    
    // Update meta tags
    const updateMetaTag = (name: string, content: string) => {
      let meta = document.querySelector(`meta[name="${name}"]`) as HTMLMetaElement;
      if (!meta) {
        meta = document.createElement('meta');
        meta.name = name;
        document.head.appendChild(meta);
      }
      meta.content = content;
    };
    
    if (route.meta.description) {
      updateMetaTag('description', route.meta.description);
    }
    
    if (route.meta.keywords) {
      updateMetaTag('keywords', route.meta.keywords.join(', '));
    }
    
    // Update Open Graph tags
    const updateOGTag = (property: string, content: string) => {
      let meta = document.querySelector(`meta[property="${property}"]`) as HTMLMetaElement;
      if (!meta) {
        meta = document.createElement('meta');
        meta.setAttribute('property', property);
        document.head.appendChild(meta);
      }
      meta.content = content;
    };
    
    if (route.meta.ogTitle) {
      updateOGTag('og:title', route.meta.ogTitle);
    }
    
    if (route.meta.ogDescription) {
      updateOGTag('og:description', route.meta.ogDescription);
    }
    
    if (route.meta.ogImage) {
      updateOGTag('og:image', route.meta.ogImage);
    }
  }
  
  return true;
};

// 🎨 Theme Middleware
export const themeMiddleware: KilatMiddleware = async (context: MiddlewareContext) => {
  const { route } = context;
  
  if (route.meta?.theme && typeof window !== 'undefined') {
    const theme = route.meta.theme;
    
    // Apply theme to document
    document.documentElement.setAttribute('data-kilat-theme', theme);
    
    // Store theme preference
    localStorage.setItem('route_theme', theme);
    
    // Dispatch theme change event
    window.dispatchEvent(new CustomEvent('kilat:theme-change', {
      detail: { theme, route: route.path }
    }));
  }
  
  return true;
};

// 🔧 Development Middleware
export const devMiddleware: KilatMiddleware = async (context: MiddlewareContext) => {
  if (process.env.NODE_ENV === 'development') {
    console.group('🧭 Kilat Router Navigation');
    console.log('Route:', context.route);
    console.log('Params:', context.params);
    console.log('Query:', Object.fromEntries(context.query.entries()));
    console.log('Pathname:', context.pathname);
    console.groupEnd();
  }
  
  return true;
};

// 🛠️ Utility Functions
function generateSessionId(): string {
  const sessionId = Math.random().toString(36).substr(2, 9);
  sessionStorage.setItem('session_id', sessionId);
  return sessionId;
}

// 📦 Middleware Presets
export const middlewarePresets = {
  // Basic web app
  basic: [analyticsMiddleware, seoMiddleware],
  
  // Authenticated app
  auth: [authMiddleware, analyticsMiddleware, seoMiddleware],
  
  // Multi-tenant app with RBAC
  enterprise: [authMiddleware, rbacMiddleware, analyticsMiddleware, seoMiddleware, rateLimitMiddleware],
  
  // International app
  i18n: [i18nMiddleware, analyticsMiddleware, seoMiddleware],
  
  // Development
  dev: [devMiddleware, analyticsMiddleware, seoMiddleware],
  
  // Full featured
  full: [
    authMiddleware,
    rbacMiddleware,
    i18nMiddleware,
    analyticsMiddleware,
    cacheMiddleware,
    rateLimitMiddleware,
    seoMiddleware,
    themeMiddleware
  ]
};

// 🎯 Middleware Factory
export function createMiddleware(
  name: string,
  handler: (context: MiddlewareContext) => Promise<boolean> | boolean
): KilatMiddleware {
  const middleware = handler as KilatMiddleware;
  middleware.displayName = name;
  return middleware;
}

// 📋 Export all middleware
export {
  authMiddleware,
  rbacMiddleware,
  i18nMiddleware,
  analyticsMiddleware,
  cacheMiddleware,
  rateLimitMiddleware,
  seoMiddleware,
  themeMiddleware,
  devMiddleware
};
