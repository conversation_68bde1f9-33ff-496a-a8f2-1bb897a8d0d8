import { pathToRegexp, Key } from 'path-to-regexp';
import type { Ki<PERSON>Route, RouteMatch } from '../types';

// 🎯 Route Matching Utility
export function matchRoute(pathname: string, routes: KilatRoute[]): RouteMatch | null {
  const matches: RouteMatch[] = [];
  
  for (const route of routes) {
    const match = matchSingleRoute(pathname, route);
    if (match) {
      matches.push(match);
    }
  }
  
  // Sort by score (higher is better)
  matches.sort((a, b) => b.score - a.score);
  
  return matches[0] || null;
}

// 🔍 Match single route
export function matchSingleRoute(pathname: string, route: KilatRoute): RouteMatch | null {
  const keys: Key[] = [];
  const regexp = pathToRegexp(route.path, keys, {
    end: route.exact !== false,
    sensitive: route.caseSensitive || false
  });
  
  const match = regexp.exec(pathname);
  
  if (!match) {
    return null;
  }
  
  // Extract parameters
  const params: Record<string, string> = {};
  for (let i = 0; i < keys.length; i++) {
    const key = keys[i];
    const value = match[i + 1];
    if (value !== undefined) {
      params[key.name] = decodeURIComponent(value);
    }
  }
  
  // Calculate match score
  const score = calculateMatchScore(pathname, route, match);
  
  return {
    route,
    params,
    score,
    exact: match[0] === pathname
  };
}

// 📊 Calculate match score for route prioritization
function calculateMatchScore(pathname: string, route: KilatRoute, match: RegExpExecArray): number {
  let score = 0;
  
  // Exact matches get highest score
  if (match[0] === pathname) {
    score += 1000;
  }
  
  // Static segments get higher score than dynamic
  const pathSegments = pathname.split('/').filter(Boolean);
  const routeSegments = route.path.split('/').filter(Boolean);
  
  for (let i = 0; i < Math.min(pathSegments.length, routeSegments.length); i++) {
    const routeSegment = routeSegments[i];
    
    if (!routeSegment.startsWith(':') && !routeSegment.includes('*')) {
      // Static segment
      score += 100;
    } else if (routeSegment.startsWith(':')) {
      // Dynamic segment
      score += 50;
    } else {
      // Catch-all segment
      score += 10;
    }
  }
  
  // Prefer routes with fewer dynamic segments
  const dynamicSegments = routeSegments.filter(s => s.startsWith(':') || s.includes('*')).length;
  score -= dynamicSegments * 5;
  
  // Prefer routes with exact length match
  if (pathSegments.length === routeSegments.length) {
    score += 25;
  }
  
  return score;
}

// 🔍 Find all matching routes (for nested routing)
export function findAllMatches(pathname: string, routes: KilatRoute[]): RouteMatch[] {
  const matches: RouteMatch[] = [];
  
  for (const route of routes) {
    const match = matchSingleRoute(pathname, route);
    if (match) {
      matches.push(match);
    }
  }
  
  return matches.sort((a, b) => b.score - a.score);
}

// 🧩 Match nested routes
export function matchNestedRoutes(pathname: string, routes: KilatRoute[]): RouteMatch[] {
  const matches: RouteMatch[] = [];
  const segments = pathname.split('/').filter(Boolean);
  
  // Build path progressively to find nested matches
  for (let i = 0; i <= segments.length; i++) {
    const currentPath = '/' + segments.slice(0, i).join('/');
    const normalizedPath = currentPath === '/' ? '/' : currentPath;
    
    const match = matchRoute(normalizedPath, routes);
    if (match) {
      matches.push(match);
    }
  }
  
  return matches;
}

// 🎯 Check if route matches exactly
export function isExactMatch(pathname: string, route: KilatRoute): boolean {
  const match = matchSingleRoute(pathname, route);
  return match ? match.exact : false;
}

// 🔄 Generate path from route and params
export function generatePath(route: KilatRoute, params: Record<string, string> = {}): string {
  let path = route.path;
  
  // Replace dynamic segments with actual values
  for (const [key, value] of Object.entries(params)) {
    path = path.replace(`:${key}`, encodeURIComponent(value));
    path = path.replace(`:${key}?`, encodeURIComponent(value));
  }
  
  // Remove optional segments that don't have values
  path = path.replace(/\/:[^\/]*\?/g, '');
  
  // Handle catch-all routes
  if (path.includes('*') && params.slug) {
    const slugValue = Array.isArray(params.slug) ? params.slug.join('/') : params.slug;
    path = path.replace('*', slugValue);
  }
  
  return path;
}

// 🔍 Extract route parameters from path
export function extractParams(pathname: string, route: KilatRoute): Record<string, string> {
  const match = matchSingleRoute(pathname, route);
  return match ? match.params : {};
}

// 📊 Route matching statistics
export interface MatchStats {
  totalRoutes: number;
  matchedRoutes: number;
  exactMatches: number;
  partialMatches: number;
  dynamicMatches: number;
  staticMatches: number;
}

export function getMatchingStats(pathname: string, routes: KilatRoute[]): MatchStats {
  const matches = findAllMatches(pathname, routes);
  
  let exactMatches = 0;
  let partialMatches = 0;
  let dynamicMatches = 0;
  let staticMatches = 0;
  
  for (const match of matches) {
    if (match.exact) {
      exactMatches++;
    } else {
      partialMatches++;
    }
    
    const hasDynamicSegments = match.route.path.includes(':') || match.route.path.includes('*');
    if (hasDynamicSegments) {
      dynamicMatches++;
    } else {
      staticMatches++;
    }
  }
  
  return {
    totalRoutes: routes.length,
    matchedRoutes: matches.length,
    exactMatches,
    partialMatches,
    dynamicMatches,
    staticMatches
  };
}

// 🎯 Validate route path
export function validateRoutePath(path: string): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Check if path starts with /
  if (!path.startsWith('/')) {
    errors.push('Route path must start with "/"');
  }
  
  // Check for invalid characters
  const invalidChars = /[<>:"\\|?*]/;
  if (invalidChars.test(path)) {
    errors.push('Route path contains invalid characters');
  }
  
  // Check for consecutive slashes
  if (path.includes('//')) {
    errors.push('Route path cannot contain consecutive slashes');
  }
  
  // Check for trailing slash (except root)
  if (path.length > 1 && path.endsWith('/')) {
    errors.push('Route path should not end with "/" (except root)');
  }
  
  // Validate dynamic segments
  const dynamicSegments = path.match(/:[^\/]+/g) || [];
  for (const segment of dynamicSegments) {
    const paramName = segment.slice(1);
    if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(paramName.replace('?', ''))) {
      errors.push(`Invalid parameter name: ${paramName}`);
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

// 🔄 Normalize route path
export function normalizeRoutePath(path: string): string {
  // Remove trailing slash (except root)
  if (path.length > 1 && path.endsWith('/')) {
    path = path.slice(0, -1);
  }
  
  // Ensure starts with /
  if (!path.startsWith('/')) {
    path = '/' + path;
  }
  
  // Remove consecutive slashes
  path = path.replace(/\/+/g, '/');
  
  // Handle empty path
  if (path === '' || path === '/') {
    return '/';
  }
  
  return path;
}
