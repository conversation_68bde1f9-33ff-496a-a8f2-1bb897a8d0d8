import * as THREE from 'three';
import type { PresetFactory, HologramConfig } from '../types';

/**
 * Hologram Preset - Futuristic holographic display effect
 * Creates a sci-fi hologram with scan lines and glitch effects
 */
export const HologramPreset: PresetFactory = {
  create: (config: HologramConfig = {}) => {
    const {
      gridSize = 20,
      scanLineSpeed = 2,
      glitchIntensity = 0.1,
      hologramColor = '#00ffff',
      opacity = 0.7,
      wireframe = true,
      scanLines = true,
      glitchEffect = true
    } = config;

    const group = new THREE.Group();
    group.name = 'hologram';

    // 🔷 Create holographic grid
    const gridGeometry = new THREE.PlaneGeometry(gridSize, gridSize, 32, 32);
    const gridMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        color: { value: new THREE.Color(hologramColor) },
        opacity: { value: opacity },
        scanLineSpeed: { value: scanLineSpeed },
        glitchIntensity: { value: glitchIntensity }
      },
      vertexShader: `
        uniform float time;
        uniform float glitchIntensity;
        varying vec2 vUv;
        varying vec3 vPosition;
        
        void main() {
          vUv = uv;
          vPosition = position;
          
          vec3 pos = position;
          
          // Hologram distortion
          pos.z += sin(pos.x * 10.0 + time * 2.0) * 0.1;
          pos.z += sin(pos.y * 15.0 + time * 3.0) * 0.05;
          
          // Glitch effect
          if (glitchIntensity > 0.0) {
            float glitch = sin(time * 50.0) * glitchIntensity;
            pos.x += glitch * step(0.9, sin(pos.y * 100.0));
          }
          
          gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
        }
      `,
      fragmentShader: `
        uniform float time;
        uniform vec3 color;
        uniform float opacity;
        uniform float scanLineSpeed;
        varying vec2 vUv;
        varying vec3 vPosition;
        
        void main() {
          vec2 uv = vUv;
          
          // Grid pattern
          vec2 grid = abs(fract(uv * 20.0) - 0.5);
          float gridLine = smoothstep(0.0, 0.1, grid.x) * smoothstep(0.0, 0.1, grid.y);
          
          // Scan lines
          float scanLine = sin(uv.y * 100.0 + time * scanLineSpeed) * 0.5 + 0.5;
          scanLine = smoothstep(0.3, 0.7, scanLine);
          
          // Hologram flicker
          float flicker = sin(time * 10.0) * 0.1 + 0.9;
          
          // Distance fade
          float dist = length(uv - 0.5);
          float fade = 1.0 - smoothstep(0.3, 0.5, dist);
          
          vec3 finalColor = color * gridLine * scanLine * flicker * fade;
          float finalOpacity = opacity * gridLine * fade;
          
          gl_FragColor = vec4(finalColor, finalOpacity);
        }
      `,
      transparent: true,
      side: THREE.DoubleSide,
      blending: THREE.AdditiveBlending
    });

    const gridMesh = new THREE.Mesh(gridGeometry, gridMaterial);
    group.add(gridMesh);

    // 📡 Create holographic wireframe objects
    if (wireframe) {
      // Rotating cube
      const cubeGeometry = new THREE.BoxGeometry(2, 2, 2);
      const cubeWireframe = new THREE.WireframeGeometry(cubeGeometry);
      const cubeMaterial = new THREE.LineBasicMaterial({ 
        color: hologramColor,
        transparent: true,
        opacity: opacity * 0.8
      });
      const cube = new THREE.LineSegments(cubeWireframe, cubeMaterial);
      cube.position.set(-3, 2, 0);
      group.add(cube);

      // Rotating torus
      const torusGeometry = new THREE.TorusGeometry(1.5, 0.5, 8, 16);
      const torusWireframe = new THREE.WireframeGeometry(torusGeometry);
      const torusMaterial = new THREE.LineBasicMaterial({ 
        color: hologramColor,
        transparent: true,
        opacity: opacity * 0.8
      });
      const torus = new THREE.LineSegments(torusWireframe, torusMaterial);
      torus.position.set(3, 2, 0);
      group.add(torus);

      // Rotating sphere
      const sphereGeometry = new THREE.SphereGeometry(1, 16, 12);
      const sphereWireframe = new THREE.WireframeGeometry(sphereGeometry);
      const sphereMaterial = new THREE.LineBasicMaterial({ 
        color: hologramColor,
        transparent: true,
        opacity: opacity * 0.8
      });
      const sphere = new THREE.LineSegments(sphereWireframe, sphereMaterial);
      sphere.position.set(0, -2, 0);
      group.add(sphere);
    }

    // 📊 Create data visualization elements
    const dataPoints = [];
    for (let i = 0; i < 50; i++) {
      const pointGeometry = new THREE.SphereGeometry(0.05, 8, 6);
      const pointMaterial = new THREE.MeshBasicMaterial({
        color: hologramColor,
        transparent: true,
        opacity: opacity * 0.6
      });
      const point = new THREE.Mesh(pointGeometry, pointMaterial);
      
      point.position.set(
        (Math.random() - 0.5) * gridSize * 0.8,
        (Math.random() - 0.5) * gridSize * 0.8,
        (Math.random() - 0.5) * 2
      );
      
      dataPoints.push(point);
      group.add(point);
    }

    // 🎭 Animation function
    const animate = (time: number) => {
      // Update shader uniforms
      if (gridMaterial.uniforms) {
        gridMaterial.uniforms.time.value = time * 0.001;
      }

      // Rotate wireframe objects
      if (wireframe) {
        const cube = group.children.find(child => child.geometry?.type === 'BufferGeometry' && child.position.x < 0);
        const torus = group.children.find(child => child.geometry?.type === 'BufferGeometry' && child.position.x > 0);
        const sphere = group.children.find(child => child.geometry?.type === 'BufferGeometry' && child.position.y < 0);

        if (cube) {
          cube.rotation.x = time * 0.001;
          cube.rotation.y = time * 0.0015;
        }
        if (torus) {
          torus.rotation.x = time * 0.0012;
          torus.rotation.z = time * 0.0008;
        }
        if (sphere) {
          sphere.rotation.y = time * 0.001;
          sphere.rotation.z = time * 0.0007;
        }
      }

      // Animate data points
      dataPoints.forEach((point, index) => {
        const offset = index * 0.1;
        point.position.y += Math.sin(time * 0.002 + offset) * 0.01;
        point.material.opacity = (Math.sin(time * 0.003 + offset) * 0.3 + 0.7) * opacity * 0.6;
      });

      // Glitch effect
      if (glitchEffect && Math.random() < 0.01) {
        group.position.x = (Math.random() - 0.5) * glitchIntensity;
        group.position.y = (Math.random() - 0.5) * glitchIntensity;
        
        setTimeout(() => {
          group.position.x = 0;
          group.position.y = 0;
        }, 50);
      }
    };

    // Add animation function to group
    (group as any).animate = animate;

    return group;
  },

  dispose: (object: THREE.Object3D) => {
    object.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        if (child.geometry) child.geometry.dispose();
        if (child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach(material => material.dispose());
          } else {
            child.material.dispose();
          }
        }
      }
    });
  }
};

export default HologramPreset;
