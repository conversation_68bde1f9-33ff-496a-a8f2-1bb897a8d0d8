import { createLogger } from 'kilat-utils';
import type { KilatPlugin, MonitoringPluginConfig, KilatContextValue } from '../types';

/**
 * 📊 Monitoring Plugin for Kilat.js
 * Comprehensive monitoring, metrics, and alerting system
 */
export class MonitoringPlugin implements KilatPlugin {
  name = 'monitoring';
  version = '1.0.0';
  description = 'Comprehensive monitoring and metrics collection for Kilat.js';
  author = 'KangPCode';
  
  private config: MonitoringPluginConfig;
  private logger = createLogger({ prefix: 'MonitoringPlugin' });
  private metrics = new Map<string, Metric>();
  private alerts = new Map<string, Alert>();
  private collectors: MetricCollector[] = [];
  private isCollecting = false;

  constructor(config: MonitoringPluginConfig = {}) {
    this.config = {
      enabled: true,
      metrics: [
        { name: 'http_requests_total', type: 'counter', description: 'Total HTTP requests' },
        { name: 'http_request_duration', type: 'histogram', description: 'HTTP request duration' },
        { name: 'memory_usage', type: 'gauge', description: 'Memory usage in bytes' },
        { name: 'cpu_usage', type: 'gauge', description: 'CPU usage percentage' }
      ],
      alerts: [
        {
          name: 'high_memory_usage',
          condition: 'memory_usage > 1000000000',
          threshold: 1000000000,
          severity: 'high',
          channels: ['console', 'webhook']
        },
        {
          name: 'high_error_rate',
          condition: 'error_rate > 0.05',
          threshold: 0.05,
          severity: 'critical',
          channels: ['console', 'webhook', 'email']
        }
      ],
      retention: {
        metrics: '7d',
        logs: '30d',
        traces: '3d'
      },
      ...config
    };
  }

  // 🚀 Plugin initialization
  async onInit(context: KilatContextValue): Promise<void> {
    this.logger.info('Initializing Monitoring Plugin...');
    
    // Setup metrics
    await this.setupMetrics();
    
    // Setup alerts
    await this.setupAlerts();
    
    // Setup collectors
    await this.setupCollectors();
    
    // Start monitoring
    await this.startMonitoring();
    
    this.logger.success('Monitoring Plugin initialized successfully');
  }

  // 📊 Setup metrics
  private async setupMetrics(): Promise<void> {
    const metricsConfig = this.config.metrics || [];
    
    for (const metricConfig of metricsConfig) {
      const metric = this.createMetric(metricConfig);
      this.metrics.set(metricConfig.name, metric);
      this.logger.debug(`Metric ${metricConfig.name} registered`);
    }
  }

  // 🚨 Setup alerts
  private async setupAlerts(): Promise<void> {
    const alertsConfig = this.config.alerts || [];
    
    for (const alertConfig of alertsConfig) {
      const alert = this.createAlert(alertConfig);
      this.alerts.set(alertConfig.name, alert);
      this.logger.debug(`Alert ${alertConfig.name} configured`);
    }
  }

  // 🔧 Setup metric collectors
  private async setupCollectors(): Promise<void> {
    // System metrics collector
    this.collectors.push(new SystemMetricsCollector(this));
    
    // HTTP metrics collector
    this.collectors.push(new HTTPMetricsCollector(this));
    
    // Database metrics collector
    this.collectors.push(new DatabaseMetricsCollector(this));
    
    // Custom metrics collector
    this.collectors.push(new CustomMetricsCollector(this));
    
    this.logger.info(`${this.collectors.length} metric collectors configured`);
  }

  // 🚀 Start monitoring
  private async startMonitoring(): Promise<void> {
    if (this.isCollecting) return;
    
    this.isCollecting = true;
    
    // Start metric collection
    for (const collector of this.collectors) {
      await collector.start();
    }
    
    // Start alert checking
    this.startAlertChecking();
    
    this.logger.info('Monitoring started');
  }

  // 🛑 Stop monitoring
  async stopMonitoring(): Promise<void> {
    if (!this.isCollecting) return;
    
    this.isCollecting = false;
    
    // Stop metric collection
    for (const collector of this.collectors) {
      await collector.stop();
    }
    
    this.logger.info('Monitoring stopped');
  }

  // 📈 Record metric
  recordMetric(name: string, value: number, labels: Record<string, string> = {}): void {
    const metric = this.metrics.get(name);
    if (!metric) {
      this.logger.warn(`Metric ${name} not found`);
      return;
    }

    metric.record(value, labels);
  }

  // 📊 Get metric value
  getMetric(name: string): MetricValue | null {
    const metric = this.metrics.get(name);
    return metric ? metric.getValue() : null;
  }

  // 📋 Get all metrics
  getAllMetrics(): Record<string, MetricValue> {
    const result: Record<string, MetricValue> = {};
    
    for (const [name, metric] of this.metrics) {
      result[name] = metric.getValue();
    }
    
    return result;
  }

  // 🚨 Check alerts
  private startAlertChecking(): void {
    setInterval(() => {
      this.checkAlerts();
    }, 30000); // Check every 30 seconds
  }

  private async checkAlerts(): Promise<void> {
    for (const [name, alert] of this.alerts) {
      try {
        const shouldTrigger = await this.evaluateAlertCondition(alert);
        
        if (shouldTrigger && !alert.isTriggered) {
          await this.triggerAlert(alert);
        } else if (!shouldTrigger && alert.isTriggered) {
          await this.resolveAlert(alert);
        }
      } catch (error) {
        this.logger.error(`Error checking alert ${name}:`, error);
      }
    }
  }

  // 🔍 Evaluate alert condition
  private async evaluateAlertCondition(alert: Alert): Promise<boolean> {
    // Simple condition evaluation - in production use proper expression parser
    const condition = alert.condition;
    
    // Extract metric name from condition
    const metricMatch = condition.match(/(\w+)\s*([><=]+)\s*(\d+(?:\.\d+)?)/);
    if (!metricMatch) {
      return false;
    }

    const [, metricName, operator, thresholdStr] = metricMatch;
    const threshold = parseFloat(thresholdStr);
    const metricValue = this.getMetric(metricName);
    
    if (!metricValue) {
      return false;
    }

    const currentValue = metricValue.value;
    
    switch (operator) {
      case '>':
        return currentValue > threshold;
      case '<':
        return currentValue < threshold;
      case '>=':
        return currentValue >= threshold;
      case '<=':
        return currentValue <= threshold;
      case '==':
        return currentValue === threshold;
      default:
        return false;
    }
  }

  // 🚨 Trigger alert
  private async triggerAlert(alert: Alert): Promise<void> {
    alert.isTriggered = true;
    alert.triggeredAt = new Date();
    
    this.logger.warn(`Alert triggered: ${alert.name}`);
    
    // Send notifications
    for (const channel of alert.channels) {
      await this.sendAlertNotification(alert, channel, 'triggered');
    }
  }

  // ✅ Resolve alert
  private async resolveAlert(alert: Alert): Promise<void> {
    alert.isTriggered = false;
    alert.resolvedAt = new Date();
    
    this.logger.info(`Alert resolved: ${alert.name}`);
    
    // Send notifications
    for (const channel of alert.channels) {
      await this.sendAlertNotification(alert, channel, 'resolved');
    }
  }

  // 📤 Send alert notification
  private async sendAlertNotification(
    alert: Alert, 
    channel: string, 
    action: 'triggered' | 'resolved'
  ): Promise<void> {
    const message = `Alert ${alert.name} ${action} - Severity: ${alert.severity}`;
    
    switch (channel) {
      case 'console':
        if (action === 'triggered') {
          console.error(`🚨 ${message}`);
        } else {
          console.log(`✅ ${message}`);
        }
        break;
        
      case 'webhook':
        await this.sendWebhookNotification(alert, action);
        break;
        
      case 'email':
        await this.sendEmailNotification(alert, action);
        break;
        
      default:
        this.logger.warn(`Unknown notification channel: ${channel}`);
    }
  }

  // 🌐 Send webhook notification
  private async sendWebhookNotification(alert: Alert, action: string): Promise<void> {
    // Implementation would send HTTP request to webhook URL
    this.logger.debug(`Webhook notification sent for alert ${alert.name}`);
  }

  // 📧 Send email notification
  private async sendEmailNotification(alert: Alert, action: string): Promise<void> {
    // Implementation would send email notification
    this.logger.debug(`Email notification sent for alert ${alert.name}`);
  }

  // 🏭 Create metric
  private createMetric(config: any): Metric {
    switch (config.type) {
      case 'counter':
        return new CounterMetric(config.name, config.description);
      case 'gauge':
        return new GaugeMetric(config.name, config.description);
      case 'histogram':
        return new HistogramMetric(config.name, config.description);
      case 'summary':
        return new SummaryMetric(config.name, config.description);
      default:
        throw new Error(`Unknown metric type: ${config.type}`);
    }
  }

  // 🏭 Create alert
  private createAlert(config: any): Alert {
    return {
      name: config.name,
      condition: config.condition,
      threshold: config.threshold,
      severity: config.severity,
      channels: config.channels,
      isTriggered: false
    };
  }

  // 📊 Get monitoring dashboard data
  getDashboardData(): MonitoringDashboard {
    const metrics = this.getAllMetrics();
    const alerts = Array.from(this.alerts.values());
    const activeAlerts = alerts.filter(alert => alert.isTriggered);
    
    return {
      metrics,
      alerts: {
        total: alerts.length,
        active: activeAlerts.length,
        triggered: activeAlerts
      },
      system: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        cpu: process.cpuUsage()
      },
      timestamp: new Date()
    };
  }

  // 🧹 Plugin cleanup
  async onDestroy(): Promise<void> {
    await this.stopMonitoring();
    this.metrics.clear();
    this.alerts.clear();
    this.collectors = [];
    this.logger.info('Monitoring Plugin destroyed');
  }
}

// 📊 Metric Classes
abstract class Metric {
  constructor(
    public name: string,
    public description: string
  ) {}

  abstract record(value: number, labels?: Record<string, string>): void;
  abstract getValue(): MetricValue;
}

class CounterMetric extends Metric {
  private value = 0;

  record(value: number): void {
    this.value += value;
  }

  getValue(): MetricValue {
    return {
      type: 'counter',
      value: this.value,
      timestamp: new Date()
    };
  }
}

class GaugeMetric extends Metric {
  private value = 0;

  record(value: number): void {
    this.value = value;
  }

  getValue(): MetricValue {
    return {
      type: 'gauge',
      value: this.value,
      timestamp: new Date()
    };
  }
}

class HistogramMetric extends Metric {
  private values: number[] = [];

  record(value: number): void {
    this.values.push(value);
    // Keep only last 1000 values
    if (this.values.length > 1000) {
      this.values = this.values.slice(-1000);
    }
  }

  getValue(): MetricValue {
    const sorted = [...this.values].sort((a, b) => a - b);
    const count = sorted.length;
    
    return {
      type: 'histogram',
      value: count > 0 ? sorted[Math.floor(count * 0.5)] : 0, // median
      timestamp: new Date(),
      histogram: {
        count,
        sum: sorted.reduce((a, b) => a + b, 0),
        buckets: this.calculateBuckets(sorted)
      }
    };
  }

  private calculateBuckets(values: number[]): Record<string, number> {
    // Simple bucket calculation
    const buckets: Record<string, number> = {};
    const max = Math.max(...values);
    const bucketSize = max / 10;
    
    for (let i = 0; i < 10; i++) {
      const upperBound = (i + 1) * bucketSize;
      buckets[upperBound.toString()] = values.filter(v => v <= upperBound).length;
    }
    
    return buckets;
  }
}

class SummaryMetric extends Metric {
  private values: number[] = [];

  record(value: number): void {
    this.values.push(value);
    if (this.values.length > 1000) {
      this.values = this.values.slice(-1000);
    }
  }

  getValue(): MetricValue {
    const sorted = [...this.values].sort((a, b) => a - b);
    const count = sorted.length;
    
    return {
      type: 'summary',
      value: count > 0 ? sorted.reduce((a, b) => a + b, 0) / count : 0, // average
      timestamp: new Date(),
      summary: {
        count,
        sum: sorted.reduce((a, b) => a + b, 0),
        quantiles: {
          '0.5': count > 0 ? sorted[Math.floor(count * 0.5)] : 0,
          '0.9': count > 0 ? sorted[Math.floor(count * 0.9)] : 0,
          '0.99': count > 0 ? sorted[Math.floor(count * 0.99)] : 0
        }
      }
    };
  }
}

// 🔧 Metric Collectors
abstract class MetricCollector {
  constructor(protected plugin: MonitoringPlugin) {}
  
  abstract start(): Promise<void>;
  abstract stop(): Promise<void>;
}

class SystemMetricsCollector extends MetricCollector {
  private interval?: NodeJS.Timeout;

  async start(): Promise<void> {
    this.interval = setInterval(() => {
      const memUsage = process.memoryUsage();
      this.plugin.recordMetric('memory_usage', memUsage.heapUsed);
      this.plugin.recordMetric('memory_total', memUsage.heapTotal);
    }, 5000);
  }

  async stop(): Promise<void> {
    if (this.interval) {
      clearInterval(this.interval);
    }
  }
}

class HTTPMetricsCollector extends MetricCollector {
  async start(): Promise<void> {
    // Would integrate with HTTP server to collect request metrics
  }

  async stop(): Promise<void> {
    // Cleanup HTTP metric collection
  }
}

class DatabaseMetricsCollector extends MetricCollector {
  async start(): Promise<void> {
    // Would integrate with database to collect query metrics
  }

  async stop(): Promise<void> {
    // Cleanup database metric collection
  }
}

class CustomMetricsCollector extends MetricCollector {
  async start(): Promise<void> {
    // Custom application metrics
  }

  async stop(): Promise<void> {
    // Cleanup custom metrics
  }
}

// 🔧 Types
interface MetricValue {
  type: string;
  value: number;
  timestamp: Date;
  histogram?: {
    count: number;
    sum: number;
    buckets: Record<string, number>;
  };
  summary?: {
    count: number;
    sum: number;
    quantiles: Record<string, number>;
  };
}

interface Alert {
  name: string;
  condition: string;
  threshold: number;
  severity: string;
  channels: string[];
  isTriggered: boolean;
  triggeredAt?: Date;
  resolvedAt?: Date;
}

interface MonitoringDashboard {
  metrics: Record<string, MetricValue>;
  alerts: {
    total: number;
    active: number;
    triggered: Alert[];
  };
  system: {
    uptime: number;
    memory: NodeJS.MemoryUsage;
    cpu: NodeJS.CpuUsage;
  };
  timestamp: Date;
}
