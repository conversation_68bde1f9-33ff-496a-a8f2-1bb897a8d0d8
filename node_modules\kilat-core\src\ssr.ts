/**
 * 🌐 Kilat.js SSR (Server-Side Rendering)
 * Server-side rendering utilities and helpers
 */

import { ReactElement } from 'react';

/**
 * 🎯 SSR Configuration
 */
export interface SSRConfig {
  enabled: boolean;
  hydrate: boolean;
  preloadData: boolean;
  cacheStrategy: 'none' | 'static' | 'dynamic';
  timeout: number;
}

/**
 * 🔧 SSR Context
 */
export interface SSRContext {
  url: string;
  userAgent: string;
  headers: Record<string, string>;
  cookies: Record<string, string>;
  isBot: boolean;
  device: 'mobile' | 'tablet' | 'desktop';
}

/**
 * 📊 SSR Result
 */
export interface SSRResult {
  html: string;
  css: string;
  scripts: string[];
  meta: {
    title: string;
    description: string;
    keywords: string[];
    ogTags: Record<string, string>;
  };
  preloadedData: any;
  performance: {
    renderTime: number;
    memoryUsage: number;
  };
}

/**
 * 🌐 SSR Renderer
 */
export class KilatSSRRenderer {
  private config: SSRConfig;

  constructor(config: Partial<SSRConfig> = {}) {
    this.config = {
      enabled: true,
      hydrate: true,
      preloadData: true,
      cacheStrategy: 'dynamic',
      timeout: 5000,
      ...config
    };
  }

  /**
   * 🎨 Render React component to HTML
   */
  async renderToString(
    element: ReactElement,
    context: SSRContext
  ): Promise<SSRResult> {
    const startTime = performance.now();
    
    try {
      // Mock implementation - in real scenario would use ReactDOMServer
      const html = this.mockRenderToString(element);
      const css = this.extractCSS();
      const scripts = this.getScripts();
      const meta = this.extractMeta(element);
      const preloadedData = await this.preloadData(context);
      
      const endTime = performance.now();
      
      return {
        html,
        css,
        scripts,
        meta,
        preloadedData,
        performance: {
          renderTime: endTime - startTime,
          memoryUsage: this.getMemoryUsage()
        }
      };
    } catch (error) {
      throw new Error(`SSR rendering failed: ${error}`);
    }
  }

  /**
   * 🔄 Hydrate client-side
   */
  hydrate(element: ReactElement, container: Element): void {
    if (!this.config.hydrate) return;
    
    // Mock implementation - in real scenario would use ReactDOM.hydrate
    console.log('🔄 Hydrating Kilat.js app...');
  }

  /**
   * 📦 Preload data for SSR
   */
  private async preloadData(context: SSRContext): Promise<any> {
    if (!this.config.preloadData) return null;
    
    // Mock data preloading
    return {
      user: null,
      theme: 'cyberpunk',
      locale: 'en',
      timestamp: Date.now()
    };
  }

  /**
   * 🎨 Extract CSS from components
   */
  private extractCSS(): string {
    // Mock CSS extraction
    return `
      :root {
        --kilat-primary: #00ffff;
        --kilat-secondary: #ff0040;
        --kilat-background: #000011;
      }
      
      .kilat-app {
        font-family: system-ui, -apple-system, sans-serif;
        background: var(--kilat-background);
        color: var(--kilat-primary);
        min-height: 100vh;
      }
    `;
  }

  /**
   * 📜 Get required scripts
   */
  private getScripts(): string[] {
    return [
      '/assets/kilat-core.js',
      '/assets/kilat-router.js',
      '/assets/app.js'
    ];
  }

  /**
   * 🏷️ Extract meta tags from component
   */
  private extractMeta(element: ReactElement): SSRResult['meta'] {
    // Mock meta extraction
    return {
      title: 'Kilat.js App',
      description: 'Lightning-fast full-stack framework',
      keywords: ['kilat', 'react', 'framework', 'ssr'],
      ogTags: {
        'og:title': 'Kilat.js App',
        'og:description': 'Lightning-fast full-stack framework',
        'og:type': 'website',
        'og:image': '/assets/og-image.png'
      }
    };
  }

  /**
   * 🧠 Get memory usage
   */
  private getMemoryUsage(): number {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      return process.memoryUsage().heapUsed;
    }
    return 0;
  }

  /**
   * 🎭 Mock render to string
   */
  private mockRenderToString(element: ReactElement): string {
    return `
      <!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Kilat.js App</title>
          <style id="kilat-ssr-styles"></style>
        </head>
        <body>
          <div id="kilat-root">
            <div class="kilat-app">
              <!-- SSR rendered content would go here -->
              <h1>⚡ Kilat.js SSR</h1>
              <p>Server-side rendered content</p>
            </div>
          </div>
          <script id="kilat-preloaded-data" type="application/json"></script>
        </body>
      </html>
    `;
  }
}

/**
 * 🔧 SSR Utilities
 */
export function createSSRContext(req: any): SSRContext {
  const userAgent = req.headers['user-agent'] || '';
  const isBot = /bot|crawler|spider/i.test(userAgent);
  
  let device: 'mobile' | 'tablet' | 'desktop' = 'desktop';
  if (/mobile/i.test(userAgent)) device = 'mobile';
  else if (/tablet|ipad/i.test(userAgent)) device = 'tablet';

  return {
    url: req.url || '/',
    userAgent,
    headers: req.headers || {},
    cookies: req.cookies || {},
    isBot,
    device
  };
}

export function isSSR(): boolean {
  return typeof window === 'undefined';
}

export function getSSRData<T = any>(key: string): T | null {
  if (isSSR()) return null;
  
  try {
    const script = document.getElementById('kilat-preloaded-data');
    if (!script) return null;
    
    const data = JSON.parse(script.textContent || '{}');
    return data[key] || null;
  } catch {
    return null;
  }
}

/**
 * 🎯 Default SSR renderer instance
 */
export const ssrRenderer = new KilatSSRRenderer();

/**
 * 🚀 Quick SSR render function
 */
export async function renderKilatApp(
  element: ReactElement,
  context: SSRContext,
  config?: Partial<SSRConfig>
): Promise<SSRResult> {
  const renderer = new KilatSSRRenderer(config);
  return renderer.renderToString(element, context);
}
