name: 🚀 Kilat.js CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
  release:
    types: [published]

env:
  BUN_VERSION: '1.0.0'
  NODE_VERSION: '18'

jobs:
  # 🧪 Testing and Quality Checks
  test:
    name: 🧪 Test & Quality
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        bun-version: ['1.0.0', 'latest']
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🟢 Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: ${{ matrix.bun-version }}

      - name: 📦 Install dependencies
        run: bun install --frozen-lockfile

      - name: 🔍 Type check
        run: bun run type-check

      - name: 🧹 Lint check
        run: bun run lint

      - name: 🎨 Format check
        run: bun run format --check

      - name: 🧪 Run unit tests
        run: bun run test

      - name: 📊 Upload coverage
        uses: codecov/codecov-action@v3
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          files: ./coverage/lcov.info

  # 🏗️ Build Packages
  build:
    name: 🏗️ Build Packages
    runs-on: ubuntu-latest
    needs: test
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🟢 Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: 📦 Install dependencies
        run: bun install --frozen-lockfile

      - name: 🏗️ Build packages
        run: bun run build:packages

      - name: 📦 Cache build artifacts
        uses: actions/cache@v3
        with:
          path: |
            packages/*/dist
            packages/*/build
          key: build-${{ github.sha }}
          restore-keys: |
            build-${{ github.ref_name }}-

  # 🌐 Build Web App
  build-web:
    name: 🌐 Build Web App
    runs-on: ubuntu-latest
    needs: build
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🟢 Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: 📦 Install dependencies
        run: bun install --frozen-lockfile

      - name: 📦 Restore build cache
        uses: actions/cache@v3
        with:
          path: |
            packages/*/dist
            packages/*/build
          key: build-${{ github.sha }}

      - name: 🌐 Build web app
        run: bun run --filter='@kilat/web-demo' build
        env:
          NODE_ENV: production

      - name: 📤 Upload web build
        uses: actions/upload-artifact@v3
        with:
          name: web-build
          path: apps/web/dist

  # 🖥️ Build Desktop App
  build-desktop:
    name: 🖥️ Build Desktop App
    runs-on: ${{ matrix.os }}
    needs: build
    
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🟢 Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: 📦 Install dependencies
        run: bun install --frozen-lockfile

      - name: 🖥️ Build desktop app
        run: bun run --filter='@kilat/desktop-demo' build

      - name: 📦 Package desktop app
        run: bun run --filter='@kilat/desktop-demo' package

      - name: 📤 Upload desktop build
        uses: actions/upload-artifact@v3
        with:
          name: desktop-build-${{ matrix.os }}
          path: apps/desktop/dist

  # 📱 Build Mobile App
  build-mobile:
    name: 📱 Build Mobile App
    runs-on: ubuntu-latest
    needs: build
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🟢 Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: 📦 Install dependencies
        run: bun install --frozen-lockfile

      - name: 📱 Build mobile app
        run: bun run --filter='@kilat/mobile-demo' build

  # 🧪 E2E Testing
  e2e:
    name: 🧪 E2E Tests
    runs-on: ubuntu-latest
    needs: build-web
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🟢 Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: 📦 Install dependencies
        run: bun install --frozen-lockfile

      - name: 📥 Download web build
        uses: actions/download-artifact@v3
        with:
          name: web-build
          path: apps/web/dist

      - name: 🎭 Install Playwright
        run: bunx playwright install --with-deps

      - name: 🧪 Run E2E tests
        run: bun run test:e2e

      - name: 📤 Upload E2E results
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: e2e-results
          path: test-results/

  # 🏥 Health Check
  health-check:
    name: 🏥 Health Check
    runs-on: ubuntu-latest
    needs: [build-web, build-desktop, build-mobile]
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🟢 Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: 📦 Install dependencies
        run: bun install --frozen-lockfile

      - name: 🏥 Run health check
        run: bun run doctor

  # 🚀 Deploy to Staging
  deploy-staging:
    name: 🚀 Deploy Staging
    runs-on: ubuntu-latest
    needs: [e2e, health-check]
    if: github.ref == 'refs/heads/develop'
    
    environment:
      name: staging
      url: https://staging.kilat.js.org
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📥 Download web build
        uses: actions/download-artifact@v3
        with:
          name: web-build
          path: apps/web/dist

      - name: 🚀 Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: apps/web

  # 🌟 Deploy to Production
  deploy-production:
    name: 🌟 Deploy Production
    runs-on: ubuntu-latest
    needs: [e2e, health-check]
    if: github.ref == 'refs/heads/main'
    
    environment:
      name: production
      url: https://kilat.js.org
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📥 Download web build
        uses: actions/download-artifact@v3
        with:
          name: web-build
          path: apps/web/dist

      - name: 🌟 Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
          working-directory: apps/web

  # 📦 Publish Packages
  publish:
    name: 📦 Publish Packages
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: github.event_name == 'release'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🟢 Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: 📦 Install dependencies
        run: bun install --frozen-lockfile

      - name: 🏗️ Build packages
        run: bun run build:packages

      - name: 📦 Publish to NPM
        run: bun run publish:packages
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

  # 📊 Performance Monitoring
  lighthouse:
    name: 📊 Lighthouse Audit
    runs-on: ubuntu-latest
    needs: deploy-staging
    if: github.ref == 'refs/heads/develop'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📊 Run Lighthouse
        uses: treosh/lighthouse-ci-action@v9
        with:
          urls: |
            https://staging.kilat.js.org
            https://staging.kilat.js.org/demo
            https://staging.kilat.js.org/docs
          configPath: ./.lighthouserc.json
          uploadArtifacts: true
          temporaryPublicStorage: true

  # 🔔 Notifications
  notify:
    name: 🔔 Notifications
    runs-on: ubuntu-latest
    needs: [deploy-production, publish]
    if: always()
    
    steps:
      - name: 🔔 Discord Notification
        uses: Ilshidur/action-discord@master
        env:
          DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}
        with:
          args: |
            ⚡ **Kilat.js Deployment Complete!**
            
            🚀 **Status**: ${{ job.status }}
            📝 **Commit**: ${{ github.event.head_commit.message }}
            👤 **Author**: ${{ github.actor }}
            🌐 **Environment**: Production
            🔗 **URL**: https://kilat.js.org
            
            ${{ job.status == 'success' && '✅ All systems operational!' || '❌ Deployment failed!' }}
