/**
 * 🧩 Kilat.js Core Components
 * Essential UI components for the framework
 */

import React from 'react';

/**
 * 🔄 Loading Component
 */
export interface KilatLoadingProps {
  size?: 'small' | 'medium' | 'large';
  color?: string;
  text?: string;
  className?: string;
}

export function KilatLoading({ 
  size = 'medium', 
  color = '#00ffff', 
  text = 'Loading...', 
  className = '' 
}: KilatLoadingProps) {
  const sizeMap = {
    small: '24px',
    medium: '48px',
    large: '72px'
  };

  return (
    <div 
      className={`kilat-loading ${className}`}
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '1rem'
      }}
    >
      <div
        style={{
          width: sizeMap[size],
          height: sizeMap[size],
          border: `3px solid transparent`,
          borderTop: `3px solid ${color}`,
          borderRadius: '50%',
          animation: 'kilat-spin 1s linear infinite'
        }}
      />
      {text && (
        <div style={{ color, fontSize: '0.9rem' }}>
          {text}
        </div>
      )}
      <style>{`
        @keyframes kilat-spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}

/**
 * ⚠️ Error Component
 */
export interface KilatErrorProps {
  error: Error;
  retry?: () => void;
  className?: string;
}

export function KilatError({ error, retry, className = '' }: KilatErrorProps) {
  return (
    <div 
      className={`kilat-error ${className}`}
      style={{
        padding: '2rem',
        border: '1px solid #ff0040',
        borderRadius: '8px',
        background: 'rgba(255, 0, 64, 0.1)',
        color: '#ff0040',
        textAlign: 'center'
      }}
    >
      <h3 style={{ marginBottom: '1rem', color: '#ff0040' }}>
        ⚠️ Something went wrong
      </h3>
      <p style={{ marginBottom: '1rem', color: '#cccccc' }}>
        {error.message}
      </p>
      {retry && (
        <button
          onClick={retry}
          style={{
            background: '#ff0040',
            color: '#ffffff',
            border: 'none',
            padding: '0.5rem 1rem',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          🔄 Try Again
        </button>
      )}
    </div>
  );
}

/**
 * 🎯 Container Component
 */
export interface KilatContainerProps {
  children: React.ReactNode;
  maxWidth?: string;
  padding?: string;
  className?: string;
}

export function KilatContainer({ 
  children, 
  maxWidth = '1200px', 
  padding = '1rem',
  className = '' 
}: KilatContainerProps) {
  return (
    <div 
      className={`kilat-container ${className}`}
      style={{
        maxWidth,
        margin: '0 auto',
        padding,
        width: '100%'
      }}
    >
      {children}
    </div>
  );
}

/**
 * 📱 Responsive Component
 */
export interface KilatResponsiveProps {
  children: React.ReactNode;
  breakpoint?: 'mobile' | 'tablet' | 'desktop';
  className?: string;
}

export function KilatResponsive({ 
  children, 
  breakpoint = 'mobile',
  className = '' 
}: KilatResponsiveProps) {
  const [matches, setMatches] = React.useState(false);

  React.useEffect(() => {
    const breakpoints = {
      mobile: '(max-width: 768px)',
      tablet: '(max-width: 1024px)',
      desktop: '(min-width: 1025px)'
    };

    const mediaQuery = window.matchMedia(breakpoints[breakpoint]);
    setMatches(mediaQuery.matches);

    const handler = (e: MediaQueryListEvent) => setMatches(e.matches);
    mediaQuery.addEventListener('change', handler);

    return () => mediaQuery.removeEventListener('change', handler);
  }, [breakpoint]);

  if (!matches) return null;

  return (
    <div className={`kilat-responsive kilat-responsive-${breakpoint} ${className}`}>
      {children}
    </div>
  );
}

/**
 * 🎨 Theme Provider Component
 */
export interface KilatThemeProviderProps {
  children: React.ReactNode;
  theme?: string;
  className?: string;
}

export function KilatThemeProvider({ 
  children, 
  theme = 'cyberpunk',
  className = '' 
}: KilatThemeProviderProps) {
  return (
    <div 
      className={`kilat-theme kilat-theme-${theme} ${className}`}
      data-theme={theme}
    >
      {children}
    </div>
  );
}

/**
 * 🔧 Debug Component
 */
export interface KilatDebugProps {
  data: any;
  title?: string;
  className?: string;
}

export function KilatDebug({ data, title = 'Debug Info', className = '' }: KilatDebugProps) {
  if (process.env.NODE_ENV === 'production') return null;

  return (
    <div 
      className={`kilat-debug ${className}`}
      style={{
        position: 'fixed',
        bottom: '1rem',
        right: '1rem',
        background: 'rgba(0, 0, 0, 0.9)',
        color: '#00ffff',
        padding: '1rem',
        borderRadius: '8px',
        border: '1px solid #00ffff',
        fontSize: '0.8rem',
        maxWidth: '300px',
        maxHeight: '200px',
        overflow: 'auto',
        zIndex: 9999
      }}
    >
      <h4 style={{ marginBottom: '0.5rem', color: '#00ffff' }}>
        🔧 {title}
      </h4>
      <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}
