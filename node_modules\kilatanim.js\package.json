{"name": "kilatanim.js", "version": "1.0.0", "description": "⚡ KilatAnim.js - 3D Animation Presets with Three.js for Kilat.js Framework", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.esm.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./presets/*": {"import": "./dist/presets/*.esm.js", "require": "./dist/presets/*.js", "types": "./dist/presets/*.d.ts"}}, "files": ["dist", "README.md"], "scripts": {"build": "bun run build:esm && bun run build:cjs && bun run build:types", "build:esm": "esbuild src/index.ts --bundle --format=esm --outfile=dist/index.esm.js --external:react --external:react-dom --external:three --external:@react-three/fiber --external:@react-three/drei", "build:cjs": "esbuild src/index.ts --bundle --format=cjs --outfile=dist/index.js --external:react --external:react-dom --external:three --external:@react-three/fiber --external:@react-three/drei", "build:types": "tsc --emitDeclarationOnly --outDir dist", "dev": "bun run build --watch", "test": "bun test", "clean": "rm -rf dist"}, "keywords": ["kilat", "animation", "3d", "threejs", "react", "presets", "galaxy", "matrix", "cyberpunk", "glow"], "author": "KangPCode", "license": "MIT", "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "three": "^0.158.0"}, "dependencies": {"@react-three/fiber": "^8.15.0", "@react-three/drei": "^9.88.0", "three": "^0.158.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/three": "^0.158.0", "esbuild": "^0.19.0", "typescript": "^5.3.0"}}