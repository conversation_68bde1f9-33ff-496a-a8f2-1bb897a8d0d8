{"name": "tr46", "version": "0.0.3", "description": "An implementation of the Unicode TR46 spec", "main": "index.js", "scripts": {"test": "mocha", "pretest": "node scripts/getLatestUnicodeTests.js", "prepublish": "node scripts/generateMappingTable.js"}, "repository": {"type": "git", "url": "git+https://github.com/Sebmaster/tr46.js.git"}, "keywords": ["unicode", "tr46", "url", "whatwg"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/Sebmaster/tr46.js/issues"}, "homepage": "https://github.com/Sebmaster/tr46.js#readme", "devDependencies": {"mocha": "^2.2.5", "request": "^2.57.0"}}