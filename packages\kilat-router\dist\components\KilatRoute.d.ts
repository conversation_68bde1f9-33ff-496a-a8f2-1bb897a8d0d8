import React from 'react';

/**
 * 🛣️ KilatRoute Component
 * Enhanced Route component with Kilat.js features
 */
export interface KilatRouteProps {
  path?: string;
  element?: React.ReactElement;
  index?: boolean;
  middleware?: string[];
  layout?: React.ComponentType<any>;
  meta?: {
    title?: string;
    description?: string;
    keywords?: string[];
  };
}

export declare function KilatRoute(props: KilatRouteProps): React.ReactElement;
export default KilatRoute;
