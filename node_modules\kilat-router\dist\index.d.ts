/**
 * 🧭 Kilat.js Router - Simple and powerful routing
 */

// Re-export React Router components for convenience
export {
  BrowserRouter,
  Routes,
  Route,
  Link,
  Navigate,
  Outlet,
  useNavigate,
  useLocation,
  useParams,
  useSearchParams
} from 'react-router-dom';

// Export Kilat-specific components (simple versions)
export { KilatRoute, KilatRouteProps } from './components/KilatRoute';
export { KilatLink, KilatLinkProps } from './components/KilatLink';
export { KilatOutlet, KilatOutletProps } from './components/KilatOutlet';
export { NestedLayout, NestedLayoutProps } from './components/NestedLayout';

// Version info
export declare const KILAT_ROUTER_VERSION: string;
