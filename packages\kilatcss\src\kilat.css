/**
 * KilatCSS ⚡ - Utility-first CSS Framework with Glow Effects
 * Framework Glow Futuristik Nusantara
 */

/* 🎯 CSS Custom Properties (CSS Variables) */
:root {
  /* 🎨 Color Palette */
  --k-primary: #00ffff;
  --k-secondary: #ff00ff;
  --k-accent: #ffff00;
  --k-background: #000000;
  --k-surface: #111111;
  --k-text: #ffffff;
  --k-text-muted: #888888;
  
  /* 🌈 Neon Colors */
  --k-neon-blue: #00ffff;
  --k-neon-pink: #ff00ff;
  --k-neon-green: #00ff00;
  --k-neon-yellow: #ffff00;
  --k-neon-purple: #8000ff;
  --k-neon-orange: #ff8000;
  --k-neon-red: #ff0040;
  
  /* 🔮 Glow Effects */
  --k-glow-sm: 0 0 5px currentColor;
  --k-glow-md: 0 0 10px currentColor, 0 0 20px currentColor;
  --k-glow-lg: 0 0 15px currentColor, 0 0 30px currentColor, 0 0 45px currentColor;
  --k-glow-xl: 0 0 20px currentColor, 0 0 40px currentColor, 0 0 60px currentColor, 0 0 80px currentColor;
  
  /* 📏 Spacing */
  --k-space-1: 0.25rem;
  --k-space-2: 0.5rem;
  --k-space-3: 0.75rem;
  --k-space-4: 1rem;
  --k-space-5: 1.25rem;
  --k-space-6: 1.5rem;
  --k-space-8: 2rem;
  --k-space-10: 2.5rem;
  --k-space-12: 3rem;
  --k-space-16: 4rem;
  --k-space-20: 5rem;
  --k-space-24: 6rem;
  
  /* 🔤 Typography */
  --k-font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --k-font-mono: 'Fira Code', 'JetBrains Mono', Consolas, monospace;
  --k-font-display: 'Orbitron', 'Exo 2', sans-serif;
  
  /* 🎭 Animations */
  --k-transition-fast: 150ms ease;
  --k-transition-normal: 300ms ease;
  --k-transition-slow: 500ms ease;
  
  /* 🌊 Border Radius */
  --k-radius-sm: 0.125rem;
  --k-radius-md: 0.375rem;
  --k-radius-lg: 0.5rem;
  --k-radius-xl: 0.75rem;
  --k-radius-full: 9999px;
  
  /* 🎯 Z-Index */
  --k-z-dropdown: 1000;
  --k-z-sticky: 1020;
  --k-z-fixed: 1030;
  --k-z-modal: 1040;
  --k-z-popover: 1050;
  --k-z-tooltip: 1060;
}

/* 🌙 Dark Mode Variables */
[data-kilat-mode="dark"] {
  --k-background: #000000;
  --k-surface: #111111;
  --k-text: #ffffff;
  --k-text-muted: #888888;
}

/* ☀️ Light Mode Variables */
[data-kilat-mode="light"] {
  --k-background: #ffffff;
  --k-surface: #f8f9fa;
  --k-text: #000000;
  --k-text-muted: #666666;
}

/* 🎯 Base Styles */
.kilat,
.kilat * {
  box-sizing: border-box;
}

.kilat {
  font-family: var(--k-font-sans);
  line-height: 1.5;
  color: var(--k-text);
  background-color: var(--k-background);
  transition: background-color var(--k-transition-normal), color var(--k-transition-normal);
}

/* 📦 Import Utilities */
@import './utilities/spacing.css';
@import './utilities/colors.css';
@import './utilities/typography.css';
@import './utilities/layout.css';
@import './utilities/effects.css';
@import './utilities/animations.css';

/* 🧱 Import Components */
@import './components/index.css';

/* 🎨 Import Themes */
@import './themes/cyberpunk.css';
@import './themes/nusantara.css';
@import './themes/retro.css';
@import './themes/material.css';
@import './themes/neumorphism.css';
@import './themes/carbon.css';
@import './themes/minimalist.css';
@import './themes/asymetric.css';
@import './themes/elemen3d.css';
@import './themes/dana.css';
@import './themes/ark.css';
@import './themes/aurora.css';
@import './themes/unix.css';
@import './themes/classic.css';

/* 🔮 Glow Utilities */
.k-glow {
  filter: drop-shadow(var(--k-glow-md));
}

.k-glow-sm {
  filter: drop-shadow(var(--k-glow-sm));
}

.k-glow-lg {
  filter: drop-shadow(var(--k-glow-lg));
}

.k-glow-xl {
  filter: drop-shadow(var(--k-glow-xl));
}

/* 🌈 Text Glow Colors */
.k-text-glow {
  text-shadow: var(--k-glow-md);
}

.k-text-glow-blue {
  color: var(--k-neon-blue);
  text-shadow: var(--k-glow-md);
}

.k-text-glow-pink {
  color: var(--k-neon-pink);
  text-shadow: var(--k-glow-md);
}

.k-text-glow-green {
  color: var(--k-neon-green);
  text-shadow: var(--k-glow-md);
}

.k-text-glow-yellow {
  color: var(--k-neon-yellow);
  text-shadow: var(--k-glow-md);
}

.k-text-glow-purple {
  color: var(--k-neon-purple);
  text-shadow: var(--k-glow-md);
}

.k-text-glow-orange {
  color: var(--k-neon-orange);
  text-shadow: var(--k-glow-md);
}

.k-text-glow-red {
  color: var(--k-neon-red);
  text-shadow: var(--k-glow-md);
}

/* 🎭 Animations */
@keyframes k-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes k-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes k-bounce {
  0%, 100% { transform: translateY(-25%); animation-timing-function: cubic-bezier(0.8, 0, 1, 1); }
  50% { transform: translateY(0); animation-timing-function: cubic-bezier(0, 0, 0.2, 1); }
}

@keyframes k-glitch {
  0% { transform: translate(0); }
  20% { transform: translate(-2px, 2px); }
  40% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  80% { transform: translate(2px, -2px); }
  100% { transform: translate(0); }
}

.k-animate-pulse { animation: k-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
.k-animate-spin { animation: k-spin 1s linear infinite; }
.k-animate-bounce { animation: k-bounce 1s infinite; }
.k-animate-glitch { animation: k-glitch 0.3s ease-in-out infinite; }

/* 🎯 Responsive Design */
@media (max-width: 640px) {
  .k-sm\:hidden { display: none; }
}

@media (max-width: 768px) {
  .k-md\:hidden { display: none; }
}

@media (max-width: 1024px) {
  .k-lg\:hidden { display: none; }
}

@media (max-width: 1280px) {
  .k-xl\:hidden { display: none; }
}

/* 🎨 Themes */
@import './themes/cyberpunk.css';
@import './themes/nusantara.css';
@import './themes/retro.css';
@import './themes/material.css';
@import './themes/neumorphism.css';
@import './themes/aurora.css';
@import './themes/carbon.css';
@import './themes/unix.css';
