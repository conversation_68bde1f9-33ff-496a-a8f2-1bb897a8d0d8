import React from 'react';
import { Outlet, OutletProps } from 'react-router-dom';

/**
 * 🚪 KilatOutlet Component
 * Enhanced Outlet component with Kilat.js features
 */
export interface KilatOutletProps extends OutletProps {
  loading?: React.ComponentType;
  error?: React.ComponentType<{ error: Error }>;
  fallback?: React.ReactNode;
}

export function KilatOutlet({ 
  loading: LoadingComponent, 
  error: ErrorComponent,
  fallback,
  ...props 
}: KilatOutletProps) {
  return (
    <React.Suspense 
      fallback={
        LoadingComponent ? <LoadingComponent /> : 
        fallback || <div className="k-loading">Loading...</div>
      }
    >
      <Outlet {...props} />
    </React.Suspense>
  );
}

export default KilatOutlet;
