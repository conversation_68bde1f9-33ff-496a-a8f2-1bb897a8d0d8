{"name": "kilatpack", "version": "1.0.0", "description": "⚡ KilatPack - Fast build engine for Kilat.js with HMR and debugging", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "bin": {"kilatpack": "./bin/kilatpack.js"}, "exports": {".": {"import": "./dist/index.esm.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./dev": {"import": "./dist/dev.esm.js", "require": "./dist/dev.js", "types": "./dist/dev.d.ts"}, "./build": {"import": "./dist/build.esm.js", "require": "./dist/build.js", "types": "./dist/build.d.ts"}}, "files": ["dist", "bin", "README.md"], "scripts": {"build": "bun run build:esm && bun run build:cjs && bun run build:types", "build:esm": "esbuild src/index.ts --bundle --format=esm --outfile=dist/index.esm.js --external:esbuild --external:vite", "build:cjs": "esbuild src/index.ts --bundle --format=cjs --outfile=dist/index.js --external:esbuild --external:vite", "build:types": "tsc --emitDeclarationOnly --outDir dist", "dev": "bun run build --watch", "test": "bun test", "clean": "rm -rf dist"}, "keywords": ["kilat", "build", "bundler", "esbuild", "vite", "hmr", "development", "typescript"], "author": "Kilat.js Team", "license": "MIT", "dependencies": {"esbuild": "^0.19.8", "vite": "^5.0.0", "rollup": "^4.6.0", "chokidar": "^3.5.3", "ws": "^8.14.2", "connect": "^3.7.0", "sirv": "^2.0.3", "picocolors": "^1.0.0", "kilat-utils": "workspace:*"}, "devDependencies": {"typescript": "^5.3.0", "@types/ws": "^8.5.8", "@types/connect": "^3.4.38"}, "peerDependencies": {"typescript": "^5.0.0"}}