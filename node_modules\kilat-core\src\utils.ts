/**
 * 🔧 Kilat.js Core Utilities
 * Essential utility functions for the framework
 */

/**
 * 🎯 Type Guards
 */
export function isString(value: unknown): value is string {
  return typeof value === 'string';
}

export function isNumber(value: unknown): value is number {
  return typeof value === 'number' && !isNaN(value);
}

export function isObject(value: unknown): value is Record<string, unknown> {
  return typeof value === 'object' && value !== null && !Array.isArray(value);
}

export function isFunction(value: unknown): value is Function {
  return typeof value === 'function';
}

/**
 * 🌐 Environment Detection
 */
export function isBrowser(): boolean {
  return typeof window !== 'undefined';
}

export function isNode(): boolean {
  return typeof window === 'undefined' && typeof process !== 'undefined';
}

export function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development';
}

export function isProduction(): boolean {
  return process.env.NODE_ENV === 'production';
}

/**
 * 🎨 CSS Utilities
 */
export function cn(...classes: (string | undefined | null | false)[]): string {
  return classes.filter(Boolean).join(' ');
}

export function createCSSVariables(theme: Record<string, string>): string {
  return Object.entries(theme)
    .map(([key, value]) => `--kilat-${key}: ${value};`)
    .join('\n');
}

/**
 * 🔄 Async Utilities
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export function timeout<T>(promise: Promise<T>, ms: number): Promise<T> {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) => 
      setTimeout(() => reject(new Error('Timeout')), ms)
    )
  ]);
}

export async function retry<T>(
  fn: () => Promise<T>, 
  maxRetries: number = 3, 
  delayMs: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      if (i < maxRetries) {
        await delay(delayMs * Math.pow(2, i)); // Exponential backoff
      }
    }
  }
  
  throw lastError!;
}

/**
 * 📊 Performance Utilities
 */
export function measurePerformance<T>(
  fn: () => T, 
  label?: string
): T {
  const start = performance.now();
  const result = fn();
  const end = performance.now();
  
  if (label && isDevelopment()) {
    console.log(`⚡ ${label}: ${(end - start).toFixed(2)}ms`);
  }
  
  return result;
}

export async function measureAsyncPerformance<T>(
  fn: () => Promise<T>, 
  label?: string
): Promise<T> {
  const start = performance.now();
  const result = await fn();
  const end = performance.now();
  
  if (label && isDevelopment()) {
    console.log(`⚡ ${label}: ${(end - start).toFixed(2)}ms`);
  }
  
  return result;
}

/**
 * 🎲 Random Utilities
 */
export function randomId(length: number = 8): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

export function randomColor(): string {
  const colors = ['#00ffff', '#ff0040', '#00ff80', '#ff8000', '#8000ff'];
  return colors[Math.floor(Math.random() * colors.length)];
}

/**
 * 📱 Device Utilities
 */
export function getDeviceInfo() {
  if (!isBrowser()) {
    return {
      type: 'server',
      mobile: false,
      tablet: false,
      desktop: true,
      userAgent: ''
    };
  }

  const userAgent = navigator.userAgent.toLowerCase();
  const mobile = /mobile|android|iphone/.test(userAgent);
  const tablet = /tablet|ipad/.test(userAgent);
  const desktop = !mobile && !tablet;

  return {
    type: mobile ? 'mobile' : tablet ? 'tablet' : 'desktop',
    mobile,
    tablet,
    desktop,
    userAgent: navigator.userAgent
  };
}

export function getViewportSize() {
  if (!isBrowser()) {
    return { width: 0, height: 0 };
  }

  return {
    width: window.innerWidth,
    height: window.innerHeight
  };
}

/**
 * 🔍 Debug Utilities
 */
export function createLogger(prefix: string = 'Kilat') {
  return {
    info: (message: string, ...args: any[]) => {
      if (isDevelopment()) {
        console.log(`🔵 [${prefix}] ${message}`, ...args);
      }
    },
    warn: (message: string, ...args: any[]) => {
      if (isDevelopment()) {
        console.warn(`🟡 [${prefix}] ${message}`, ...args);
      }
    },
    error: (message: string, ...args: any[]) => {
      console.error(`🔴 [${prefix}] ${message}`, ...args);
    },
    debug: (message: string, ...args: any[]) => {
      if (isDevelopment()) {
        console.debug(`🔧 [${prefix}] ${message}`, ...args);
      }
    }
  };
}

/**
 * 🎯 Object Utilities
 */
export function deepMerge<T extends Record<string, any>>(
  target: T, 
  source: Partial<T>
): T {
  const result = { ...target };
  
  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      const sourceValue = source[key];
      const targetValue = result[key];
      
      if (isObject(sourceValue) && isObject(targetValue)) {
        result[key] = deepMerge(targetValue, sourceValue);
      } else {
        result[key] = sourceValue as T[Extract<keyof T, string>];
      }
    }
  }
  
  return result;
}

export function pick<T extends Record<string, any>, K extends keyof T>(
  obj: T, 
  keys: K[]
): Pick<T, K> {
  const result = {} as Pick<T, K>;
  keys.forEach(key => {
    if (key in obj) {
      result[key] = obj[key];
    }
  });
  return result;
}

export function omit<T extends Record<string, any>, K extends keyof T>(
  obj: T, 
  keys: K[]
): Omit<T, K> {
  const result = { ...obj };
  keys.forEach(key => {
    delete result[key];
  });
  return result;
}

/**
 * 🎨 Theme Utilities
 */
export function generateThemeCSS(theme: Record<string, string>): string {
  const cssVars = createCSSVariables(theme);
  return `:root {\n${cssVars}\n}`;
}

export function applyTheme(theme: Record<string, string>): void {
  if (!isBrowser()) return;
  
  const root = document.documentElement;
  Object.entries(theme).forEach(([key, value]) => {
    root.style.setProperty(`--kilat-${key}`, value);
  });
}
