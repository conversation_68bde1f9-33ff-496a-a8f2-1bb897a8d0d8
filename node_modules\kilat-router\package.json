{"name": "kilat-router", "version": "1.0.0", "description": "⚡ Kilat.js Router - File-based routing system with layout nesting and middleware", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.esm.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./components": {"import": "./dist/components.esm.js", "require": "./dist/components.js", "types": "./dist/components.d.ts"}}, "files": ["dist", "README.md"], "scripts": {"build": "bun run build:esm && bun run build:cjs && bun run build:types", "build:esm": "esbuild src/index.ts --bundle --format=esm --outfile=dist/index.esm.js --external:react --external:react-dom --external:react-router-dom", "build:cjs": "esbuild src/index.ts --bundle --format=cjs --outfile=dist/index.js --external:react --external:react-dom --external:react-router-dom", "build:types": "tsc --emitDeclarationOnly --outDir dist", "dev": "bun run build --watch", "test": "bun test", "clean": "rm -rf dist"}, "keywords": ["kilat", "router", "routing", "file-based", "react", "nextjs-like"], "author": "KangPCode", "license": "MIT", "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dependencies": {"react-router-dom": "^6.20.0", "path-to-regexp": "^6.2.1", "kilat-core": "workspace:*", "kilat-utils": "workspace:*"}, "devDependencies": {"esbuild": "^0.19.8", "typescript": "^5.3.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0"}}