import * as THREE from 'three';
import type { PresetFactory, PlasmaConfig } from '../types';

/**
 * Plasma Preset - Flowing plasma energy effect
 * Creates dynamic plasma waves with electric energy
 */
export const PlasmaPreset: PresetFactory = {
  create: (config: PlasmaConfig = {}) => {
    const {
      intensity = 1.0,
      speed = 1.0,
      colors = ['#ff0080', '#8000ff', '#0080ff', '#00ff80'],
      resolution = 128,
      turbulence = 0.5,
      electricArcs = true,
      pulseEffect = true
    } = config;

    const group = new THREE.Group();
    group.name = 'plasma';

    // 🌊 Create plasma surface
    const plasmaGeometry = new THREE.PlaneGeometry(20, 20, resolution, resolution);
    const plasmaMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        intensity: { value: intensity },
        speed: { value: speed },
        turbulence: { value: turbulence },
        color1: { value: new THREE.Color(colors[0]) },
        color2: { value: new THREE.Color(colors[1]) },
        color3: { value: new THREE.Color(colors[2]) },
        color4: { value: new THREE.Color(colors[3]) },
        resolution: { value: new THREE.Vector2(resolution, resolution) }
      },
      vertexShader: `
        uniform float time;
        uniform float intensity;
        uniform float speed;
        uniform float turbulence;
        varying vec2 vUv;
        varying vec3 vPosition;
        varying float vElevation;
        
        // Noise function
        vec3 mod289(vec3 x) {
          return x - floor(x * (1.0 / 289.0)) * 289.0;
        }
        
        vec4 mod289(vec4 x) {
          return x - floor(x * (1.0 / 289.0)) * 289.0;
        }
        
        vec4 permute(vec4 x) {
          return mod289(((x*34.0)+1.0)*x);
        }
        
        vec4 taylorInvSqrt(vec4 r) {
          return 1.79284291400159 - 0.85373472095314 * r;
        }
        
        float snoise(vec3 v) {
          const vec2 C = vec2(1.0/6.0, 1.0/3.0);
          const vec4 D = vec4(0.0, 0.5, 1.0, 2.0);
          
          vec3 i = floor(v + dot(v, C.yyy));
          vec3 x0 = v - i + dot(i, C.xxx);
          
          vec3 g = step(x0.yzx, x0.xyz);
          vec3 l = 1.0 - g;
          vec3 i1 = min(g.xyz, l.zxy);
          vec3 i2 = max(g.xyz, l.zxy);
          
          vec3 x1 = x0 - i1 + C.xxx;
          vec3 x2 = x0 - i2 + C.yyy;
          vec3 x3 = x0 - D.yyy;
          
          i = mod289(i);
          vec4 p = permute(permute(permute(
                    i.z + vec4(0.0, i1.z, i2.z, 1.0))
                  + i.y + vec4(0.0, i1.y, i2.y, 1.0))
                  + i.x + vec4(0.0, i1.x, i2.x, 1.0));
          
          float n_ = 0.142857142857;
          vec3 ns = n_ * D.wyz - D.xzx;
          
          vec4 j = p - 49.0 * floor(p * ns.z * ns.z);
          
          vec4 x_ = floor(j * ns.z);
          vec4 y_ = floor(j - 7.0 * x_);
          
          vec4 x = x_ *ns.x + ns.yyyy;
          vec4 y = y_ *ns.x + ns.yyyy;
          vec4 h = 1.0 - abs(x) - abs(y);
          
          vec4 b0 = vec4(x.xy, y.xy);
          vec4 b1 = vec4(x.zw, y.zw);
          
          vec4 s0 = floor(b0)*2.0 + 1.0;
          vec4 s1 = floor(b1)*2.0 + 1.0;
          vec4 sh = -step(h, vec4(0.0));
          
          vec4 a0 = b0.xzyw + s0.xzyw*sh.xxyy;
          vec4 a1 = b1.xzyw + s1.xzyw*sh.zzww;
          
          vec3 p0 = vec3(a0.xy, h.x);
          vec3 p1 = vec3(a0.zw, h.y);
          vec3 p2 = vec3(a1.xy, h.z);
          vec3 p3 = vec3(a1.zw, h.w);
          
          vec4 norm = taylorInvSqrt(vec4(dot(p0,p0), dot(p1,p1), dot(p2, p2), dot(p3,p3)));
          p0 *= norm.x;
          p1 *= norm.y;
          p2 *= norm.z;
          p3 *= norm.w;
          
          vec4 m = max(0.6 - vec4(dot(x0,x0), dot(x1,x1), dot(x2,x2), dot(x3,x3)), 0.0);
          m = m * m;
          return 42.0 * dot(m*m, vec4(dot(p0,x0), dot(p1,x1), dot(p2,x2), dot(p3,x3)));
        }
        
        void main() {
          vUv = uv;
          vPosition = position;
          
          vec3 pos = position;
          
          // Create plasma waves
          float noise1 = snoise(vec3(pos.x * 0.5, pos.y * 0.5, time * speed * 0.5));
          float noise2 = snoise(vec3(pos.x * 1.0, pos.y * 1.0, time * speed * 0.7));
          float noise3 = snoise(vec3(pos.x * 2.0, pos.y * 2.0, time * speed * 1.0));
          
          float elevation = (noise1 * 0.5 + noise2 * 0.3 + noise3 * 0.2) * intensity * turbulence;
          pos.z += elevation;
          
          vElevation = elevation;
          
          gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
        }
      `,
      fragmentShader: `
        uniform float time;
        uniform float intensity;
        uniform float speed;
        uniform vec3 color1;
        uniform vec3 color2;
        uniform vec3 color3;
        uniform vec3 color4;
        varying vec2 vUv;
        varying vec3 vPosition;
        varying float vElevation;
        
        void main() {
          vec2 uv = vUv;
          
          // Create plasma pattern
          float plasma1 = sin(uv.x * 10.0 + time * speed * 2.0);
          float plasma2 = sin(uv.y * 8.0 + time * speed * 1.5);
          float plasma3 = sin((uv.x + uv.y) * 6.0 + time * speed * 3.0);
          float plasma4 = sin(sqrt(pow(uv.x - 0.5, 2.0) + pow(uv.y - 0.5, 2.0)) * 20.0 + time * speed * 2.5);
          
          float plasma = (plasma1 + plasma2 + plasma3 + plasma4) * 0.25;
          
          // Color mixing based on plasma value and elevation
          vec3 color;
          float t = (plasma + vElevation * 2.0) * 0.5 + 0.5;
          
          if (t < 0.33) {
            color = mix(color1, color2, t * 3.0);
          } else if (t < 0.66) {
            color = mix(color2, color3, (t - 0.33) * 3.0);
          } else {
            color = mix(color3, color4, (t - 0.66) * 3.0);
          }
          
          // Add energy glow
          float glow = abs(plasma) * intensity;
          color += color * glow * 0.5;
          
          // Add transparency based on intensity
          float alpha = 0.8 + glow * 0.2;
          
          gl_FragColor = vec4(color, alpha);
        }
      `,
      transparent: true,
      side: THREE.DoubleSide,
      blending: THREE.AdditiveBlending
    });

    const plasmaMesh = new THREE.Mesh(plasmaGeometry, plasmaMaterial);
    group.add(plasmaMesh);

    // ⚡ Create electric arcs
    if (electricArcs) {
      const arcCount = 8;
      const arcs = [];

      for (let i = 0; i < arcCount; i++) {
        const points = [];
        const segments = 20;
        
        for (let j = 0; j <= segments; j++) {
          const t = j / segments;
          const x = (Math.random() - 0.5) * 15;
          const y = (Math.random() - 0.5) * 15;
          const z = Math.sin(t * Math.PI) * 2 + (Math.random() - 0.5) * 0.5;
          points.push(new THREE.Vector3(x, y, z));
        }

        const arcGeometry = new THREE.BufferGeometry().setFromPoints(points);
        const arcMaterial = new THREE.LineBasicMaterial({
          color: colors[i % colors.length],
          transparent: true,
          opacity: 0.6,
          linewidth: 2
        });

        const arc = new THREE.Line(arcGeometry, arcMaterial);
        arcs.push(arc);
        group.add(arc);
      }

      // Store arcs for animation
      (group as any).arcs = arcs;
    }

    // 💫 Create energy particles
    const particleCount = 200;
    const particleGeometry = new THREE.BufferGeometry();
    const particlePositions = new Float32Array(particleCount * 3);
    const particleColors = new Float32Array(particleCount * 3);
    const particleSizes = new Float32Array(particleCount);

    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;
      
      particlePositions[i3] = (Math.random() - 0.5) * 25;
      particlePositions[i3 + 1] = (Math.random() - 0.5) * 25;
      particlePositions[i3 + 2] = Math.random() * 5;
      
      const color = new THREE.Color(colors[Math.floor(Math.random() * colors.length)]);
      particleColors[i3] = color.r;
      particleColors[i3 + 1] = color.g;
      particleColors[i3 + 2] = color.b;
      
      particleSizes[i] = Math.random() * 0.1 + 0.05;
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));
    particleGeometry.setAttribute('color', new THREE.BufferAttribute(particleColors, 3));
    particleGeometry.setAttribute('size', new THREE.BufferAttribute(particleSizes, 1));

    const particleMaterial = new THREE.PointsMaterial({
      size: 0.1,
      transparent: true,
      opacity: 0.8,
      vertexColors: true,
      blending: THREE.AdditiveBlending,
      sizeAttenuation: true
    });

    const particles = new THREE.Points(particleGeometry, particleMaterial);
    group.add(particles);

    // 🎭 Animation function
    const animate = (time: number) => {
      // Update plasma shader
      if (plasmaMaterial.uniforms) {
        plasmaMaterial.uniforms.time.value = time * 0.001;
      }

      // Animate electric arcs
      if (electricArcs && (group as any).arcs) {
        (group as any).arcs.forEach((arc: THREE.Line, index: number) => {
          const material = arc.material as THREE.LineBasicMaterial;
          material.opacity = 0.3 + Math.sin(time * 0.005 + index) * 0.3;
          
          // Occasionally regenerate arc paths
          if (Math.random() < 0.01) {
            const positions = arc.geometry.attributes.position.array as Float32Array;
            for (let i = 0; i < positions.length; i += 3) {
              positions[i] += (Math.random() - 0.5) * 0.5;
              positions[i + 1] += (Math.random() - 0.5) * 0.5;
            }
            arc.geometry.attributes.position.needsUpdate = true;
          }
        });
      }

      // Animate particles
      const positions = particles.geometry.attributes.position.array as Float32Array;
      for (let i = 0; i < particleCount; i++) {
        const i3 = i * 3;
        positions[i3 + 2] += 0.02;
        
        if (positions[i3 + 2] > 5) {
          positions[i3 + 2] = -2;
          positions[i3] = (Math.random() - 0.5) * 25;
          positions[i3 + 1] = (Math.random() - 0.5) * 25;
        }
      }
      particles.geometry.attributes.position.needsUpdate = true;

      // Pulse effect
      if (pulseEffect) {
        const pulse = Math.sin(time * 0.003) * 0.2 + 1.0;
        group.scale.setScalar(pulse);
      }
    };

    // Add animation function to group
    (group as any).animate = animate;

    return group;
  },

  dispose: (object: THREE.Object3D) => {
    object.traverse((child) => {
      if (child instanceof THREE.Mesh || child instanceof THREE.Points || child instanceof THREE.Line) {
        if (child.geometry) child.geometry.dispose();
        if (child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach(material => material.dispose());
          } else {
            child.material.dispose();
          }
        }
      }
    });
  }
};

export default PlasmaPreset;
