import { ReactNode } from 'react';

// 🎨 Theme Types
export type KilatTheme = 
  | 'cyberpunk' 
  | 'nusantara' 
  | 'retro' 
  | 'material' 
  | 'neumorphism' 
  | 'carbon' 
  | 'minimalist' 
  | 'asymetric' 
  | 'elemen3d' 
  | 'dana' 
  | 'ark' 
  | 'aurora' 
  | 'unix' 
  | 'classic';

export type KilatMode = 'dark' | 'light' | 'auto';

// 🌌 Animation Types
export type KilatAnimationPreset = 
  | 'galaxy' 
  | 'matrix' 
  | 'neonTunnel' 
  | 'cyberwave' 
  | 'glowParticles';

export interface KilatAnimationConfig {
  autoRotate: boolean;
  background: string;
  interactive: boolean;
  ambientSound: boolean;
}

// 🔀 Router Types
export interface KilatRouterConfig {
  basePath: string;
  middleware: string[];
  fileBasedRouting: boolean;
  dynamicRoutes: boolean;
  layoutNesting: boolean;
}

// 🗃️ Database Types
export type KilatDatabaseDriver = 'sqlite' | 'mysql';

export interface KilatDatabaseConfig {
  driver: KilatDatabaseDriver;
  connection: {
    sqlite?: {
      file: string;
      enableWAL?: boolean;
      timeout?: number;
    };
    mysql?: {
      host: string;
      port?: number;
      user: string;
      password: string;
      database: string;
      ssl?: boolean;
    };
  };
  migrations: {
    directory: string;
    autoRun: boolean;
  };
}

// 🔧 Build Types
export type KilatBuildEngine = 'kilatpack' | 'vite' | 'webpack';

export interface KilatBuildConfig {
  engine: KilatBuildEngine;
  target: string;
  minify: boolean;
  sourcemap: boolean;
  debugOverlay: boolean;
  hotReload: boolean;
  analyze: boolean;
  outputDir: string;
  publicDir: string;
  assetsDir: string;
}

// 🌐 Platform Types
export interface KilatPlatformConfig {
  web: {
    enabled: boolean;
    ssr: boolean;
    pwa: boolean;
  };
  desktop: {
    enabled: boolean;
    electron: boolean;
    tauri: boolean;
  };
  mobile: {
    enabled: boolean;
    expo: boolean;
    capacitor: boolean;
  };
}

// 🤖 AI Assistant Types
export interface KilatAIConfig {
  enabled: boolean;
  endpoint: string;
  model: string;
  features: string[];
}

// 📊 Development Types
export interface KilatDevConfig {
  port: number;
  host: string;
  open: boolean;
  cors: boolean;
  proxy: Record<string, string>;
  hmr: {
    port: number;
    overlay: boolean;
  };
}

// 🎯 Performance Types
export interface KilatPerformanceConfig {
  bundleSplitting: boolean;
  treeshaking: boolean;
  compression: 'gzip' | 'brotli' | 'none';
  caching: {
    enabled: boolean;
    strategy: string;
  };
}

// 🔒 Security Types
export interface KilatSecurityConfig {
  csp: {
    enabled: boolean;
    directives: Record<string, string[]>;
  };
  cors: {
    origin: string[];
    credentials: boolean;
  };
}

// 📦 Main Config Type
export interface KilatConfig {
  theme: KilatTheme;
  mode: KilatMode;
  presetScene: KilatAnimationPreset;
  animation: KilatAnimationConfig;
  router: KilatRouterConfig;
  database: KilatDatabaseConfig;
  build: KilatBuildConfig;
  plugins: string[];
  aiAssistant: KilatAIConfig;
  platform: KilatPlatformConfig;
  dev: KilatDevConfig;
  performance: KilatPerformanceConfig;
  security: KilatSecurityConfig;
}

// 🧱 Layout Types
export interface KilatLayoutProps {
  children: ReactNode;
  theme?: KilatTheme;
  mode?: KilatMode;
  className?: string;
}

// 🎯 Context Types
export interface KilatContextValue {
  config: KilatConfig;
  theme: KilatTheme;
  mode: KilatMode;
  setTheme: (theme: KilatTheme) => void;
  setMode: (mode: KilatMode) => void;
  isLoading: boolean;
  error: Error | null;
}

// 🔌 Plugin Types
export interface KilatPlugin {
  name: string;
  version?: string;
  onInit?: (ctx: KilatContextValue) => void | Promise<void>;
  onRequest?: (ctx: any) => void | Promise<void>;
  onResponse?: (ctx: any) => void | Promise<void>;
  onError?: (error: Error, ctx: any) => void | Promise<void>;
}

// 🌐 Platform Detection Types
export type KilatPlatformType = 'web' | 'desktop' | 'mobile';

export interface KilatPlatformInfo {
  type: KilatPlatformType;
  isWeb: boolean;
  isDesktop: boolean;
  isMobile: boolean;
  userAgent: string;
  viewport: {
    width: number;
    height: number;
  };
}
