{"name": "is-path-inside", "version": "4.0.0", "description": "Check if a path is inside another path", "license": "MIT", "repository": "sindresorhus/is-path-inside", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["path", "inside", "folder", "directory", "file", "resolve"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.39.1"}}