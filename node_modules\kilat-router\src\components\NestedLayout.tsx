import React from 'react';
import { Outlet } from 'react-router-dom';

/**
 * 🏗️ NestedLayout Component
 * Layout component for nested routes
 */
export interface NestedLayoutProps {
  children?: React.ReactNode;
  header?: React.ComponentType;
  footer?: React.ComponentType;
  sidebar?: React.ComponentType;
  className?: string;
}

export function NestedLayout({ 
  children,
  header: Header,
  footer: Footer,
  sidebar: Sidebar,
  className = ''
}: NestedLayoutProps) {
  return (
    <div className={`k-nested-layout ${className}`}>
      {Header && (
        <header className="k-layout-header">
          <Header />
        </header>
      )}
      
      <div className="k-layout-main">
        {Sidebar && (
          <aside className="k-layout-sidebar">
            <Sidebar />
          </aside>
        )}
        
        <main className="k-layout-content">
          {children || <Outlet />}
        </main>
      </div>
      
      {Footer && (
        <footer className="k-layout-footer">
          <Footer />
        </footer>
      )}
    </div>
  );
}

export default NestedLayout;
