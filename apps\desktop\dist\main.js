"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createMainWindow = createMainWindow;
exports.createMenu = createMenu;
const electron_1 = require("electron");
const path_1 = require("path");
const kilat_utils_1 = require("kilat-utils");
const logger = (0, kilat_utils_1.createLogger)({ prefix: 'ElectronMain' });
/**
 * 🖥️ Kilat.js Desktop - Main Process
 * Electron main process for desktop application
 */
// Keep a global reference of the window object
let mainWindow = null;
// App configuration
const isDev = process.env.NODE_ENV === 'development';
const isWin = process.platform === 'win32';
const isMac = process.platform === 'darwin';
/**
 * 🪟 Create Main Window
 */
function createMainWindow() {
    // Create the browser window
    mainWindow = new electron_1.BrowserWindow({
        width: 1200,
        height: 800,
        minWidth: 800,
        minHeight: 600,
        show: false, // Don't show until ready
        icon: (0, path_1.join)(__dirname, '../assets/icon.png'),
        titleBarStyle: isMac ? 'hiddenInset' : 'default',
        titleBarOverlay: isWin ? {
            color: '#000011',
            symbolColor: '#00ffff',
            height: 30
        } : undefined,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false,
            preload: (0, path_1.join)(__dirname, 'preload.js'),
            webSecurity: !isDev,
        },
        backgroundColor: '#000011', // Cyberpunk theme background
    });
    // Load the app
    if (isDev) {
        mainWindow.loadURL('http://localhost:5173');
        // Open DevTools in development
        mainWindow.webContents.openDevTools();
    }
    else {
        mainWindow.loadFile((0, path_1.join)(__dirname, '../renderer/index.html'));
    }
    // Show window when ready
    mainWindow.once('ready-to-show', () => {
        if (mainWindow) {
            mainWindow.show();
            // Focus on window
            if (isDev) {
                mainWindow.focus();
            }
            logger.info('Main window created and shown');
        }
    });
    // Handle window closed
    mainWindow.on('closed', () => {
        mainWindow = null;
    });
    // Handle external links
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        electron_1.shell.openExternal(url);
        return { action: 'deny' };
    });
    // Prevent navigation to external URLs
    mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
        const parsedUrl = new URL(navigationUrl);
        if (parsedUrl.origin !== 'http://localhost:5173' && !isDev) {
            event.preventDefault();
        }
    });
    return mainWindow;
}
/**
 * 📋 Create Application Menu
 */
function createMenu() {
    const template = [
        // macOS app menu
        ...(isMac ? [{
                label: electron_1.app.getName(),
                submenu: [
                    { role: 'about' },
                    { type: 'separator' },
                    { role: 'services' },
                    { type: 'separator' },
                    { role: 'hide' },
                    { role: 'hideOthers' },
                    { role: 'unhide' },
                    { type: 'separator' },
                    { role: 'quit' }
                ]
            }] : []),
        // File menu
        {
            label: 'File',
            submenu: [
                {
                    label: 'New Project',
                    accelerator: 'CmdOrCtrl+N',
                    click: () => {
                        // Handle new project
                        mainWindow?.webContents.send('menu-new-project');
                    }
                },
                {
                    label: 'Open Project',
                    accelerator: 'CmdOrCtrl+O',
                    click: async () => {
                        if (mainWindow) {
                            const result = await electron_1.dialog.showOpenDialog(mainWindow, {
                                properties: ['openDirectory'],
                                title: 'Open Kilat.js Project'
                            });
                            if (!result.canceled && result.filePaths.length > 0) {
                                mainWindow.webContents.send('menu-open-project', result.filePaths[0]);
                            }
                        }
                    }
                },
                { type: 'separator' },
                isMac ? { role: 'close' } : { role: 'quit' }
            ]
        },
        // Edit menu
        {
            label: 'Edit',
            submenu: [
                { role: 'undo' },
                { role: 'redo' },
                { type: 'separator' },
                { role: 'cut' },
                { role: 'copy' },
                { role: 'paste' },
                ...(isMac ? [
                    { role: 'pasteAndMatchStyle' },
                    { role: 'delete' },
                    { role: 'selectAll' },
                    { type: 'separator' },
                    {
                        label: 'Speech',
                        submenu: [
                            { role: 'startSpeaking' },
                            { role: 'stopSpeaking' }
                        ]
                    }
                ] : [
                    { role: 'delete' },
                    { type: 'separator' },
                    { role: 'selectAll' }
                ])
            ]
        },
        // View menu
        {
            label: 'View',
            submenu: [
                { role: 'reload' },
                { role: 'forceReload' },
                { role: 'toggleDevTools' },
                { type: 'separator' },
                { role: 'resetZoom' },
                { role: 'zoomIn' },
                { role: 'zoomOut' },
                { type: 'separator' },
                { role: 'togglefullscreen' },
                { type: 'separator' },
                {
                    label: 'Toggle Theme',
                    accelerator: 'CmdOrCtrl+T',
                    click: () => {
                        mainWindow?.webContents.send('menu-toggle-theme');
                    }
                }
            ]
        },
        // Window menu
        {
            label: 'Window',
            submenu: [
                { role: 'minimize' },
                { role: 'close' },
                ...(isMac ? [
                    { type: 'separator' },
                    { role: 'front' },
                    { type: 'separator' },
                    { role: 'window' }
                ] : [])
            ]
        },
        // Help menu
        {
            role: 'help',
            submenu: [
                {
                    label: 'About Kilat.js',
                    click: () => {
                        mainWindow?.webContents.send('menu-about');
                    }
                },
                {
                    label: 'Documentation',
                    click: () => {
                        electron_1.shell.openExternal('https://docs.kilat.js.org');
                    }
                },
                {
                    label: 'GitHub Repository',
                    click: () => {
                        electron_1.shell.openExternal('https://github.com/kangpcode/kilat.js');
                    }
                },
                { type: 'separator' },
                {
                    label: 'Report Issue',
                    click: () => {
                        electron_1.shell.openExternal('https://github.com/kangpcode/kilat.js/issues');
                    }
                }
            ]
        }
    ];
    const menu = electron_1.Menu.buildFromTemplate(template);
    electron_1.Menu.setApplicationMenu(menu);
}
/**
 * 🔧 Setup IPC Handlers
 */
function setupIpcHandlers() {
    // Get app version
    electron_1.ipcMain.handle('app:get-version', () => {
        return electron_1.app.getVersion();
    });
    // Get platform info
    electron_1.ipcMain.handle('app:get-platform', () => {
        return {
            platform: process.platform,
            arch: process.arch,
            version: process.version,
            electronVersion: process.versions.electron,
        };
    });
    // Theme handling
    electron_1.ipcMain.handle('theme:get-native-theme', () => {
        return electron_1.nativeTheme.shouldUseDarkColors;
    });
    electron_1.ipcMain.on('theme:set-native-theme', (_, theme) => {
        electron_1.nativeTheme.themeSource = theme;
    });
    // Window controls
    electron_1.ipcMain.on('window:minimize', () => {
        mainWindow?.minimize();
    });
    electron_1.ipcMain.on('window:maximize', () => {
        if (mainWindow?.isMaximized()) {
            mainWindow.unmaximize();
        }
        else {
            mainWindow?.maximize();
        }
    });
    electron_1.ipcMain.on('window:close', () => {
        mainWindow?.close();
    });
    // Show message box
    electron_1.ipcMain.handle('dialog:show-message', async (_, options) => {
        if (mainWindow) {
            return await electron_1.dialog.showMessageBox(mainWindow, options);
        }
    });
    // Show save dialog
    electron_1.ipcMain.handle('dialog:show-save', async (_, options) => {
        if (mainWindow) {
            return await electron_1.dialog.showSaveDialog(mainWindow, options);
        }
    });
    logger.info('IPC handlers setup complete');
}
/**
 * 🚀 App Event Handlers
 */
// This method will be called when Electron has finished initialization
electron_1.app.whenReady().then(() => {
    logger.info('App ready, creating main window...');
    createMainWindow();
    createMenu();
    setupIpcHandlers();
    // macOS: Re-create window when dock icon is clicked
    electron_1.app.on('activate', () => {
        if (electron_1.BrowserWindow.getAllWindows().length === 0) {
            createMainWindow();
        }
    });
});
// Quit when all windows are closed
electron_1.app.on('window-all-closed', () => {
    // On macOS, keep app running even when all windows are closed
    if (!isMac) {
        electron_1.app.quit();
    }
});
// Security: Prevent new window creation
electron_1.app.on('web-contents-created', (_, contents) => {
    contents.on('new-window', (event, navigationUrl) => {
        event.preventDefault();
        electron_1.shell.openExternal(navigationUrl);
    });
});
// Handle certificate errors
electron_1.app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
    if (isDev) {
        // In development, ignore certificate errors
        event.preventDefault();
        callback(true);
    }
    else {
        // In production, use default behavior
        callback(false);
    }
});
// Handle app updates (placeholder)
electron_1.app.on('ready', () => {
    if (!isDev) {
        // Auto-updater logic would go here
        logger.info('Auto-updater ready (placeholder)');
    }
});
// Prevent navigation to external URLs
electron_1.app.on('web-contents-created', (_, contents) => {
    contents.on('will-navigate', (event, navigationUrl) => {
        const parsedUrl = new URL(navigationUrl);
        if (parsedUrl.origin !== 'http://localhost:5173' && isDev) {
            event.preventDefault();
        }
    });
});
// Log app events
electron_1.app.on('before-quit', () => {
    logger.info('App is about to quit');
});
electron_1.app.on('will-quit', () => {
    logger.info('App will quit');
});
//# sourceMappingURL=main.js.map