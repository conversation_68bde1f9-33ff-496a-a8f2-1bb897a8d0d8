import { useContext } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import type { KilatRouterContext } from '../types';

// This will be imported from the main router component
const KilatRouterContext = React.createContext<KilatRouterContext | null>(null);

// 🧭 Main router hook
export function useKilatRouter(): KilatRouterContext {
  const context = useContext(KilatRouterContext);
  if (!context) {
    throw new Error('useKilatRouter must be used within a KilatRouter');
  }
  return context;
}

// 📍 Location hook with Kilat enhancements
export function useKilatLocation() {
  const location = useLocation();
  const { currentRoute } = useKilatRouter();
  
  return {
    ...location,
    route: currentRoute,
    isExact: location.pathname === currentRoute?.path,
    segments: location.pathname.split('/').filter(Boolean),
    depth: location.pathname.split('/').filter(Boolean).length
  };
}

// 🎯 Enhanced navigation hook
export function useKilatNavigate() {
  const navigate = useNavigate();
  const { navigate: kilatNavigate, preload, config } = useKilatRouter();
  
  return {
    // Standard navigation
    navigate: kilatNavigate,
    push: (to: string, state?: any) => kilatNavigate(to, { state }),
    replace: (to: string, state?: any) => kilatNavigate(to, { replace: true, state }),
    
    // Enhanced navigation
    back: () => window.history.back(),
    forward: () => window.history.forward(),
    go: (delta: number) => window.history.go(delta),
    refresh: () => window.location.reload(),
    
    // Preloading
    preload,
    
    // Utility functions
    canGoBack: () => window.history.length > 1,
    canGoForward: () => window.history.length > window.history.state?.idx + 1,
    
    // Route-aware navigation
    navigateToRoute: (routeName: string, params?: Record<string, string>) => {
      // Implementation would depend on route registry
      console.warn('navigateToRoute not implemented yet');
    }
  };
}

// 📊 Route parameters hook
export function useKilatParams<T extends Record<string, string> = Record<string, string>>(): T {
  const params = useParams();
  const { currentRoute } = useKilatRouter();
  
  // Type-safe parameter access with route validation
  return params as T;
}

// 🔍 Query parameters hook
export function useKilatQuery() {
  const { query } = useKilatRouter();
  
  const get = (key: string): string | null => {
    return query.get(key);
  };
  
  const getAll = (key: string): string[] => {
    return query.getAll(key);
  };
  
  const has = (key: string): boolean => {
    return query.has(key);
  };
  
  const set = (key: string, value: string) => {
    const newQuery = new URLSearchParams(query);
    newQuery.set(key, value);
    const newUrl = `${window.location.pathname}?${newQuery.toString()}`;
    window.history.replaceState(null, '', newUrl);
  };
  
  const delete_ = (key: string) => {
    const newQuery = new URLSearchParams(query);
    newQuery.delete(key);
    const newUrl = `${window.location.pathname}?${newQuery.toString()}`;
    window.history.replaceState(null, '', newUrl);
  };
  
  const clear = () => {
    window.history.replaceState(null, '', window.location.pathname);
  };
  
  const toString = (): string => {
    return query.toString();
  };
  
  const toObject = (): Record<string, string> => {
    const obj: Record<string, string> = {};
    for (const [key, value] of query.entries()) {
      obj[key] = value;
    }
    return obj;
  };
  
  return {
    query,
    get,
    getAll,
    has,
    set,
    delete: delete_,
    clear,
    toString,
    toObject,
    
    // Convenience getters for common query params
    page: get('page') ? parseInt(get('page')!, 10) : 1,
    limit: get('limit') ? parseInt(get('limit')!, 10) : 10,
    search: get('search') || get('q') || '',
    sort: get('sort') || '',
    filter: get('filter') || ''
  };
}

// 🎭 Route metadata hook
export function useKilatMeta() {
  const { currentRoute } = useKilatRouter();
  const meta = currentRoute?.meta || {};
  
  // Update document title and meta tags
  React.useEffect(() => {
    if (meta.title) {
      document.title = meta.title;
    }
    
    if (meta.description) {
      let descMeta = document.querySelector('meta[name="description"]');
      if (!descMeta) {
        descMeta = document.createElement('meta');
        descMeta.setAttribute('name', 'description');
        document.head.appendChild(descMeta);
      }
      descMeta.setAttribute('content', meta.description);
    }
    
    if (meta.keywords) {
      let keywordsMeta = document.querySelector('meta[name="keywords"]');
      if (!keywordsMeta) {
        keywordsMeta = document.createElement('meta');
        keywordsMeta.setAttribute('name', 'keywords');
        document.head.appendChild(keywordsMeta);
      }
      const keywords = Array.isArray(meta.keywords) ? meta.keywords.join(', ') : meta.keywords;
      keywordsMeta.setAttribute('content', keywords);
    }
  }, [meta]);
  
  return {
    meta,
    title: meta.title,
    description: meta.description,
    keywords: meta.keywords,
    auth: meta.auth,
    roles: meta.roles,
    permissions: meta.permissions,
    cache: meta.cache,
    preload: meta.preload
  };
}

// 🔐 Route authentication hook
export function useKilatAuth() {
  const { currentRoute } = useKilatRouter();
  const meta = currentRoute?.meta || {};
  
  return {
    requiresAuth: meta.auth || false,
    requiredRoles: meta.roles || [],
    requiredPermissions: meta.permissions || [],
    isProtected: meta.auth || (meta.roles && meta.roles.length > 0) || (meta.permissions && meta.permissions.length > 0)
  };
}

// 📊 Route analytics hook
export function useKilatAnalytics() {
  const { currentRoute } = useKilatRouter();
  const location = useKilatLocation();
  
  React.useEffect(() => {
    // Track page view
    const startTime = Date.now();
    
    // Send analytics event (implementation depends on analytics service)
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', 'GA_MEASUREMENT_ID', {
        page_title: currentRoute?.meta?.title || document.title,
        page_location: window.location.href
      });
    }
    
    return () => {
      // Track time spent on page
      const timeSpent = Date.now() - startTime;
      console.debug('Time spent on route:', location.pathname, timeSpent + 'ms');
    };
  }, [currentRoute, location.pathname]);
  
  const trackEvent = (eventName: string, properties?: Record<string, any>) => {
    // Implementation depends on analytics service
    console.debug('Track event:', eventName, properties);
  };
  
  return {
    trackEvent,
    currentRoute: currentRoute?.path,
    routeMeta: currentRoute?.meta
  };
}

// 🎯 Route matching hook
export function useKilatMatch(path: string) {
  const location = useKilatLocation();
  const { config } = useKilatRouter();
  
  // Simple path matching
  const isMatch = React.useMemo(() => {
    if (path === location.pathname) return true;
    
    // Handle dynamic segments
    const pathSegments = path.split('/').filter(Boolean);
    const locationSegments = location.pathname.split('/').filter(Boolean);
    
    if (pathSegments.length !== locationSegments.length) return false;
    
    for (let i = 0; i < pathSegments.length; i++) {
      const pathSegment = pathSegments[i];
      const locationSegment = locationSegments[i];
      
      if (pathSegment.startsWith(':')) {
        // Dynamic segment - always matches
        continue;
      }
      
      if (pathSegment !== locationSegment) {
        return false;
      }
    }
    
    return true;
  }, [path, location.pathname]);
  
  return {
    isMatch,
    isExact: path === location.pathname,
    isPartial: location.pathname.startsWith(path)
  };
}
