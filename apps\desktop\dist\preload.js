"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
const electronAPI = {
    // App info
    getVersion: () => electron_1.ipcRenderer.invoke('app:get-version'),
    getPlatform: () => electron_1.ipcRenderer.invoke('app:get-platform'),
    // Theme management
    getNativeTheme: () => electron_1.ipcRenderer.invoke('theme:get-native-theme'),
    setNativeTheme: (theme) => electron_1.ipcRenderer.send('theme:set-native-theme', theme),
    // Window controls
    minimizeWindow: () => electron_1.ipcRenderer.send('window:minimize'),
    maximizeWindow: () => electron_1.ipcRenderer.send('window:maximize'),
    closeWindow: () => electron_1.ipcRenderer.send('window:close'),
    // Dialog APIs
    showMessage: (options) => electron_1.ipcRenderer.invoke('dialog:show-message', options),
    showSaveDialog: (options) => electron_1.ipcRenderer.invoke('dialog:show-save', options),
    // Menu event listeners
    onMenuEvent: (event, callback) => {
        const channel = `menu:${event}`;
        electron_1.ipcRenderer.on(channel, callback);
    },
    removeMenuListener: (event) => {
        const channel = `menu:${event}`;
        electron_1.ipcRenderer.removeAllListeners(channel);
    },
    // File system (safe operations only)
    readFile: (path) => electron_1.ipcRenderer.invoke('fs:read-file', path),
    writeFile: (path, content) => electron_1.ipcRenderer.invoke('fs:write-file', path, content),
    // Project management
    createProject: (name, path) => electron_1.ipcRenderer.invoke('project:create', name, path),
    openProject: (path) => electron_1.ipcRenderer.invoke('project:open', path),
};
// Expose the API to the renderer process
electron_1.contextBridge.exposeInMainWorld('electronAPI', electronAPI);
// Also expose some Node.js globals that are safe to use
electron_1.contextBridge.exposeInMainWorld('nodeAPI', {
    platform: process.platform,
    arch: process.arch,
    versions: process.versions,
});
// Development helpers
if (process.env.NODE_ENV === 'development') {
    electron_1.contextBridge.exposeInMainWorld('devAPI', {
        openDevTools: () => electron_1.ipcRenderer.send('dev:open-devtools'),
        reload: () => electron_1.ipcRenderer.send('dev:reload'),
    });
}
// Security: Remove Node.js globals from window object
delete window.require;
delete window.exports;
delete window.module;
// Log successful preload
console.log('🔗 Preload script loaded successfully');
//# sourceMappingURL=preload.js.map