var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __require = /* @__PURE__ */ ((x) => typeof require !== "undefined" ? require : typeof Proxy !== "undefined" ? new Proxy(x, {
  get: (a, b) => (typeof require !== "undefined" ? require : a)[b]
}) : x)(function(x) {
  if (typeof require !== "undefined")
    return require.apply(this, arguments);
  throw Error('Dynamic require of "' + x + '" is not supported');
});
var __commonJS = (cb, mod) => function __require2() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));

// node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js
var require_use_sync_external_store_shim_development = __commonJS({
  "node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js"(exports) {
    "use strict";
    (function() {
      function is(x, y) {
        return x === y && (0 !== x || 1 / x === 1 / y) || x !== x && y !== y;
      }
      function useSyncExternalStore$2(subscribe, getSnapshot) {
        didWarnOld18Alpha || void 0 === React4.startTransition || (didWarnOld18Alpha = true, console.error(
          "You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release."
        ));
        var value = getSnapshot();
        if (!didWarnUncachedGetSnapshot) {
          var cachedValue = getSnapshot();
          objectIs(value, cachedValue) || (console.error(
            "The result of getSnapshot should be cached to avoid an infinite loop"
          ), didWarnUncachedGetSnapshot = true);
        }
        cachedValue = useState3({
          inst: { value, getSnapshot }
        });
        var inst = cachedValue[0].inst, forceUpdate = cachedValue[1];
        useLayoutEffect(
          function() {
            inst.value = value;
            inst.getSnapshot = getSnapshot;
            checkIfSnapshotChanged(inst) && forceUpdate({ inst });
          },
          [subscribe, value, getSnapshot]
        );
        useEffect2(
          function() {
            checkIfSnapshotChanged(inst) && forceUpdate({ inst });
            return subscribe(function() {
              checkIfSnapshotChanged(inst) && forceUpdate({ inst });
            });
          },
          [subscribe]
        );
        useDebugValue2(value);
        return value;
      }
      function checkIfSnapshotChanged(inst) {
        var latestGetSnapshot = inst.getSnapshot;
        inst = inst.value;
        try {
          var nextValue = latestGetSnapshot();
          return !objectIs(inst, nextValue);
        } catch (error) {
          return true;
        }
      }
      function useSyncExternalStore$1(subscribe, getSnapshot) {
        return getSnapshot();
      }
      "undefined" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && "function" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());
      var React4 = __require("react"), objectIs = "function" === typeof Object.is ? Object.is : is, useState3 = React4.useState, useEffect2 = React4.useEffect, useLayoutEffect = React4.useLayoutEffect, useDebugValue2 = React4.useDebugValue, didWarnOld18Alpha = false, didWarnUncachedGetSnapshot = false, shim = "undefined" === typeof window || "undefined" === typeof window.document || "undefined" === typeof window.document.createElement ? useSyncExternalStore$1 : useSyncExternalStore$2;
      exports.useSyncExternalStore = void 0 !== React4.useSyncExternalStore ? React4.useSyncExternalStore : shim;
      "undefined" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && "function" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());
    })();
  }
});

// node_modules/use-sync-external-store/shim/index.js
var require_shim = __commonJS({
  "node_modules/use-sync-external-store/shim/index.js"(exports, module) {
    "use strict";
    if (false) {
      module.exports = null;
    } else {
      module.exports = require_use_sync_external_store_shim_development();
    }
  }
});

// node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js
var require_with_selector_development = __commonJS({
  "node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js"(exports) {
    "use strict";
    (function() {
      function is(x, y) {
        return x === y && (0 !== x || 1 / x === 1 / y) || x !== x && y !== y;
      }
      "undefined" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && "function" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());
      var React4 = __require("react"), shim = require_shim(), objectIs = "function" === typeof Object.is ? Object.is : is, useSyncExternalStore = shim.useSyncExternalStore, useRef = React4.useRef, useEffect2 = React4.useEffect, useMemo2 = React4.useMemo, useDebugValue2 = React4.useDebugValue;
      exports.useSyncExternalStoreWithSelector = function(subscribe, getSnapshot, getServerSnapshot, selector, isEqual) {
        var instRef = useRef(null);
        if (null === instRef.current) {
          var inst = { hasValue: false, value: null };
          instRef.current = inst;
        } else
          inst = instRef.current;
        instRef = useMemo2(
          function() {
            function memoizedSelector(nextSnapshot) {
              if (!hasMemo) {
                hasMemo = true;
                memoizedSnapshot = nextSnapshot;
                nextSnapshot = selector(nextSnapshot);
                if (void 0 !== isEqual && inst.hasValue) {
                  var currentSelection = inst.value;
                  if (isEqual(currentSelection, nextSnapshot))
                    return memoizedSelection = currentSelection;
                }
                return memoizedSelection = nextSnapshot;
              }
              currentSelection = memoizedSelection;
              if (objectIs(memoizedSnapshot, nextSnapshot))
                return currentSelection;
              var nextSelection = selector(nextSnapshot);
              if (void 0 !== isEqual && isEqual(currentSelection, nextSelection))
                return memoizedSnapshot = nextSnapshot, currentSelection;
              memoizedSnapshot = nextSnapshot;
              return memoizedSelection = nextSelection;
            }
            var hasMemo = false, memoizedSnapshot, memoizedSelection, maybeGetServerSnapshot = void 0 === getServerSnapshot ? null : getServerSnapshot;
            return [
              function() {
                return memoizedSelector(getSnapshot());
              },
              null === maybeGetServerSnapshot ? void 0 : function() {
                return memoizedSelector(maybeGetServerSnapshot());
              }
            ];
          },
          [getSnapshot, getServerSnapshot, selector, isEqual]
        );
        var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);
        useEffect2(
          function() {
            inst.hasValue = true;
            inst.value = value;
          },
          [value]
        );
        useDebugValue2(value);
        return value;
      };
      "undefined" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && "function" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());
    })();
  }
});

// node_modules/use-sync-external-store/shim/with-selector.js
var require_with_selector = __commonJS({
  "node_modules/use-sync-external-store/shim/with-selector.js"(exports, module) {
    "use strict";
    if (false) {
      module.exports = null;
    } else {
      module.exports = require_with_selector_development();
    }
  }
});

// packages/kilat-core/src/context.tsx
import { createContext, useContext, useEffect } from "react";

// node_modules/zustand/esm/vanilla.mjs
var createStoreImpl = (createState) => {
  let state;
  const listeners = /* @__PURE__ */ new Set();
  const setState = (partial, replace) => {
    const nextState = typeof partial === "function" ? partial(state) : partial;
    if (!Object.is(nextState, state)) {
      const previousState = state;
      state = (replace != null ? replace : typeof nextState !== "object" || nextState === null) ? nextState : Object.assign({}, state, nextState);
      listeners.forEach((listener) => listener(state, previousState));
    }
  };
  const getState = () => state;
  const getInitialState = () => initialState2;
  const subscribe = (listener) => {
    listeners.add(listener);
    return () => listeners.delete(listener);
  };
  const destroy = () => {
    if ((import.meta.env ? import.meta.env.MODE : void 0) !== "production") {
      console.warn(
        "[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."
      );
    }
    listeners.clear();
  };
  const api = { setState, getState, getInitialState, subscribe, destroy };
  const initialState2 = state = createState(setState, getState, api);
  return api;
};
var createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;

// node_modules/zustand/esm/index.mjs
var import_with_selector = __toESM(require_with_selector(), 1);
import ReactExports from "react";
var { useDebugValue } = ReactExports;
var { useSyncExternalStoreWithSelector } = import_with_selector.default;
var didWarnAboutEqualityFn = false;
var identity = (arg) => arg;
function useStore(api, selector = identity, equalityFn) {
  if ((import.meta.env ? import.meta.env.MODE : void 0) !== "production" && equalityFn && !didWarnAboutEqualityFn) {
    console.warn(
      "[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"
    );
    didWarnAboutEqualityFn = true;
  }
  const slice = useSyncExternalStoreWithSelector(
    api.subscribe,
    api.getState,
    api.getServerState || api.getInitialState,
    selector,
    equalityFn
  );
  useDebugValue(slice);
  return slice;
}
var createImpl = (createState) => {
  if ((import.meta.env ? import.meta.env.MODE : void 0) !== "production" && typeof createState !== "function") {
    console.warn(
      "[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`."
    );
  }
  const api = typeof createState === "function" ? createStore(createState) : createState;
  const useBoundStore = (selector, equalityFn) => useStore(api, selector, equalityFn);
  Object.assign(useBoundStore, api);
  return useBoundStore;
};
var create = (createState) => createState ? createImpl(createState) : createImpl;

// node_modules/zustand/esm/middleware.mjs
function createJSONStorage(getStorage, options) {
  let storage;
  try {
    storage = getStorage();
  } catch (_e) {
    return;
  }
  const persistStorage = {
    getItem: (name) => {
      var _a;
      const parse = (str2) => {
        if (str2 === null) {
          return null;
        }
        return JSON.parse(str2, options == null ? void 0 : options.reviver);
      };
      const str = (_a = storage.getItem(name)) != null ? _a : null;
      if (str instanceof Promise) {
        return str.then(parse);
      }
      return parse(str);
    },
    setItem: (name, newValue) => storage.setItem(
      name,
      JSON.stringify(newValue, options == null ? void 0 : options.replacer)
    ),
    removeItem: (name) => storage.removeItem(name)
  };
  return persistStorage;
}
var toThenable = (fn) => (input) => {
  try {
    const result = fn(input);
    if (result instanceof Promise) {
      return result;
    }
    return {
      then(onFulfilled) {
        return toThenable(onFulfilled)(result);
      },
      catch(_onRejected) {
        return this;
      }
    };
  } catch (e) {
    return {
      then(_onFulfilled) {
        return this;
      },
      catch(onRejected) {
        return toThenable(onRejected)(e);
      }
    };
  }
};
var oldImpl = (config, baseOptions) => (set, get, api) => {
  let options = {
    getStorage: () => localStorage,
    serialize: JSON.stringify,
    deserialize: JSON.parse,
    partialize: (state) => state,
    version: 0,
    merge: (persistedState, currentState) => ({
      ...currentState,
      ...persistedState
    }),
    ...baseOptions
  };
  let hasHydrated = false;
  const hydrationListeners = /* @__PURE__ */ new Set();
  const finishHydrationListeners = /* @__PURE__ */ new Set();
  let storage;
  try {
    storage = options.getStorage();
  } catch (_e) {
  }
  if (!storage) {
    return config(
      (...args) => {
        console.warn(
          `[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`
        );
        set(...args);
      },
      get,
      api
    );
  }
  const thenableSerialize = toThenable(options.serialize);
  const setItem = () => {
    const state = options.partialize({ ...get() });
    let errorInSync;
    const thenable = thenableSerialize({ state, version: options.version }).then(
      (serializedValue) => storage.setItem(options.name, serializedValue)
    ).catch((e) => {
      errorInSync = e;
    });
    if (errorInSync) {
      throw errorInSync;
    }
    return thenable;
  };
  const savedSetState = api.setState;
  api.setState = (state, replace) => {
    savedSetState(state, replace);
    void setItem();
  };
  const configResult = config(
    (...args) => {
      set(...args);
      void setItem();
    },
    get,
    api
  );
  let stateFromStorage;
  const hydrate = () => {
    var _a;
    if (!storage)
      return;
    hasHydrated = false;
    hydrationListeners.forEach((cb) => cb(get()));
    const postRehydrationCallback = ((_a = options.onRehydrateStorage) == null ? void 0 : _a.call(options, get())) || void 0;
    return toThenable(storage.getItem.bind(storage))(options.name).then((storageValue) => {
      if (storageValue) {
        return options.deserialize(storageValue);
      }
    }).then((deserializedStorageValue) => {
      if (deserializedStorageValue) {
        if (typeof deserializedStorageValue.version === "number" && deserializedStorageValue.version !== options.version) {
          if (options.migrate) {
            return options.migrate(
              deserializedStorageValue.state,
              deserializedStorageValue.version
            );
          }
          console.error(
            `State loaded from storage couldn't be migrated since no migrate function was provided`
          );
        } else {
          return deserializedStorageValue.state;
        }
      }
    }).then((migratedState) => {
      var _a2;
      stateFromStorage = options.merge(
        migratedState,
        (_a2 = get()) != null ? _a2 : configResult
      );
      set(stateFromStorage, true);
      return setItem();
    }).then(() => {
      postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);
      hasHydrated = true;
      finishHydrationListeners.forEach((cb) => cb(stateFromStorage));
    }).catch((e) => {
      postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);
    });
  };
  api.persist = {
    setOptions: (newOptions) => {
      options = {
        ...options,
        ...newOptions
      };
      if (newOptions.getStorage) {
        storage = newOptions.getStorage();
      }
    },
    clearStorage: () => {
      storage == null ? void 0 : storage.removeItem(options.name);
    },
    getOptions: () => options,
    rehydrate: () => hydrate(),
    hasHydrated: () => hasHydrated,
    onHydrate: (cb) => {
      hydrationListeners.add(cb);
      return () => {
        hydrationListeners.delete(cb);
      };
    },
    onFinishHydration: (cb) => {
      finishHydrationListeners.add(cb);
      return () => {
        finishHydrationListeners.delete(cb);
      };
    }
  };
  hydrate();
  return stateFromStorage || configResult;
};
var newImpl = (config, baseOptions) => (set, get, api) => {
  let options = {
    storage: createJSONStorage(() => localStorage),
    partialize: (state) => state,
    version: 0,
    merge: (persistedState, currentState) => ({
      ...currentState,
      ...persistedState
    }),
    ...baseOptions
  };
  let hasHydrated = false;
  const hydrationListeners = /* @__PURE__ */ new Set();
  const finishHydrationListeners = /* @__PURE__ */ new Set();
  let storage = options.storage;
  if (!storage) {
    return config(
      (...args) => {
        console.warn(
          `[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`
        );
        set(...args);
      },
      get,
      api
    );
  }
  const setItem = () => {
    const state = options.partialize({ ...get() });
    return storage.setItem(options.name, {
      state,
      version: options.version
    });
  };
  const savedSetState = api.setState;
  api.setState = (state, replace) => {
    savedSetState(state, replace);
    void setItem();
  };
  const configResult = config(
    (...args) => {
      set(...args);
      void setItem();
    },
    get,
    api
  );
  api.getInitialState = () => configResult;
  let stateFromStorage;
  const hydrate = () => {
    var _a, _b;
    if (!storage)
      return;
    hasHydrated = false;
    hydrationListeners.forEach((cb) => {
      var _a2;
      return cb((_a2 = get()) != null ? _a2 : configResult);
    });
    const postRehydrationCallback = ((_b = options.onRehydrateStorage) == null ? void 0 : _b.call(options, (_a = get()) != null ? _a : configResult)) || void 0;
    return toThenable(storage.getItem.bind(storage))(options.name).then((deserializedStorageValue) => {
      if (deserializedStorageValue) {
        if (typeof deserializedStorageValue.version === "number" && deserializedStorageValue.version !== options.version) {
          if (options.migrate) {
            return [
              true,
              options.migrate(
                deserializedStorageValue.state,
                deserializedStorageValue.version
              )
            ];
          }
          console.error(
            `State loaded from storage couldn't be migrated since no migrate function was provided`
          );
        } else {
          return [false, deserializedStorageValue.state];
        }
      }
      return [false, void 0];
    }).then((migrationResult) => {
      var _a2;
      const [migrated, migratedState] = migrationResult;
      stateFromStorage = options.merge(
        migratedState,
        (_a2 = get()) != null ? _a2 : configResult
      );
      set(stateFromStorage, true);
      if (migrated) {
        return setItem();
      }
    }).then(() => {
      postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);
      stateFromStorage = get();
      hasHydrated = true;
      finishHydrationListeners.forEach((cb) => cb(stateFromStorage));
    }).catch((e) => {
      postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);
    });
  };
  api.persist = {
    setOptions: (newOptions) => {
      options = {
        ...options,
        ...newOptions
      };
      if (newOptions.storage) {
        storage = newOptions.storage;
      }
    },
    clearStorage: () => {
      storage == null ? void 0 : storage.removeItem(options.name);
    },
    getOptions: () => options,
    rehydrate: () => hydrate(),
    hasHydrated: () => hasHydrated,
    onHydrate: (cb) => {
      hydrationListeners.add(cb);
      return () => {
        hydrationListeners.delete(cb);
      };
    },
    onFinishHydration: (cb) => {
      finishHydrationListeners.add(cb);
      return () => {
        finishHydrationListeners.delete(cb);
      };
    }
  };
  if (!options.skipHydration) {
    hydrate();
  }
  return stateFromStorage || configResult;
};
var persistImpl = (config, baseOptions) => {
  if ("getStorage" in baseOptions || "serialize" in baseOptions || "deserialize" in baseOptions) {
    if ((import.meta.env ? import.meta.env.MODE : void 0) !== "production") {
      console.warn(
        "[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."
      );
    }
    return oldImpl(config, baseOptions);
  }
  return newImpl(config, baseOptions);
};
var persist = persistImpl;

// packages/kilat-core/src/error-recovery.ts
var KilatErrorRecovery = class {
  config;
  retryCount = /* @__PURE__ */ new Map();
  crashReports = [];
  isInSafeMode = false;
  constructor(config) {
    this.config = {
      enabled: true,
      maxRetries: 3,
      retryDelay: 1e3,
      fallbackMode: true,
      crashReport: {
        enabled: true,
        includeLogs: true,
        autoRetry: true
      },
      killSwitch: true,
      safeMode: true,
      ...config
    };
    this.setupGlobalErrorHandlers();
  }
  // 🚨 Setup global error handlers
  setupGlobalErrorHandlers() {
    if (typeof window !== "undefined") {
      window.addEventListener("unhandledrejection", (event) => {
        this.handleError(new Error(event.reason), "unhandledrejection");
      });
      window.addEventListener("error", (event) => {
        this.handleError(event.error || new Error(event.message), "javascript");
      });
      window.addEventListener("react-error", (event) => {
        this.handleError(event.detail.error, "react");
      });
    }
  }
  // 🔄 Handle errors with retry logic
  async handleError(error, source, context) {
    if (!this.config.enabled) {
      console.error("Kilat.js Error:", error);
      return false;
    }
    const errorKey = `${source}:${error.message}`;
    const currentRetries = this.retryCount.get(errorKey) || 0;
    const crashReport = this.createCrashReport(error, source, context);
    this.crashReports.push(crashReport);
    if (currentRetries < this.config.maxRetries) {
      this.retryCount.set(errorKey, currentRetries + 1);
      console.warn(`Kilat.js: Retrying after error (${currentRetries + 1}/${this.config.maxRetries}):`, error.message);
      await this.delay(this.config.retryDelay * (currentRetries + 1));
      return true;
    }
    console.error(`Kilat.js: Max retries reached for error:`, error);
    if (this.config.crashReport.enabled) {
      await this.sendCrashReport(crashReport);
    }
    if (this.config.safeMode && !this.isInSafeMode) {
      this.activateSafeMode();
    }
    if (this.config.killSwitch) {
      this.activateKillSwitch(source);
    }
    return false;
  }
  // 📊 Create crash report
  createCrashReport(error, source, context) {
    return {
      id: this.generateId(),
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack || ""
      },
      source,
      context,
      userAgent: typeof navigator !== "undefined" ? navigator.userAgent : "unknown",
      url: typeof window !== "undefined" ? window.location.href : "unknown",
      retryCount: this.retryCount.get(`${source}:${error.message}`) || 0,
      logs: this.config.crashReport.includeLogs ? this.getRecentLogs() : []
    };
  }
  // 📤 Send crash report
  async sendCrashReport(report) {
    try {
      const { endpoint, webhookURL } = this.config.crashReport;
      if (endpoint) {
        await fetch(endpoint, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(report)
        });
      }
      if (webhookURL) {
        await fetch(webhookURL, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            text: `\u{1F6A8} Kilat.js Crash Report`,
            attachments: [{
              color: "danger",
              title: `Error: ${report.error.name}`,
              text: report.error.message,
              fields: [
                { title: "Source", value: report.source, short: true },
                { title: "Retries", value: report.retryCount.toString(), short: true },
                { title: "URL", value: report.url, short: false }
              ]
            }]
          })
        });
      }
    } catch (sendError) {
      console.error("Failed to send crash report:", sendError);
    }
  }
  // 🛡️ Activate safe mode
  activateSafeMode() {
    this.isInSafeMode = true;
    console.warn("Kilat.js: Activating safe mode due to repeated errors");
    document.documentElement.setAttribute("data-kilat-safe-mode", "true");
    if (typeof window !== "undefined") {
      window.dispatchEvent(new CustomEvent("kilat:safe-mode", {
        detail: { active: true }
      }));
    }
  }
  // ⚡ Activate kill switch for specific source
  activateKillSwitch(source) {
    console.warn(`Kilat.js: Activating kill switch for source: ${source}`);
    const killSwitches = {
      "animation": () => {
        document.documentElement.setAttribute("data-kilat-disable-animations", "true");
      },
      "plugin": () => {
        document.documentElement.setAttribute("data-kilat-disable-plugins", "true");
      },
      "router": () => {
        document.documentElement.setAttribute("data-kilat-disable-router", "true");
      }
    };
    const killSwitch = killSwitches[source];
    if (killSwitch) {
      killSwitch();
    }
  }
  // 🔄 Reset error recovery state
  resetRecovery() {
    this.retryCount.clear();
    this.crashReports = [];
    this.isInSafeMode = false;
    document.documentElement.removeAttribute("data-kilat-safe-mode");
    document.documentElement.removeAttribute("data-kilat-disable-animations");
    document.documentElement.removeAttribute("data-kilat-disable-plugins");
    document.documentElement.removeAttribute("data-kilat-disable-router");
    console.log("Kilat.js: Error recovery state reset");
  }
  // 📊 Get crash reports
  getCrashReports() {
    return [...this.crashReports];
  }
  // 🛡️ Check if in safe mode
  isInSafeModeActive() {
    return this.isInSafeMode;
  }
  // 🔧 Utility methods
  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
  generateId() {
    return Math.random().toString(36).substr(2, 9);
  }
  getRecentLogs() {
    return [];
  }
};
var globalErrorRecovery = null;
function initializeErrorRecovery(config) {
  if (!globalErrorRecovery) {
    globalErrorRecovery = new KilatErrorRecovery(config);
  }
  return globalErrorRecovery;
}
function getErrorRecovery() {
  return globalErrorRecovery;
}

// packages/kilat-core/src/plugin-system.ts
var KilatPluginSystem = class {
  plugins = /* @__PURE__ */ new Map();
  pluginStates = /* @__PURE__ */ new Map();
  hooks = /* @__PURE__ */ new Map();
  context = null;
  constructor() {
    this.setupDefaultHooks();
  }
  // 🎯 Setup default plugin hooks
  setupDefaultHooks() {
    this.hooks.set("init", []);
    this.hooks.set("request", []);
    this.hooks.set("response", []);
    this.hooks.set("error", []);
    this.hooks.set("destroy", []);
  }
  // 📦 Register a plugin
  async registerPlugin(plugin) {
    try {
      if (!plugin.name) {
        throw new Error("Plugin must have a name");
      }
      if (this.plugins.has(plugin.name)) {
        console.warn(`Plugin ${plugin.name} is already registered`);
        return false;
      }
      this.pluginStates.set(plugin.name, {
        name: plugin.name,
        version: plugin.version || "1.0.0",
        enabled: true,
        retryCount: 0,
        maxRetries: 3,
        lastError: null,
        status: "initializing"
      });
      this.plugins.set(plugin.name, plugin);
      await this.initializePlugin(plugin);
      console.log(`\u2705 Plugin ${plugin.name} registered successfully`);
      return true;
    } catch (error) {
      console.error(`\u274C Failed to register plugin ${plugin.name}:`, error);
      await this.handlePluginError(plugin.name, error);
      return false;
    }
  }
  // 🚀 Initialize a plugin
  async initializePlugin(plugin) {
    const state = this.pluginStates.get(plugin.name);
    if (!state)
      return;
    try {
      state.status = "initializing";
      if (plugin.onInit && this.context) {
        await plugin.onInit(this.context);
      }
      this.registerPluginHooks(plugin);
      state.status = "active";
      state.lastError = null;
      console.log(`\u{1F680} Plugin ${plugin.name} initialized`);
    } catch (error) {
      state.status = "error";
      state.lastError = error;
      throw error;
    }
  }
  // 🪝 Register plugin hooks
  registerPluginHooks(plugin) {
    const hooks = [
      { name: "request", handler: plugin.onRequest },
      { name: "response", handler: plugin.onResponse },
      { name: "error", handler: plugin.onError }
    ];
    hooks.forEach(({ name, handler }) => {
      if (handler) {
        const hookList = this.hooks.get(name) || [];
        hookList.push({
          pluginName: plugin.name,
          handler
        });
        this.hooks.set(name, hookList);
      }
    });
  }
  // 🔄 Execute hooks
  async executeHooks(hookName, context) {
    const hooks = this.hooks.get(hookName) || [];
    for (const hook of hooks) {
      const state = this.pluginStates.get(hook.pluginName);
      if (!state || !state.enabled || state.status === "error") {
        continue;
      }
      try {
        await hook.handler(context);
      } catch (error) {
        console.error(`Hook ${hookName} failed for plugin ${hook.pluginName}:`, error);
        await this.handlePluginError(hook.pluginName, error);
      }
    }
  }
  // 🚨 Handle plugin errors with retry logic
  async handlePluginError(pluginName, error) {
    const state = this.pluginStates.get(pluginName);
    if (!state)
      return;
    state.lastError = error;
    state.retryCount++;
    const errorRecovery = getErrorRecovery();
    if (errorRecovery) {
      const shouldRetry = await errorRecovery.handleError(error, "plugin", { pluginName });
      if (shouldRetry && state.retryCount < state.maxRetries) {
        console.log(`\u{1F504} Retrying plugin ${pluginName} (${state.retryCount}/${state.maxRetries})`);
        const plugin = this.plugins.get(pluginName);
        if (plugin) {
          try {
            await this.initializePlugin(plugin);
            return;
          } catch (retryError) {
            console.error(`Retry failed for plugin ${pluginName}:`, retryError);
          }
        }
      }
    }
    if (state.retryCount >= state.maxRetries) {
      console.error(`\u274C Plugin ${pluginName} disabled after ${state.maxRetries} failed attempts`);
      state.enabled = false;
      state.status = "disabled";
      this.emitPluginEvent("disabled", { pluginName, error });
    }
  }
  // 📤 Emit plugin events
  emitPluginEvent(eventName, data) {
    if (typeof window !== "undefined") {
      window.dispatchEvent(new CustomEvent(`kilat:plugin:${eventName}`, {
        detail: data
      }));
    }
  }
  // 🔧 Plugin management methods
  enablePlugin(pluginName) {
    const state = this.pluginStates.get(pluginName);
    if (!state)
      return false;
    state.enabled = true;
    state.retryCount = 0;
    state.lastError = null;
    console.log(`\u2705 Plugin ${pluginName} enabled`);
    return true;
  }
  disablePlugin(pluginName) {
    const state = this.pluginStates.get(pluginName);
    if (!state)
      return false;
    state.enabled = false;
    console.log(`\u274C Plugin ${pluginName} disabled`);
    return true;
  }
  unregisterPlugin(pluginName) {
    const plugin = this.plugins.get(pluginName);
    if (!plugin)
      return false;
    this.hooks.forEach((hooks, hookName) => {
      const filtered = hooks.filter((h) => h.pluginName !== pluginName);
      this.hooks.set(hookName, filtered);
    });
    this.plugins.delete(pluginName);
    this.pluginStates.delete(pluginName);
    console.log(`\u{1F5D1}\uFE0F Plugin ${pluginName} unregistered`);
    return true;
  }
  // 📊 Get plugin information
  getPluginState(pluginName) {
    return this.pluginStates.get(pluginName) || null;
  }
  getAllPluginStates() {
    return Array.from(this.pluginStates.values());
  }
  getActivePlugins() {
    return Array.from(this.pluginStates.entries()).filter(([_, state]) => state.enabled && state.status === "active").map(([name]) => name);
  }
  getFailedPlugins() {
    return Array.from(this.pluginStates.entries()).filter(([_, state]) => state.status === "error" || state.status === "disabled").map(([name]) => name);
  }
  // 🎯 Set context for plugins
  setContext(context) {
    this.context = context;
  }
  // 🔄 Restart all failed plugins
  async restartFailedPlugins() {
    const failedPlugins = this.getFailedPlugins();
    for (const pluginName of failedPlugins) {
      const plugin = this.plugins.get(pluginName);
      const state = this.pluginStates.get(pluginName);
      if (plugin && state) {
        console.log(`\u{1F504} Restarting plugin ${pluginName}`);
        state.enabled = true;
        state.retryCount = 0;
        state.lastError = null;
        state.status = "initializing";
        try {
          await this.initializePlugin(plugin);
        } catch (error) {
          console.error(`Failed to restart plugin ${pluginName}:`, error);
        }
      }
    }
  }
  // 🧹 Cleanup all plugins
  async cleanup() {
    console.log("\u{1F9F9} Cleaning up plugins...");
    await this.executeHooks("destroy");
    this.plugins.clear();
    this.pluginStates.clear();
    this.hooks.clear();
    this.context = null;
    this.setupDefaultHooks();
  }
};
var globalPluginSystem = null;
function getPluginSystem() {
  if (!globalPluginSystem) {
    globalPluginSystem = new KilatPluginSystem();
  }
  return globalPluginSystem;
}
function usePluginSystem() {
  const pluginSystem = getPluginSystem();
  return {
    registerPlugin: pluginSystem.registerPlugin.bind(pluginSystem),
    enablePlugin: pluginSystem.enablePlugin.bind(pluginSystem),
    disablePlugin: pluginSystem.disablePlugin.bind(pluginSystem),
    unregisterPlugin: pluginSystem.unregisterPlugin.bind(pluginSystem),
    getPluginState: pluginSystem.getPluginState.bind(pluginSystem),
    getAllPluginStates: pluginSystem.getAllPluginStates.bind(pluginSystem),
    getActivePlugins: pluginSystem.getActivePlugins.bind(pluginSystem),
    getFailedPlugins: pluginSystem.getFailedPlugins.bind(pluginSystem),
    restartFailedPlugins: pluginSystem.restartFailedPlugins.bind(pluginSystem)
  };
}

// packages/kilat-core/src/context.tsx
import { jsx } from "react/jsx-runtime";
var useKilatStore = create()(
  persist(
    (set, get) => ({
      config: null,
      theme: "cyberpunk",
      mode: "dark",
      isLoading: false,
      error: null,
      setConfig: (config) => set({ config }),
      setTheme: (theme) => {
        set({ theme });
        document.documentElement.setAttribute("data-kilat-theme", theme);
      },
      setMode: (mode) => {
        set({ mode });
        document.documentElement.setAttribute("data-kilat-mode", mode);
      },
      setLoading: (isLoading) => set({ isLoading }),
      setError: (error) => set({ error })
    }),
    {
      name: "kilat-store",
      partialize: (state) => ({
        theme: state.theme,
        mode: state.mode
      })
    }
  )
);
var KilatContext = createContext(null);
function KilatProvider({ children, config }) {
  const store = useKilatStore();
  useEffect(() => {
    store.setConfig(config);
    store.setTheme(config.theme);
    store.setMode(config.mode);
    if (config.errorRecovery?.enabled) {
      initializeErrorRecovery(config.errorRecovery);
    }
    const pluginSystem = getPluginSystem();
    const contextValue2 = {
      config,
      theme: config.theme,
      mode: config.mode,
      setTheme: store.setTheme,
      setMode: store.setMode,
      isLoading: store.isLoading,
      error: store.error
    };
    pluginSystem.setContext(contextValue2);
  }, [config, store]);
  useEffect(() => {
    document.documentElement.setAttribute("data-kilat-theme", store.theme);
    document.documentElement.setAttribute("data-kilat-mode", store.mode);
    document.documentElement.classList.add("kilat");
    return () => {
      document.documentElement.classList.remove("kilat");
    };
  }, [store.theme, store.mode]);
  useEffect(() => {
    if (store.mode === "auto") {
      const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
      const handleChange = (e) => {
        document.documentElement.setAttribute(
          "data-kilat-mode",
          e.matches ? "dark" : "light"
        );
      };
      mediaQuery.addEventListener("change", handleChange);
      document.documentElement.setAttribute(
        "data-kilat-mode",
        mediaQuery.matches ? "dark" : "light"
      );
      return () => mediaQuery.removeEventListener("change", handleChange);
    }
  }, [store.mode]);
  const contextValue = {
    config: store.config || config,
    theme: store.theme,
    mode: store.mode,
    setTheme: store.setTheme,
    setMode: store.setMode,
    isLoading: store.isLoading,
    error: store.error
  };
  return /* @__PURE__ */ jsx(KilatContext.Provider, { value: contextValue, children });
}
function useKilat() {
  const context = useContext(KilatContext);
  if (!context) {
    throw new Error("useKilat must be used within a KilatProvider");
  }
  return context;
}
function useTheme() {
  const { theme, setTheme, mode, setMode } = useKilat();
  return {
    theme,
    mode,
    setTheme,
    setMode,
    isDark: mode === "dark" || mode === "auto" && window.matchMedia("(prefers-color-scheme: dark)").matches,
    isLight: mode === "light" || mode === "auto" && !window.matchMedia("(prefers-color-scheme: dark)").matches
  };
}
function useLoading() {
  const store = useKilatStore();
  return {
    isLoading: store.isLoading,
    setLoading: store.setLoading
  };
}
function useError() {
  const store = useKilatStore();
  return {
    error: store.error,
    setError: store.setError,
    clearError: () => store.setError(null)
  };
}

// packages/kilat-core/src/layout.tsx
import { Suspense } from "react";

// node_modules/react-error-boundary/dist/react-error-boundary.esm.js
import { createContext as createContext2, Component, createElement, useContext as useContext2, useState as useState2, useMemo, forwardRef } from "react";
var ErrorBoundaryContext = createContext2(null);
var initialState = {
  didCatch: false,
  error: null
};
var ErrorBoundary = class extends Component {
  constructor(props) {
    super(props);
    this.resetErrorBoundary = this.resetErrorBoundary.bind(this);
    this.state = initialState;
  }
  static getDerivedStateFromError(error) {
    return {
      didCatch: true,
      error
    };
  }
  resetErrorBoundary() {
    const {
      error
    } = this.state;
    if (error !== null) {
      var _this$props$onReset, _this$props;
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      (_this$props$onReset = (_this$props = this.props).onReset) === null || _this$props$onReset === void 0 ? void 0 : _this$props$onReset.call(_this$props, {
        args,
        reason: "imperative-api"
      });
      this.setState(initialState);
    }
  }
  componentDidCatch(error, info) {
    var _this$props$onError, _this$props2;
    (_this$props$onError = (_this$props2 = this.props).onError) === null || _this$props$onError === void 0 ? void 0 : _this$props$onError.call(_this$props2, error, info);
  }
  componentDidUpdate(prevProps, prevState) {
    const {
      didCatch
    } = this.state;
    const {
      resetKeys
    } = this.props;
    if (didCatch && prevState.error !== null && hasArrayChanged(prevProps.resetKeys, resetKeys)) {
      var _this$props$onReset2, _this$props3;
      (_this$props$onReset2 = (_this$props3 = this.props).onReset) === null || _this$props$onReset2 === void 0 ? void 0 : _this$props$onReset2.call(_this$props3, {
        next: resetKeys,
        prev: prevProps.resetKeys,
        reason: "keys"
      });
      this.setState(initialState);
    }
  }
  render() {
    const {
      children,
      fallbackRender,
      FallbackComponent,
      fallback
    } = this.props;
    const {
      didCatch,
      error
    } = this.state;
    let childToRender = children;
    if (didCatch) {
      const props = {
        error,
        resetErrorBoundary: this.resetErrorBoundary
      };
      if (typeof fallbackRender === "function") {
        childToRender = fallbackRender(props);
      } else if (FallbackComponent) {
        childToRender = createElement(FallbackComponent, props);
      } else if (fallback !== void 0) {
        childToRender = fallback;
      } else {
        throw error;
      }
    }
    return createElement(ErrorBoundaryContext.Provider, {
      value: {
        didCatch,
        error,
        resetErrorBoundary: this.resetErrorBoundary
      }
    }, childToRender);
  }
};
function hasArrayChanged() {
  let a = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];
  let b = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];
  return a.length !== b.length || a.some((item, index) => !Object.is(item, b[index]));
}

// packages/kilat-core/src/layout.tsx
import { jsx as jsx2, jsxs } from "react/jsx-runtime";
function ErrorFallback({ error, resetErrorBoundary }) {
  return /* @__PURE__ */ jsx2("div", { className: "k-error-boundary k-min-h-screen k-flex k-items-center k-justify-center k-bg-dark k-text-light", children: /* @__PURE__ */ jsxs("div", { className: "k-text-center k-p-8", children: [
    /* @__PURE__ */ jsx2("div", { className: "k-text-6xl k-mb-4", children: "\u26A1" }),
    /* @__PURE__ */ jsx2("h1", { className: "k-text-2xl k-font-bold k-mb-4 k-text-glow-red", children: "Oops! Something went wrong" }),
    /* @__PURE__ */ jsx2("p", { className: "k-text-gray-400 k-mb-6 k-max-w-md", children: error.message || "An unexpected error occurred" }),
    /* @__PURE__ */ jsx2(
      "button",
      {
        onClick: resetErrorBoundary,
        className: "k-btn k-bg-neon-blue k-text-dark k-px-6 k-py-3 k-rounded-lg k-font-semibold k-transition-all k-duration-300 hover:k-scale-105",
        children: "Try Again"
      }
    ),
    /* @__PURE__ */ jsxs("details", { className: "k-mt-6 k-text-left k-bg-gray-900 k-p-4 k-rounded k-text-sm", children: [
      /* @__PURE__ */ jsx2("summary", { className: "k-cursor-pointer k-text-yellow-400", children: "Error Details (Development)" }),
      /* @__PURE__ */ jsx2("pre", { className: "k-mt-2 k-text-red-400 k-overflow-auto", children: error.stack })
    ] })
  ] }) });
}
function LoadingFallback() {
  return /* @__PURE__ */ jsx2("div", { className: "k-loading-fallback k-min-h-screen k-flex k-items-center k-justify-center k-bg-dark", children: /* @__PURE__ */ jsxs("div", { className: "k-text-center", children: [
    /* @__PURE__ */ jsx2("div", { className: "k-animate-spin k-text-6xl k-mb-4 k-text-glow-blue", children: "\u26A1" }),
    /* @__PURE__ */ jsx2("p", { className: "k-text-light k-text-lg k-animate-pulse", children: "Loading Kilat.js..." })
  ] }) });
}
function KilatLayout({
  children,
  theme,
  mode,
  className = ""
}) {
  const { config } = useKilat();
  const { theme: currentTheme, mode: currentMode } = useTheme();
  const appliedTheme = theme || currentTheme;
  const appliedMode = mode || currentMode;
  return /* @__PURE__ */ jsx2(
    "div",
    {
      className: `kilat-layout ${className}`,
      "data-kilat-theme": appliedTheme,
      "data-kilat-mode": appliedMode,
      children: /* @__PURE__ */ jsx2(
        ErrorBoundary,
        {
          FallbackComponent: ErrorFallback,
          onError: (error, errorInfo) => {
            console.error("Kilat.js Error:", error, errorInfo);
            if (config?.dev?.hmr?.overlay) {
            }
          },
          children: /* @__PURE__ */ jsx2(Suspense, { fallback: /* @__PURE__ */ jsx2(LoadingFallback, {}), children })
        }
      )
    }
  );
}
function LayoutManager({
  children,
  isSSR = false,
  layoutComponent: CustomLayout,
  layoutProps = {}
}) {
  const { config } = useKilat();
  if (isSSR) {
    return /* @__PURE__ */ jsx2("div", { className: "kilat-ssr-layout", suppressHydrationWarning: true, children: CustomLayout ? /* @__PURE__ */ jsx2(CustomLayout, { ...layoutProps, children }) : /* @__PURE__ */ jsx2(KilatLayout, { ...layoutProps, children }) });
  }
  return /* @__PURE__ */ jsx2(
    ErrorBoundary,
    {
      FallbackComponent: ErrorFallback,
      onReset: () => {
        window.location.reload();
      },
      children: CustomLayout ? /* @__PURE__ */ jsx2(CustomLayout, { ...layoutProps, children: /* @__PURE__ */ jsx2(Suspense, { fallback: /* @__PURE__ */ jsx2(LoadingFallback, {}), children }) }) : /* @__PURE__ */ jsx2(KilatLayout, { ...layoutProps, children })
    }
  );
}
function useLayout() {
  const { config } = useKilat();
  return {
    setLayout: (layoutComponent, props) => {
    },
    getCurrentLayout: () => {
    },
    isSSR: typeof window === "undefined",
    config: config?.platform
  };
}
function DevLayout({ children }) {
  const { config, theme, mode } = useKilat();
  if (false) {
    return /* @__PURE__ */ jsx2(Fragment, { children });
  }
  return /* @__PURE__ */ jsxs("div", { className: "kilat-dev-layout", children: [
    config?.build?.debugOverlay && /* @__PURE__ */ jsxs("div", { className: "k-fixed k-top-0 k-right-0 k-z-50 k-bg-black k-bg-opacity-80 k-text-white k-p-2 k-text-xs k-font-mono", children: [
      /* @__PURE__ */ jsxs("div", { children: [
        "Theme: ",
        theme
      ] }),
      /* @__PURE__ */ jsxs("div", { children: [
        "Mode: ",
        mode
      ] }),
      /* @__PURE__ */ jsxs("div", { children: [
        "Platform: ",
        config.platform.web.enabled ? "Web" : "Unknown"
      ] }),
      /* @__PURE__ */ jsxs("div", { children: [
        "HMR: ",
        config.build.hotReload ? "\u{1F7E2}" : "\u{1F534}"
      ] })
    ] }),
    /* @__PURE__ */ jsx2(KilatLayout, { children })
  ] });
}

// packages/kilat-core/src/platform.ts
import React3 from "react";
var KilatPlatform = class _KilatPlatform {
  static instance;
  platformInfo;
  constructor() {
    this.platformInfo = this.detectPlatform();
    this.setupPlatformAttributes();
  }
  static getInstance() {
    if (!_KilatPlatform.instance) {
      _KilatPlatform.instance = new _KilatPlatform();
    }
    return _KilatPlatform.instance;
  }
  // 🔍 Detect current platform
  detectPlatform() {
    const isSSR = typeof window === "undefined";
    if (isSSR) {
      return {
        type: "web",
        isWeb: true,
        isDesktop: false,
        isMobile: false,
        userAgent: "SSR",
        viewport: { width: 0, height: 0 }
      };
    }
    const userAgent = navigator.userAgent;
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };
    const isElectron = !!window.require || !!window.process?.type || !!window.electronAPI;
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent) || window.innerWidth <= 768 && "ontouchstart" in window;
    const isDesktop = !isMobile || isElectron;
    let type = "web";
    if (isElectron) {
      type = "desktop";
    } else if (isMobile) {
      type = "mobile";
    }
    return {
      type,
      isWeb: !isElectron && !isMobile,
      isDesktop,
      isMobile,
      userAgent,
      viewport
    };
  }
  // 🏷️ Setup platform attributes on document
  setupPlatformAttributes() {
    if (typeof document === "undefined")
      return;
    const { type, isWeb, isDesktop, isMobile } = this.platformInfo;
    document.documentElement.setAttribute("data-kilat-platform", type);
    if (isWeb)
      document.documentElement.setAttribute("data-kilat-web", "true");
    if (isDesktop)
      document.documentElement.setAttribute("data-kilat-desktop", "true");
    if (isMobile)
      document.documentElement.setAttribute("data-kilat-mobile", "true");
    this.updateViewportClasses();
    window.addEventListener("resize", () => {
      this.updateViewport();
      this.updateViewportClasses();
    });
  }
  // 📱 Update viewport information
  updateViewport() {
    if (typeof window === "undefined")
      return;
    this.platformInfo.viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };
  }
  // 📐 Update viewport classes for responsive design
  updateViewportClasses() {
    if (typeof document === "undefined")
      return;
    const { width } = this.platformInfo.viewport;
    const root = document.documentElement;
    root.classList.remove("k-xs", "k-sm", "k-md", "k-lg", "k-xl", "k-2xl");
    if (width < 640) {
      root.classList.add("k-xs");
    } else if (width < 768) {
      root.classList.add("k-sm");
    } else if (width < 1024) {
      root.classList.add("k-md");
    } else if (width < 1280) {
      root.classList.add("k-lg");
    } else if (width < 1536) {
      root.classList.add("k-xl");
    } else {
      root.classList.add("k-2xl");
    }
  }
  // 🔍 Get platform information
  getPlatformInfo() {
    return { ...this.platformInfo };
  }
  // 🌐 Platform check methods
  isWeb() {
    return this.platformInfo.isWeb;
  }
  isDesktop() {
    return this.platformInfo.isDesktop;
  }
  isMobile() {
    return this.platformInfo.isMobile;
  }
  getType() {
    return this.platformInfo.type;
  }
  // 📱 Device-specific checks
  isIOS() {
    return /iPad|iPhone|iPod/.test(this.platformInfo.userAgent);
  }
  isAndroid() {
    return /Android/.test(this.platformInfo.userAgent);
  }
  isElectron() {
    return this.platformInfo.type === "desktop" && (typeof window !== "undefined" && (!!window.require || !!window.process?.type || !!window.electronAPI));
  }
  // 🔧 Browser detection
  getBrowser() {
    const userAgent = this.platformInfo.userAgent;
    if (userAgent.includes("Chrome"))
      return "chrome";
    if (userAgent.includes("Firefox"))
      return "firefox";
    if (userAgent.includes("Safari") && !userAgent.includes("Chrome"))
      return "safari";
    if (userAgent.includes("Edge"))
      return "edge";
    if (userAgent.includes("Opera"))
      return "opera";
    return "unknown";
  }
  // 📐 Viewport utilities
  getViewport() {
    return { ...this.platformInfo.viewport };
  }
  isPortrait() {
    const { width, height } = this.platformInfo.viewport;
    return height > width;
  }
  isLandscape() {
    return !this.isPortrait();
  }
  // 🎯 Feature detection
  supportsTouch() {
    return typeof window !== "undefined" && "ontouchstart" in window;
  }
  supportsWebGL() {
    if (typeof window === "undefined")
      return false;
    try {
      const canvas = document.createElement("canvas");
      return !!(canvas.getContext("webgl") || canvas.getContext("experimental-webgl"));
    } catch {
      return false;
    }
  }
  supportsWebAssembly() {
    return typeof WebAssembly !== "undefined";
  }
  supportsServiceWorker() {
    return typeof navigator !== "undefined" && "serviceWorker" in navigator;
  }
  // 🔄 Refresh platform detection
  refresh() {
    this.platformInfo = this.detectPlatform();
    this.setupPlatformAttributes();
  }
};
function usePlatform() {
  const platform = KilatPlatform.getInstance();
  return {
    ...platform.getPlatformInfo(),
    isWeb: platform.isWeb(),
    isDesktop: platform.isDesktop(),
    isMobile: platform.isMobile(),
    isIOS: platform.isIOS(),
    isAndroid: platform.isAndroid(),
    isElectron: platform.isElectron(),
    browser: platform.getBrowser(),
    viewport: platform.getViewport(),
    isPortrait: platform.isPortrait(),
    isLandscape: platform.isLandscape(),
    supportsTouch: platform.supportsTouch(),
    supportsWebGL: platform.supportsWebGL(),
    supportsWebAssembly: platform.supportsWebAssembly(),
    supportsServiceWorker: platform.supportsServiceWorker(),
    refresh: platform.refresh.bind(platform)
  };
}
var kilatPlatform = KilatPlatform.getInstance();
function withPlatform(Component2) {
  return function PlatformAwareComponent(props) {
    const platform = usePlatform();
    return React3.createElement(Component2, {
      ...props,
      platform
    });
  };
}
function PlatformRenderer({
  web,
  desktop,
  mobile,
  fallback
}) {
  const platform = usePlatform();
  if (platform.isWeb && web)
    return web;
  if (platform.isDesktop && desktop)
    return desktop;
  if (platform.isMobile && mobile)
    return mobile;
  return fallback || null;
}

// packages/kilat-core/src/index.ts
var KILAT_THEMES = [
  "cyberpunk",
  "nusantara",
  "retro",
  "material",
  "neumorphism",
  "carbon",
  "minimalist",
  "asymetric",
  "elemen3d",
  "dana",
  "ark",
  "aurora",
  "unix",
  "classic"
];
var KILAT_ANIMATION_PRESETS = [
  "galaxy",
  "matrix",
  "neonTunnel",
  "cyberwave",
  "glowParticles"
];
function createKilatConfig(config) {
  const defaultConfig = {
    theme: "cyberpunk",
    mode: "dark",
    presetScene: "galaxy",
    animation: {
      autoRotate: true,
      background: "#000",
      interactive: true,
      ambientSound: false
    },
    router: {
      basePath: "/",
      middleware: [],
      fileBasedRouting: true,
      dynamicRoutes: true,
      layoutNesting: true
    },
    database: {
      driver: "sqlite",
      connection: {
        sqlite: {
          file: "./data.db",
          enableWAL: true,
          timeout: 5e3
        }
      },
      migrations: {
        directory: "./migrations",
        autoRun: true
      }
    },
    build: {
      engine: "kilatpack",
      target: "es2022",
      minify: true,
      sourcemap: true,
      debugOverlay: false,
      hotReload: true,
      analyze: false,
      outputDir: "dist",
      publicDir: "public",
      assetsDir: "assets"
    },
    plugins: [],
    aiAssistant: {
      enabled: false,
      endpoint: "/api/ai",
      model: "gpt-4",
      features: []
    },
    platform: {
      web: {
        enabled: true,
        ssr: true,
        pwa: true
      },
      desktop: {
        enabled: false,
        electron: true,
        tauri: false
      },
      mobile: {
        enabled: false,
        expo: true,
        capacitor: false
      }
    },
    dev: {
      port: 3e3,
      host: "localhost",
      open: true,
      cors: true,
      proxy: {},
      hmr: {
        port: 24678,
        overlay: true
      }
    },
    performance: {
      bundleSplitting: true,
      treeshaking: true,
      compression: "gzip",
      caching: {
        enabled: true,
        strategy: "stale-while-revalidate"
      }
    },
    security: {
      csp: {
        enabled: true,
        directives: {
          "default-src": ["'self'"],
          "script-src": ["'self'", "'unsafe-inline'"],
          "style-src": ["'self'", "'unsafe-inline'"],
          "img-src": ["'self'", "data:", "https:"]
        }
      },
      cors: {
        origin: ["http://localhost:3000"],
        credentials: true
      }
    },
    errorRecovery: {
      enabled: true,
      maxRetries: 3,
      retryDelay: 1e3,
      fallbackMode: true,
      crashReport: {
        enabled: true,
        includeLogs: true,
        autoRetry: true
      },
      killSwitch: true,
      safeMode: true
    },
    monitoring: {
      enabled: false,
      performance: true,
      errors: true,
      analytics: false,
      sampleRate: 0.1
    }
  };
  return { ...defaultConfig, ...config };
}
function detectPlatform() {
  if (typeof window === "undefined")
    return "web";
  const userAgent = navigator.userAgent.toLowerCase();
  if (/electron/.test(userAgent))
    return "desktop";
  if (/mobile|android|iphone|ipad/.test(userAgent))
    return "mobile";
  return "web";
}
function getPlatformInfo() {
  const type = detectPlatform();
  return {
    type,
    isWeb: type === "web",
    isDesktop: type === "desktop",
    isMobile: type === "mobile",
    userAgent: typeof navigator !== "undefined" ? navigator.userAgent : "",
    viewport: {
      width: typeof window !== "undefined" ? window.innerWidth : 0,
      height: typeof window !== "undefined" ? window.innerHeight : 0
    }
  };
}
var KILAT_VERSION = "1.0.0";
var KILAT_CORE_VERSION = "1.0.0";
export {
  DevLayout,
  KILAT_ANIMATION_PRESETS,
  KILAT_CORE_VERSION,
  KILAT_THEMES,
  KILAT_VERSION,
  KilatErrorRecovery,
  KilatLayout,
  KilatPlatform,
  KilatPluginSystem,
  KilatProvider,
  LayoutManager,
  PlatformRenderer,
  createKilatConfig,
  detectPlatform,
  getErrorRecovery,
  getPlatformInfo,
  getPluginSystem,
  initializeErrorRecovery,
  kilatPlatform,
  useError,
  useKilat,
  useKilatStore,
  useLayout,
  useLoading,
  usePlatform,
  usePluginSystem,
  useTheme,
  withPlatform
};
/*! Bundled license information:

use-sync-external-store/cjs/use-sync-external-store-shim.development.js:
  (**
   * @license React
   * use-sync-external-store-shim.development.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js:
  (**
   * @license React
   * use-sync-external-store-shim/with-selector.development.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)
*/
