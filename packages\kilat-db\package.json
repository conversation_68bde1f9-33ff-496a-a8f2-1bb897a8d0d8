{"name": "kilat-db", "version": "1.0.0", "description": "⚡ Kilat.js Database - Universal ORM for SQLite and MySQL with offline capabilities", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.esm.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./models": {"import": "./dist/models.esm.js", "require": "./dist/models.js", "types": "./dist/models.d.ts"}, "./migrations": {"import": "./dist/migrations.esm.js", "require": "./dist/migrations.js", "types": "./dist/migrations.d.ts"}}, "files": ["dist", "README.md"], "scripts": {"build": "bun run build:esm && bun run build:cjs && bun run build:types", "build:esm": "esbuild src/index.ts --bundle --format=esm --outfile=dist/index.esm.js --external:better-sqlite3 --external:mysql2", "build:cjs": "esbuild src/index.ts --bundle --format=cjs --outfile=dist/index.js --external:better-sqlite3 --external:mysql2", "build:types": "tsc --emitDeclarationOnly --outDir dist", "dev": "bun run build --watch", "test": "bun test", "clean": "rm -rf dist"}, "keywords": ["kilat", "database", "orm", "sqlite", "mysql", "offline", "migrations", "typescript"], "author": "Kilat.js Team", "license": "MIT", "dependencies": {"better-sqlite3": "^9.2.2", "mysql2": "^3.6.5", "kilat-utils": "workspace:*"}, "devDependencies": {"esbuild": "^0.19.8", "typescript": "^5.3.0", "@types/better-sqlite3": "^7.6.8"}, "optionalDependencies": {"better-sqlite3": "^9.2.2", "mysql2": "^3.6.5"}}