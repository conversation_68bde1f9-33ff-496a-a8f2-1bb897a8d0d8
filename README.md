# Kilat.js ⚡

> **Framework Glow Futuristik Nusantara** - Modular, Fast, and Beautiful UI Framework for Web, Desktop & Mobile

[![npm version](https://badge.fury.io/js/kilatjs.svg)](https://badge.fury.io/js/kilatjs)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Build Status](https://github.com/kangpcode/kilatjs/workflows/CI/badge.svg)](https://github.com/kangpcode/kilatjs/actions)

## 🌟 Features

- ⚡ **Lightning Fast** - Built with Bun, Vite, and modern tooling
- 🎨 **Glow UI System** - 15+ beautiful themes including Cyberpunk & Nusantara
- 🌌 **3D Animations** - Preset scenes with Three.js integration
- 🔀 **Auto Routing** - File-based routing like Next.js
- 🗃️ **Universal ORM** - SQLite & MySQL support with offline capabilities
- 🔌 **Plugin System** - Extensible architecture with auth, i18n, payments
- 📱 **Multi-Platform** - Web, Desktop (Electron), Mobile (Expo)
- 🧱 **Monorepo Ready** - Scalable workspace structure

## 🚀 Quick Start

```bash
# Create new Kilat.js project
npx kilat create my-app

# Choose your platform
? Platform: Web / Desktop / Mobile
? Theme: Cyberpunk / Nusantara / Retro
? Database: SQLite / MySQL
? Plugins: Auth, Upload, i18n

# Start development
cd my-app
kilat dev
```

## 📦 Packages

| Package | Description | Version |
|---------|-------------|---------|
| `kilat-core` | Core engine with SSR & layout | ![npm](https://img.shields.io/npm/v/kilat-core) |
| `kilatcss` | Utility-first CSS with glow effects | ![npm](https://img.shields.io/npm/v/kilatcss) |
| `kilatanim.js` | 3D animation presets | ![npm](https://img.shields.io/npm/v/kilatanim.js) |
| `kilat-router` | File-based routing system | ![npm](https://img.shields.io/npm/v/kilat-router) |
| `kilat-cli` | Interactive CLI tools | ![npm](https://img.shields.io/npm/v/kilat-cli) |
| `kilat-db` | Universal ORM for SQLite/MySQL | ![npm](https://img.shields.io/npm/v/kilat-db) |

## 🎨 Themes

Choose from 15+ beautiful themes:

- 🔮 **Cyberpunk** - Neon glow futuristic
- 🇮🇩 **Nusantara** - Indonesian cultural colors
- 🕹️ **Retro** - 90s arcade pixel art
- 🧱 **Material** - Google Material Design
- 🧼 **Neumorphism** - Soft shadow inset
- 🌈 **Aurora** - Gradient blur pastels
- And many more...

## 🌌 3D Animations

```tsx
import { KilatScene } from 'kilatanim.js';

<KilatScene 
  preset="galaxy" 
  interactive 
  autoRotate 
  background="#000" 
/>
```

Available presets: `galaxy`, `matrix`, `neonTunnel`, `cyberwave`, `glowParticles`

## 🔀 Auto Routing

```
pages/
├── index.tsx          → /
├── about.tsx          → /about
├── blog/
│   ├── index.tsx      → /blog
│   └── [slug].tsx     → /blog/:slug
└── _layout.tsx        → Layout wrapper
```

## 🗃️ Database

```typescript
// Universal ORM API
await db.users.insert({ name: "Dhafa" });
await db.users.findMany({ where: { active: true } });
```

## 🔌 Plugins

```typescript
// kilat.config.ts
export default {
  plugins: ["auth", "upload", "i18n", "payments"]
};
```

## 📱 Multi-Platform

- **Web**: Vite + React with SSR
- **Desktop**: Electron integration
- **Mobile**: Expo React Native

## 🛠️ Development

```bash
# Install dependencies
bun install

# Start development
bun dev

# Build all packages
bun build

# Run tests
bun test

# Lint & format
bun lint
bun format
```

## 📚 Documentation

- [Getting Started](./docs/index.md)
- [kilatcss Guide](./docs/kilatcss.md)
- [3D Animations](./docs/kilatanim.md)
- [Routing](./docs/router.md)
- [Database](./docs/db.md)
- [Plugins](./docs/plugins.md)

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](./CONTRIBUTING.md).

## 📄 License

MIT © [Kilat.js Team](https://github.com/kangpcode/kilatjs)

---

**Made with ❤️ in Indonesia 🇮🇩**
