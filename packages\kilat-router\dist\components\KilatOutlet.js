import React from 'react';
import { Outlet } from 'react-router-dom';

/**
 * 🚪 KilatOutlet Component
 * Enhanced Outlet component with Kilat.js features
 */
export function KilatOutlet({ 
  loading: LoadingComponent, 
  error: ErrorComponent,
  fallback,
  ...props 
}) {
  return React.createElement(React.Suspense, {
    fallback: LoadingComponent 
      ? React.createElement(LoadingComponent) 
      : fallback || React.createElement('div', { className: 'k-loading' }, 'Loading...')
  }, React.createElement(Outlet, props));
}

export default KilatOutlet;
