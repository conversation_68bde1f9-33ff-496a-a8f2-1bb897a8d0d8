import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>outer, HashRouter, MemoryRouter, Routes, Route, useLocation, useNavigate, useParams } from 'react-router-dom';
import { ErrorBoundary } from 'react-error-boundary';
import { useKilat } from 'kilat-core';
import { useLogger } from 'kilat-utils';
import type { 
  KilatRouterConfig, 
  KilatRoute, 
  KilatRouterContext, 
  KilatMiddleware,
  NavigateOptions,
  RouteTransition
} from '../types';
import { createFileRoutes } from '../utils/fileRoutes';
import { matchRoute } from '../utils/routeMatcher';
import { RouteTransition as RouteTransitionComponent } from './RouteTransition';

// 🎯 Default Router Configuration
const defaultConfig: KilatRouterConfig = {
  basePath: '/',
  mode: 'browser',
  caseSensitive: false,
  trailingSlash: 'preserve',
  middleware: [],
  layouts: {},
  transitions: {
    enabled: true,
    duration: 300,
    type: 'fade'
  },
  preload: {
    enabled: true,
    strategy: 'hover'
  },
  cache: {
    enabled: true,
    ttl: 300000, // 5 minutes
    maxSize: 50
  }
};

// 🧭 Router Context
const KilatRouterContext = createContext<KilatRouterContext | null>(null);

// 🎯 Router Provider Props
interface KilatRouterProps {
  children: ReactNode;
  routes?: KilatRoute[];
  config?: Partial<KilatRouterConfig>;
  pagesDirectory?: string;
  fallback?: ReactNode;
  onRouteChange?: (route: KilatRoute) => void;
  onError?: (error: Error, route: KilatRoute) => void;
}

// 🔄 Route Cache
const routeCache = new Map<string, any>();
const preloadCache = new Set<string>();

// 🧭 Main Router Component
export function KilatRouter({
  children,
  routes: providedRoutes,
  config: userConfig = {},
  pagesDirectory = './pages',
  fallback,
  onRouteChange,
  onError
}: KilatRouterProps) {
  const { config: kilatConfig } = useKilat();
  const logger = useLogger();
  const [routes, setRoutes] = useState<KilatRoute[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  const routerConfig: KilatRouterConfig = {
    ...defaultConfig,
    ...kilatConfig?.router,
    ...userConfig
  };

  // 📁 Load file-based routes
  useEffect(() => {
    async function loadRoutes() {
      try {
        setIsLoading(true);
        setError(null);
        
        let finalRoutes: KilatRoute[];
        
        if (providedRoutes) {
          finalRoutes = providedRoutes;
        } else {
          // Generate routes from file system
          finalRoutes = await createFileRoutes(pagesDirectory);
        }
        
        setRoutes(finalRoutes);
        logger.info('Routes loaded successfully', { count: finalRoutes.length });
      } catch (err) {
        const error = err as Error;
        setError(error);
        logger.error('Failed to load routes', error);
      } finally {
        setIsLoading(false);
      }
    }
    
    loadRoutes();
  }, [providedRoutes, pagesDirectory, logger]);

  // 🎯 Get Router Component based on mode
  const RouterComponent = {
    browser: BrowserRouter,
    hash: HashRouter,
    memory: MemoryRouter
  }[routerConfig.mode];

  if (isLoading) {
    return fallback || (
      <div className="kilat-router-loading k-flex k-items-center k-justify-center k-min-h-screen">
        <div className="k-text-center">
          <div className="k-animate-spin k-text-4xl k-mb-4 k-text-glow-blue">⚡</div>
          <p className="k-text-light">Loading routes...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="kilat-router-error k-flex k-items-center k-justify-center k-min-h-screen">
        <div className="k-text-center k-p-8">
          <div className="k-text-6xl k-mb-4 k-text-red-500">⚠️</div>
          <h2 className="k-text-2xl k-font-bold k-mb-4 k-text-red-400">Router Error</h2>
          <p className="k-text-gray-400 k-mb-4">{error.message}</p>
          <button 
            onClick={() => window.location.reload()}
            className="k-btn k-btn-primary"
          >
            Reload Page
          </button>
        </div>
      </div>
    );
  }

  return (
    <RouterComponent basename={routerConfig.basePath}>
      <KilatRouterProvider
        config={routerConfig}
        routes={routes}
        onRouteChange={onRouteChange}
        onError={onError}
      >
        <ErrorBoundary
          FallbackComponent={RouterErrorFallback}
          onError={(error, errorInfo) => {
            logger.error('Router error boundary triggered', error, { errorInfo });
            if (onError) {
              onError(error, routes[0]); // Pass first route as fallback
            }
          }}
        >
          {routerConfig.transitions?.enabled ? (
            <RouteTransitionComponent config={routerConfig.transitions}>
              {children}
            </RouteTransitionComponent>
          ) : (
            children
          )}
        </ErrorBoundary>
      </KilatRouterProvider>
    </RouterComponent>
  );
}

// 🎯 Router Provider Component
interface KilatRouterProviderProps {
  children: ReactNode;
  config: KilatRouterConfig;
  routes: KilatRoute[];
  onRouteChange?: (route: KilatRoute) => void;
  onError?: (error: Error, route: KilatRoute) => void;
}

function KilatRouterProvider({
  children,
  config,
  routes,
  onRouteChange,
  onError
}: KilatRouterProviderProps) {
  const location = useLocation();
  const navigate = useNavigate();
  const params = useParams();
  const logger = useLogger();

  const [currentRoute, setCurrentRoute] = useState<KilatRoute | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // 🎯 Find current route
  useEffect(() => {
    const match = matchRoute(location.pathname, routes);
    if (match) {
      setCurrentRoute(match.route);
      if (onRouteChange) {
        onRouteChange(match.route);
      }
    }
  }, [location.pathname, routes, onRouteChange]);

  // 🧭 Navigation function with middleware support
  const kilatNavigate = async (to: string, options: NavigateOptions = {}) => {
    try {
      setIsLoading(true);
      setError(null);

      // Find target route
      const match = matchRoute(to, routes);
      if (!match) {
        throw new Error(`Route not found: ${to}`);
      }

      // Run middleware
      if (match.route.middleware) {
        for (const middleware of match.route.middleware) {
          const context = {
            route: match.route,
            params: match.params,
            query: new URLSearchParams(location.search),
            pathname: to,
            search: location.search,
            hash: location.hash,
            state: options.state,
            navigate: kilatNavigate,
            redirect: (redirectTo: string, redirectOptions?: NavigateOptions) => {
              navigate(redirectTo, redirectOptions);
            },
            abort: (reason?: string) => {
              throw new Error(reason || 'Navigation aborted');
            }
          };

          const result = await middleware(context);
          if (result === false) {
            logger.warn('Navigation blocked by middleware', { to, middleware: middleware.name });
            return;
          }
        }
      }

      // Perform navigation
      navigate(to, options);

    } catch (err) {
      const error = err as Error;
      setError(error);
      logger.error('Navigation failed', error, { to, options });
      if (onError && currentRoute) {
        onError(error, currentRoute);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // 🔄 Preload function
  const preload = async (path: string): Promise<void> => {
    if (preloadCache.has(path)) return;

    try {
      const match = matchRoute(path, routes);
      if (match && match.route.component) {
        // Preload component if it's lazy loaded
        if (typeof match.route.component === 'function') {
          await match.route.component();
        }
        preloadCache.add(path);
        logger.debug('Route preloaded', { path });
      }
    } catch (error) {
      logger.warn('Failed to preload route', error as Error, { path });
    }
  };

  const contextValue: KilatRouterContext = {
    config,
    currentRoute,
    params: params as Record<string, string>,
    query: new URLSearchParams(location.search),
    navigate: kilatNavigate,
    back: () => window.history.back(),
    forward: () => window.history.forward(),
    refresh: () => window.location.reload(),
    preload,
    isLoading,
    error
  };

  return (
    <KilatRouterContext.Provider value={contextValue}>
      {children}
    </KilatRouterContext.Provider>
  );
}

// 🚨 Error Fallback Component
function RouterErrorFallback({ error, resetErrorBoundary }: any) {
  return (
    <div className="kilat-router-error k-min-h-screen k-flex k-items-center k-justify-center k-bg-dark">
      <div className="k-text-center k-p-8">
        <div className="k-text-6xl k-mb-4 k-text-red-500">💥</div>
        <h2 className="k-text-2xl k-font-bold k-mb-4 k-text-red-400">Something went wrong</h2>
        <p className="k-text-gray-400 k-mb-6">{error.message}</p>
        <div className="k-space-x-4">
          <button
            onClick={resetErrorBoundary}
            className="k-btn k-btn-primary"
          >
            Try Again
          </button>
          <button
            onClick={() => window.location.href = '/'}
            className="k-btn k-btn-secondary"
          >
            Go Home
          </button>
        </div>
      </div>
    </div>
  );
}

// 🪝 Hook to use router context
export function useKilatRouter(): KilatRouterContext {
  const context = useContext(KilatRouterContext);
  if (!context) {
    throw new Error('useKilatRouter must be used within a KilatRouter');
  }
  return context;
}
