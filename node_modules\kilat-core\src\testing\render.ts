import React from 'react';
import { render as rtlRender, RenderOptions as RTLRenderOptions } from '@testing-library/react';
import { KilatProvider } from '../context/KilatProvider';
import { ThemeProvider } from '../context/ThemeProvider';
import { RouterProvider } from '../context/RouterProvider';
import { AuthProvider } from '../context/AuthProvider';
import type { KilatConfig } from '../types';

/**
 * 🧪 Enhanced Render Function
 * Renders components with all necessary providers for testing
 */

interface RenderOptions extends Omit<RTLRenderOptions, 'wrapper'> {
  kilatConfig?: Partial<KilatConfig>;
  theme?: string;
  mode?: 'light' | 'dark' | 'system';
  initialRoute?: string;
  user?: any;
  wrapper?: React.ComponentType<any>;
}

interface RenderResult extends ReturnType<typeof rtlRender> {
  user: any;
  history: any;
  store: any;
}

/**
 * Custom render function that wraps components with providers
 */
export function render(
  ui: React.ReactElement,
  options: RenderOptions = {}
): RenderResult {
  const {
    kilatConfig = {},
    theme = 'cyberpunk',
    mode = 'dark',
    initialRoute = '/',
    user = null,
    wrapper,
    ...renderOptions
  } = options;

  // Default Kilat config for testing
  const defaultConfig: KilatConfig = {
    theme,
    mode,
    router: {
      basePath: '/',
      middleware: []
    },
    errorHandling: {
      enabled: false // Disable in tests
    },
    development: {
      debugMode: false
    },
    ...kilatConfig
  };

  // Create test providers wrapper
  function TestProviders({ children }: { children: React.ReactNode }) {
    return (
      <KilatProvider config={defaultConfig}>
        <ThemeProvider theme={theme} mode={mode}>
          <RouterProvider initialRoute={initialRoute}>
            <AuthProvider user={user}>
              {children}
            </AuthProvider>
          </RouterProvider>
        </ThemeProvider>
      </KilatProvider>
    );
  }

  // Use custom wrapper if provided
  const Wrapper = wrapper ? wrapper : TestProviders;

  // Render with providers
  const result = rtlRender(ui, {
    wrapper: Wrapper,
    ...renderOptions
  });

  // Return enhanced result with additional utilities
  return {
    ...result,
    user,
    history: window.history,
    store: {} // Add store reference if using state management
  };
}

/**
 * 🔄 Re-render utility
 */
export function rerender(
  ui: React.ReactElement,
  options: RenderOptions = {}
) {
  return render(ui, options);
}

/**
 * 🧹 Cleanup utility
 */
export function cleanup() {
  // Clean up any test artifacts
  if (typeof window !== 'undefined') {
    // Clear localStorage
    window.localStorage.clear();
    
    // Clear sessionStorage
    window.sessionStorage.clear();
    
    // Reset document title
    document.title = 'Test';
    
    // Clear any timers
    jest.clearAllTimers();
  }
}

/**
 * 🎭 Render with specific theme
 */
export function renderWithTheme(
  ui: React.ReactElement,
  theme: string = 'cyberpunk',
  options: RenderOptions = {}
) {
  return render(ui, { ...options, theme });
}

/**
 * 🧭 Render with router
 */
export function renderWithRouter(
  ui: React.ReactElement,
  initialRoute: string = '/',
  options: RenderOptions = {}
) {
  return render(ui, { ...options, initialRoute });
}

/**
 * 👤 Render with authenticated user
 */
export function renderWithAuth(
  ui: React.ReactElement,
  user: any,
  options: RenderOptions = {}
) {
  return render(ui, { ...options, user });
}

/**
 * 📱 Render with platform context
 */
export function renderWithPlatform(
  ui: React.ReactElement,
  platform: 'web' | 'mobile' | 'desktop' = 'web',
  options: RenderOptions = {}
) {
  // Mock platform detection
  Object.defineProperty(window.navigator, 'userAgent', {
    value: platform === 'mobile' 
      ? 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)'
      : platform === 'desktop'
      ? 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)'
      : 'Mozilla/5.0 (X11; Linux x86_64)',
    configurable: true
  });

  return render(ui, options);
}

/**
 * 🌐 Render with specific viewport
 */
export function renderWithViewport(
  ui: React.ReactElement,
  width: number = 1024,
  height: number = 768,
  options: RenderOptions = {}
) {
  // Mock viewport size
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: width
  });
  
  Object.defineProperty(window, 'innerHeight', {
    writable: true,
    configurable: true,
    value: height
  });

  // Trigger resize event
  window.dispatchEvent(new Event('resize'));

  return render(ui, options);
}

/**
 * 🎨 Render with custom CSS variables
 */
export function renderWithCSSVariables(
  ui: React.ReactElement,
  variables: Record<string, string>,
  options: RenderOptions = {}
) {
  // Set CSS variables on document root
  Object.entries(variables).forEach(([key, value]) => {
    document.documentElement.style.setProperty(key, value);
  });

  const result = render(ui, options);

  // Cleanup function to remove variables
  const originalCleanup = result.unmount;
  result.unmount = () => {
    Object.keys(variables).forEach(key => {
      document.documentElement.style.removeProperty(key);
    });
    originalCleanup();
  };

  return result;
}

/**
 * 🔧 Render with custom configuration
 */
export function renderWithConfig(
  ui: React.ReactElement,
  config: Partial<KilatConfig>,
  options: RenderOptions = {}
) {
  return render(ui, { ...options, kilatConfig: config });
}
