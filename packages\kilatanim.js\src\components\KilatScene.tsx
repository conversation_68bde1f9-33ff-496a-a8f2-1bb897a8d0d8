import React, { useRef, useEffect, useState, Suspense } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { OrbitControls, Stats } from '@react-three/drei';
import * as THREE from 'three';
import type { Ki<PERSON>SceneProps, KilatAnimationConfig, PerformanceMetrics } from '../types';
import { GalaxyPreset } from '../presets/Galaxy';
import { MatrixPreset } from '../presets/Matrix';
import { NeonTunnelPreset } from '../presets/NeonTunnel';
import { CyberwavePreset } from '../presets/Cyberwave';
import { GlowParticlesPreset } from '../presets/GlowParticles';

// 🎯 Scene Content Component
function SceneContent({ 
  preset, 
  config, 
  interactive, 
  autoRotate,
  onLoad,
  onError 
}: {
  preset: KilatSceneProps['preset'];
  config?: KilatAnimationConfig;
  interactive?: boolean;
  autoRotate?: boolean;
  onLoad?: () => void;
  onError?: (error: Error) => void;
}) {
  const { scene, camera, gl } = useThree();
  const [presetObject, setPresetObject] = useState<THREE.Object3D | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);

  // 📊 Performance monitoring
  const performanceRef = useRef<PerformanceMetrics>({
    fps: 0,
    frameTime: 0,
    memoryUsage: 0,
    drawCalls: 0,
    triangles: 0,
    points: 0
  });

  // 🎬 Load preset based on type
  useEffect(() => {
    try {
      let newPresetObject: THREE.Object3D;

      switch (preset) {
        case 'galaxy':
          newPresetObject = GalaxyPreset.create(config);
          break;
        case 'matrix':
          newPresetObject = MatrixPreset.create(config);
          break;
        case 'neonTunnel':
          newPresetObject = NeonTunnelPreset.create(config);
          break;
        case 'cyberwave':
          newPresetObject = CyberwavePreset.create(config);
          break;
        case 'glowParticles':
          newPresetObject = GlowParticlesPreset.create(config);
          break;
        default:
          newPresetObject = GalaxyPreset.create(config);
      }

      // Clean up previous preset
      if (presetObject) {
        scene.remove(presetObject);
        // Dispose of geometries and materials
        presetObject.traverse((child) => {
          if (child instanceof THREE.Mesh) {
            child.geometry?.dispose();
            if (Array.isArray(child.material)) {
              child.material.forEach(material => material.dispose());
            } else {
              child.material?.dispose();
            }
          }
        });
      }

      scene.add(newPresetObject);
      setPresetObject(newPresetObject);
      setIsLoaded(true);
      onLoad?.();
    } catch (error) {
      console.error('Failed to load preset:', error);
      onError?.(error as Error);
    }
  }, [preset, config, scene, presetObject, onLoad, onError]);

  // 🎮 Animation loop
  useFrame((state, deltaTime) => {
    if (!presetObject || !isLoaded) return;

    try {
      // Update preset animation
      switch (preset) {
        case 'galaxy':
          GalaxyPreset.update(presetObject, deltaTime);
          break;
        case 'matrix':
          MatrixPreset.update(presetObject, deltaTime);
          break;
        case 'neonTunnel':
          NeonTunnelPreset.update(presetObject, deltaTime);
          break;
        case 'cyberwave':
          CyberwavePreset.update(presetObject, deltaTime);
          break;
        case 'glowParticles':
          GlowParticlesPreset.update(presetObject, deltaTime);
          break;
        case 'hologram':
          // Hologram uses animate function instead of update
          if ((presetObject as any).animate) {
            (presetObject as any).animate(Date.now());
          }
          break;
        case 'plasma':
          // Plasma uses animate function instead of update
          if ((presetObject as any).animate) {
            (presetObject as any).animate(Date.now());
          }
          break;
      }

      // Update performance metrics
      performanceRef.current = {
        fps: Math.round(1 / deltaTime),
        frameTime: deltaTime * 1000,
        memoryUsage: (performance as any).memory?.usedJSHeapSize || 0,
        drawCalls: gl.info.render.calls,
        triangles: gl.info.render.triangles,
        points: gl.info.render.points
      };
    } catch (error) {
      console.error('Animation update error:', error);
      onError?.(error as Error);
    }
  });

  // 🧹 Cleanup on unmount
  useEffect(() => {
    return () => {
      if (presetObject) {
        scene.remove(presetObject);
        presetObject.traverse((child) => {
          if (child instanceof THREE.Mesh) {
            child.geometry?.dispose();
            if (Array.isArray(child.material)) {
              child.material.forEach(material => material.dispose());
            } else {
              child.material?.dispose();
            }
          }
        });
      }
    };
  }, [presetObject, scene]);

  return (
    <>
      {/* 🎮 Interactive Controls */}
      {interactive && (
        <OrbitControls
          enableZoom={true}
          enablePan={true}
          enableRotate={true}
          autoRotate={autoRotate}
          autoRotateSpeed={0.5}
          dampingFactor={0.05}
          enableDamping={true}
          minDistance={5}
          maxDistance={100}
        />
      )}
      
      {/* 📊 Performance Stats (Development only) */}
      {process.env.NODE_ENV === 'development' && <Stats />}
    </>
  );
}

// 🔄 Loading Fallback
function LoadingFallback() {
  return (
    <div className="k-absolute k-inset-0 k-flex k-items-center k-justify-center k-bg-black k-bg-opacity-50">
      <div className="k-text-center">
        <div className="k-animate-spin k-text-4xl k-mb-4 k-text-glow-blue">⚡</div>
        <p className="k-text-light k-animate-pulse">Loading 3D Scene...</p>
      </div>
    </div>
  );
}

// 🚨 Error Boundary for 3D Scene
function SceneErrorBoundary({ 
  children, 
  onError 
}: { 
  children: React.ReactNode; 
  onError?: (error: Error) => void; 
}) {
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const handleError = (error: ErrorEvent) => {
      setHasError(true);
      onError?.(new Error(error.message));
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, [onError]);

  if (hasError) {
    return (
      <div className="k-absolute k-inset-0 k-flex k-items-center k-justify-center k-bg-black k-text-center">
        <div>
          <div className="k-text-6xl k-mb-4 k-text-glow-red">⚠️</div>
          <h3 className="k-text-xl k-font-bold k-mb-2 k-text-glow-red">
            3D Scene Error
          </h3>
          <p className="k-text-gray-400 k-mb-4">
            Failed to load 3D animation. Your device may not support WebGL.
          </p>
          <button
            onClick={() => setHasError(false)}
            className="k-btn k-bg-neon-blue k-text-dark"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

// 🌌 Main KilatScene Component
export function KilatScene({
  preset = 'galaxy',
  interactive = true,
  autoRotate = true,
  background = '#000000',
  ambientSound = false,
  className = '',
  style = {},
  children,
  onLoad,
  onError,
  ...config
}: KilatSceneProps & KilatAnimationConfig) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isWebGLSupported, setIsWebGLSupported] = useState(true);

  // 🔍 Check WebGL support
  useEffect(() => {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    if (!gl) {
      setIsWebGLSupported(false);
      onError?.(new Error('WebGL is not supported on this device'));
    }
  }, [onError]);

  // 🎵 Audio handling (if enabled)
  useEffect(() => {
    if (ambientSound) {
      // TODO: Implement ambient sound loading and playback
      console.log('Ambient sound requested for preset:', preset);
    }
  }, [ambientSound, preset]);

  if (!isWebGLSupported) {
    return (
      <div 
        className={`k-relative k-w-full k-h-full k-bg-black k-flex k-items-center k-justify-center ${className}`}
        style={style}
      >
        <div className="k-text-center">
          <div className="k-text-6xl k-mb-4">🚫</div>
          <h3 className="k-text-xl k-font-bold k-mb-2 k-text-glow-red">
            WebGL Not Supported
          </h3>
          <p className="k-text-gray-400">
            Your browser or device doesn't support 3D graphics.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`k-relative k-w-full k-h-full ${className}`}
      style={style}
    >
      <SceneErrorBoundary onError={onError}>
        <Canvas
          ref={canvasRef}
          camera={{
            position: config.camera?.position || [0, 0, 10],
            fov: config.camera?.fov || 75,
            near: config.camera?.near || 0.1,
            far: config.camera?.far || 1000
          }}
          gl={{
            antialias: true,
            alpha: true,
            powerPreference: 'high-performance'
          }}
          style={{ background }}
          onCreated={({ gl }) => {
            gl.setClearColor(background);
            gl.setPixelRatio(Math.min(window.devicePixelRatio, 2));
          }}
        >
          <Suspense fallback={null}>
            {/* 💡 Lighting */}
            <ambientLight 
              color={config.lights?.ambient?.color || '#404040'} 
              intensity={config.lights?.ambient?.intensity || 0.4} 
            />
            <directionalLight
              color={config.lights?.directional?.color || '#ffffff'}
              intensity={config.lights?.directional?.intensity || 1}
              position={config.lights?.directional?.position || [10, 10, 5]}
            />

            {/* 🎬 Scene Content */}
            <SceneContent
              preset={preset}
              config={config}
              interactive={interactive}
              autoRotate={autoRotate}
              onLoad={onLoad}
              onError={onError}
            />
          </Suspense>
        </Canvas>

        {/* 🔄 Loading Overlay */}
        <Suspense fallback={<LoadingFallback />}>
          {children}
        </Suspense>
      </SceneErrorBoundary>
    </div>
  );
}
