{"name": "@kilat/mobile-demo", "version": "1.0.0", "description": "📱 Kilat.js Mobile Demo - React Native + Expo showcase", "main": "expo/AppEntry.js", "scripts": {"start": "expo start", "dev": "expo start --dev-client", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "eas build", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "submit": "eas submit", "test": "jest", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"expo": "~49.0.0", "expo-status-bar": "~1.6.0", "expo-splash-screen": "~0.20.5", "expo-font": "~11.4.0", "expo-linear-gradient": "~12.3.0", "expo-blur": "~12.4.1", "expo-haptics": "~12.4.0", "expo-device": "~5.4.0", "expo-constants": "~14.4.2", "expo-router": "^2.0.0", "react": "18.2.0", "react-native": "0.72.6", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "react-native-svg": "13.9.0", "react-native-vector-icons": "^10.0.0", "@react-native-async-storage/async-storage": "1.18.2", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "@react-navigation/bottom-tabs": "^6.5.8", "kilat-core": "workspace:*", "kilat-utils": "workspace:*", "kilat-plugins": "workspace:*"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-expo": "^7.0.0", "jest": "^29.2.1", "jest-expo": "~49.0.0", "typescript": "^5.1.3"}, "keywords": ["kilat", "kilatjs", "mobile", "react-native", "expo", "cyberpunk", "nusantara", "demo"], "author": "Kilat.js Team", "license": "MIT", "private": true}