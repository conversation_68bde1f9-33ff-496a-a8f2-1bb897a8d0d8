import { app, BrowserWindow, Menu, shell, ipcMain, dialog, nativeTheme } from 'electron';
import { join } from 'path';
import { createLogger } from 'kilat-utils';

const logger = createLogger({ prefix: 'ElectronMain' });

/**
 * 🖥️ Kilat.js Desktop - Main Process
 * Electron main process for desktop application
 */

// Keep a global reference of the window object
let mainWindow: BrowserWindow | null = null;

// App configuration
const isDev = process.env.NODE_ENV === 'development';
const isWin = process.platform === 'win32';
const isMac = process.platform === 'darwin';

/**
 * 🪟 Create Main Window
 */
function createMainWindow(): BrowserWindow {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    show: false, // Don't show until ready
    icon: join(__dirname, '../assets/icon.png'),
    titleBarStyle: isMac ? 'hiddenInset' : 'default',
    titleBarOverlay: isWin ? {
      color: '#000011',
      symbolColor: '#00ffff',
      height: 30
    } : undefined,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: join(__dirname, 'preload.js'),
      webSecurity: !isDev,
    },
    backgroundColor: '#000011', // Cyberpunk theme background
  });

  // Load the app
  if (isDev) {
    mainWindow.loadURL('http://localhost:5173');
    // Open DevTools in development
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'));
  }

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    if (mainWindow) {
      mainWindow.show();
      
      // Focus on window
      if (isDev) {
        mainWindow.focus();
      }
      
      logger.info('Main window created and shown');
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // Prevent navigation to external URLs
  mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);
    
    if (parsedUrl.origin !== 'http://localhost:5173' && !isDev) {
      event.preventDefault();
    }
  });

  return mainWindow;
}

/**
 * 📋 Create Application Menu
 */
function createMenu(): void {
  const template: Electron.MenuItemConstructorOptions[] = [
    // macOS app menu
    ...(isMac ? [{
      label: app.getName(),
      submenu: [
        { role: 'about' as const },
        { type: 'separator' as const },
        { role: 'services' as const },
        { type: 'separator' as const },
        { role: 'hide' as const },
        { role: 'hideOthers' as const },
        { role: 'unhide' as const },
        { type: 'separator' as const },
        { role: 'quit' as const }
      ]
    }] : []),
    
    // File menu
    {
      label: 'File',
      submenu: [
        {
          label: 'New Project',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            // Handle new project
            mainWindow?.webContents.send('menu-new-project');
          }
        },
        {
          label: 'Open Project',
          accelerator: 'CmdOrCtrl+O',
          click: async () => {
            if (mainWindow) {
              const result = await dialog.showOpenDialog(mainWindow, {
                properties: ['openDirectory'],
                title: 'Open Kilat.js Project'
              });
              
              if (!result.canceled && result.filePaths.length > 0) {
                mainWindow.webContents.send('menu-open-project', result.filePaths[0]);
              }
            }
          }
        },
        { type: 'separator' },
        isMac ? { role: 'close' } : { role: 'quit' }
      ]
    },
    
    // Edit menu
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        ...(isMac ? [
          { role: 'pasteAndMatchStyle' as const },
          { role: 'delete' as const },
          { role: 'selectAll' as const },
          { type: 'separator' as const },
          {
            label: 'Speech',
            submenu: [
              { role: 'startSpeaking' as const },
              { role: 'stopSpeaking' as const }
            ]
          }
        ] : [
          { role: 'delete' as const },
          { type: 'separator' as const },
          { role: 'selectAll' as const }
        ])
      ]
    },
    
    // View menu
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' },
        { type: 'separator' },
        {
          label: 'Toggle Theme',
          accelerator: 'CmdOrCtrl+T',
          click: () => {
            mainWindow?.webContents.send('menu-toggle-theme');
          }
        }
      ]
    },
    
    // Window menu
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'close' },
        ...(isMac ? [
          { type: 'separator' as const },
          { role: 'front' as const },
          { type: 'separator' as const },
          { role: 'window' as const }
        ] : [])
      ]
    },
    
    // Help menu
    {
      role: 'help',
      submenu: [
        {
          label: 'About Kilat.js',
          click: () => {
            mainWindow?.webContents.send('menu-about');
          }
        },
        {
          label: 'Documentation',
          click: () => {
            shell.openExternal('https://docs.kilat-js.pcode.my.id');
          }
        },
        {
          label: 'GitHub Repository',
          click: () => {
            shell.openExternal('https://github.com/kangpcode/kilat.js');
          }
        },
        { type: 'separator' },
        {
          label: 'Report Issue',
          click: () => {
            shell.openExternal('https://github.com/kangpcode/kilat.js/issues');
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

/**
 * 🔧 Setup IPC Handlers
 */
function setupIpcHandlers(): void {
  // Get app version
  ipcMain.handle('app:get-version', () => {
    return app.getVersion();
  });

  // Get platform info
  ipcMain.handle('app:get-platform', () => {
    return {
      platform: process.platform,
      arch: process.arch,
      version: process.version,
      electronVersion: process.versions.electron,
    };
  });

  // Theme handling
  ipcMain.handle('theme:get-native-theme', () => {
    return nativeTheme.shouldUseDarkColors;
  });

  ipcMain.on('theme:set-native-theme', (_, theme: 'system' | 'light' | 'dark') => {
    nativeTheme.themeSource = theme;
  });

  // Window controls
  ipcMain.on('window:minimize', () => {
    mainWindow?.minimize();
  });

  ipcMain.on('window:maximize', () => {
    if (mainWindow?.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow?.maximize();
    }
  });

  ipcMain.on('window:close', () => {
    mainWindow?.close();
  });

  // Show message box
  ipcMain.handle('dialog:show-message', async (_, options) => {
    if (mainWindow) {
      return await dialog.showMessageBox(mainWindow, options);
    }
  });

  // Show save dialog
  ipcMain.handle('dialog:show-save', async (_, options) => {
    if (mainWindow) {
      return await dialog.showSaveDialog(mainWindow, options);
    }
  });

  logger.info('IPC handlers setup complete');
}

/**
 * 🚀 App Event Handlers
 */

// This method will be called when Electron has finished initialization
app.whenReady().then(() => {
  logger.info('App ready, creating main window...');
  
  createMainWindow();
  createMenu();
  setupIpcHandlers();

  // macOS: Re-create window when dock icon is clicked
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow();
    }
  });
});

// Quit when all windows are closed
app.on('window-all-closed', () => {
  // On macOS, keep app running even when all windows are closed
  if (!isMac) {
    app.quit();
  }
});

// Security: Prevent new window creation
app.on('web-contents-created', (_, contents) => {
  contents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });
});

// Handle certificate errors
app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
  if (isDev) {
    // In development, ignore certificate errors
    event.preventDefault();
    callback(true);
  } else {
    // In production, use default behavior
    callback(false);
  }
});

// Handle app updates (placeholder)
app.on('ready', () => {
  if (!isDev) {
    // Auto-updater logic would go here
    logger.info('Auto-updater ready (placeholder)');
  }
});

// Prevent navigation to external URLs
app.on('web-contents-created', (_, contents) => {
  contents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);
    
    if (parsedUrl.origin !== 'http://localhost:5173' && isDev) {
      event.preventDefault();
    }
  });
});

// Log app events
app.on('before-quit', () => {
  logger.info('App is about to quit');
});

app.on('will-quit', () => {
  logger.info('App will quit');
});

// Export for testing
export { createMainWindow, createMenu };
