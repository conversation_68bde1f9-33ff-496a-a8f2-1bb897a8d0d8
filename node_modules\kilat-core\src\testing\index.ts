// 🧪 Kilat.js Testing Framework
// Comprehensive testing utilities for Kilat.js applications

// Core Testing
export { render } from './render';
export { screen } from './screen';
export { fireEvent } from './fireEvent';
export { waitFor } from './waitFor';
export { cleanup } from './cleanup';
export { act } from './act';

// Test Utilities
export { createMockRouter } from './mocks/createMockRouter';
export { createMockAuth } from './mocks/createMockAuth';
export { createMockTheme } from './mocks/createMockTheme';
export { createMockPlatform } from './mocks/createMockPlatform';
export { createMockAPI } from './mocks/createMockAPI';
export { createMockDatabase } from './mocks/createMockDatabase';

// Custom Matchers
export { toBeInTheDocument } from './matchers/toBeInTheDocument';
export { toHaveClass } from './matchers/toHaveClass';
export { toHaveStyle } from './matchers/toHaveStyle';
export { toBeVisible } from './matchers/toBeVisible';
export { toHaveAttribute } from './matchers/toHaveAttribute';

// Test Providers
export { TestKilatProvider } from './providers/TestKilatProvider';
export { TestThemeProvider } from './providers/TestThemeProvider';
export { TestRouterProvider } from './providers/TestRouterProvider';
export { TestAuthProvider } from './providers/TestAuthProvider';

// Test Helpers
export { renderWithProviders } from './helpers/renderWithProviders';
export { createTestUser } from './helpers/createTestUser';
export { mockLocalStorage } from './helpers/mockLocalStorage';
export { mockSessionStorage } from './helpers/mockSessionStorage';
export { mockFetch } from './helpers/mockFetch';
export { mockWebSocket } from './helpers/mockWebSocket';

// E2E Testing
export { createE2ETest } from './e2e/createE2ETest';
export { PageObject } from './e2e/PageObject';
export { TestRunner } from './e2e/TestRunner';

// Performance Testing
export { measurePerformance } from './performance/measurePerformance';
export { ProfilerWrapper } from './performance/ProfilerWrapper';
export { MemoryTracker } from './performance/MemoryTracker';

// Visual Testing
export { takeScreenshot } from './visual/takeScreenshot';
export { compareScreenshots } from './visual/compareScreenshots';
export { VisualRegression } from './visual/VisualRegression';

// API Testing
export { createAPITest } from './api/createAPITest';
export { MockServer } from './api/MockServer';
export { RequestMatcher } from './api/RequestMatcher';

// Database Testing
export { createTestDatabase } from './database/createTestDatabase';
export { seedTestData } from './database/seedTestData';
export { cleanDatabase } from './database/cleanDatabase';

// Types
export type { 
  RenderOptions,
  RenderResult,
  TestConfig,
  MockOptions,
  E2ETestOptions,
  PerformanceMetrics,
  VisualTestOptions,
  APITestOptions,
  DatabaseTestOptions
} from './types';
