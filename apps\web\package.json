{"name": "@kilat/web-demo", "version": "1.0.0", "description": "🌐 Kilat.js Web Demo Application - Showcase of framework capabilities", "type": "module", "scripts": {"dev": "kilat dev", "build": "kilat build", "preview": "kilat preview", "test": "kilat test", "test:e2e": "kilat test --e2e", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "doctor": "kilat doctor"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "kilat-core": "workspace:*", "kilat-router": "workspace:*", "kilat-utils": "workspace:*", "kilatcss": "workspace:*", "kilatanim.js": "workspace:*", "kilat-plugins": "workspace:*", "three": "^0.158.0", "@react-three/fiber": "^8.15.0", "@react-three/drei": "^9.88.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/three": "^0.158.0", "typescript": "^5.0.0", "vite": "^5.0.0", "vitest": "^0.34.0", "eslint": "^8.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0"}, "keywords": ["kilat", "kilatjs", "react", "vite", "typescript", "demo", "showcase"], "author": "Kilat.js Team", "license": "MIT"}