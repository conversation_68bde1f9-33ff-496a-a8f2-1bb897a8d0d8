/**
 * ⚡ Kilat.js Performance Monitoring
 * Performance tracking and optimization utilities
 */

/**
 * 📊 Performance Metrics
 */
export interface PerformanceMetrics {
  renderTime: number;
  bundleSize: number;
  memoryUsage: number;
  networkRequests: number;
  cacheHitRate: number;
  errorRate: number;
  userInteractions: number;
  pageLoadTime: number;
  timeToInteractive: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  cumulativeLayoutShift: number;
}

/**
 * 🎯 Performance Config
 */
export interface PerformanceConfig {
  enabled: boolean;
  sampling: number;
  reportInterval: number;
  thresholds: {
    renderTime: number;
    memoryUsage: number;
    bundleSize: number;
  };
  reporting: {
    console: boolean;
    endpoint?: string;
    localStorage: boolean;
  };
}

/**
 * ⚡ Performance Monitor
 */
export class KilatPerformanceMonitor {
  private config: PerformanceConfig;
  private metrics: Partial<PerformanceMetrics> = {};
  private observers: PerformanceObserver[] = [];
  private startTime: number = Date.now();

  constructor(config: Partial<PerformanceConfig> = {}) {
    this.config = {
      enabled: true,
      sampling: 1.0,
      reportInterval: 30000, // 30 seconds
      thresholds: {
        renderTime: 100,
        memoryUsage: 50 * 1024 * 1024, // 50MB
        bundleSize: 1024 * 1024 // 1MB
      },
      reporting: {
        console: true,
        localStorage: true
      },
      ...config
    };

    if (this.config.enabled) {
      this.initialize();
    }
  }

  /**
   * 🚀 Initialize performance monitoring
   */
  private initialize(): void {
    if (typeof window === 'undefined') return;

    this.setupPerformanceObservers();
    this.startPeriodicReporting();
    this.trackPageLoad();
  }

  /**
   * 👀 Setup performance observers
   */
  private setupPerformanceObservers(): void {
    if (!('PerformanceObserver' in window)) return;

    // Track paint metrics
    try {
      const paintObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.name === 'first-contentful-paint') {
            this.metrics.firstContentfulPaint = entry.startTime;
          }
        }
      });
      paintObserver.observe({ entryTypes: ['paint'] });
      this.observers.push(paintObserver);
    } catch (e) {
      console.warn('Paint observer not supported');
    }

    // Track largest contentful paint
    try {
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.metrics.largestContentfulPaint = lastEntry.startTime;
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.push(lcpObserver);
    } catch (e) {
      console.warn('LCP observer not supported');
    }

    // Track layout shifts
    try {
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0;
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
          }
        }
        this.metrics.cumulativeLayoutShift = clsValue;
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
      this.observers.push(clsObserver);
    } catch (e) {
      console.warn('CLS observer not supported');
    }
  }

  /**
   * 📄 Track page load performance
   */
  private trackPageLoad(): void {
    if (typeof window === 'undefined') return;

    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        if (navigation) {
          this.metrics.pageLoadTime = navigation.loadEventEnd - navigation.fetchStart;
          this.metrics.timeToInteractive = navigation.domInteractive - navigation.fetchStart;
        }
      }, 0);
    });
  }

  /**
   * 🔄 Start periodic reporting
   */
  private startPeriodicReporting(): void {
    setInterval(() => {
      this.collectMetrics();
      this.reportMetrics();
    }, this.config.reportInterval);
  }

  /**
   * 📊 Collect current metrics
   */
  private collectMetrics(): void {
    if (typeof window === 'undefined') return;

    // Memory usage
    if ('memory' in performance) {
      this.metrics.memoryUsage = (performance as any).memory.usedJSHeapSize;
    }

    // Network requests
    const resourceEntries = performance.getEntriesByType('resource');
    this.metrics.networkRequests = resourceEntries.length;

    // Bundle size estimation
    const scriptEntries = resourceEntries.filter(entry => 
      entry.name.includes('.js') || entry.name.includes('.css')
    );
    this.metrics.bundleSize = scriptEntries.reduce((total, entry) => 
      total + (entry.transferSize || 0), 0
    );
  }

  /**
   * 📈 Report metrics
   */
  private reportMetrics(): void {
    const report = {
      timestamp: Date.now(),
      sessionDuration: Date.now() - this.startTime,
      metrics: this.metrics,
      thresholds: this.config.thresholds,
      violations: this.checkThresholds()
    };

    if (this.config.reporting.console) {
      console.group('⚡ Kilat.js Performance Report');
      console.table(this.metrics);
      if (report.violations.length > 0) {
        console.warn('⚠️ Performance violations:', report.violations);
      }
      console.groupEnd();
    }

    if (this.config.reporting.localStorage) {
      try {
        localStorage.setItem('kilat-performance', JSON.stringify(report));
      } catch (e) {
        console.warn('Failed to save performance data to localStorage');
      }
    }

    if (this.config.reporting.endpoint) {
      this.sendToEndpoint(report);
    }
  }

  /**
   * 🚨 Check performance thresholds
   */
  private checkThresholds(): string[] {
    const violations: string[] = [];

    if (this.metrics.renderTime && this.metrics.renderTime > this.config.thresholds.renderTime) {
      violations.push(`Render time: ${this.metrics.renderTime}ms > ${this.config.thresholds.renderTime}ms`);
    }

    if (this.metrics.memoryUsage && this.metrics.memoryUsage > this.config.thresholds.memoryUsage) {
      violations.push(`Memory usage: ${Math.round(this.metrics.memoryUsage / 1024 / 1024)}MB > ${Math.round(this.config.thresholds.memoryUsage / 1024 / 1024)}MB`);
    }

    if (this.metrics.bundleSize && this.metrics.bundleSize > this.config.thresholds.bundleSize) {
      violations.push(`Bundle size: ${Math.round(this.metrics.bundleSize / 1024)}KB > ${Math.round(this.config.thresholds.bundleSize / 1024)}KB`);
    }

    return violations;
  }

  /**
   * 📡 Send metrics to endpoint
   */
  private async sendToEndpoint(report: any): Promise<void> {
    if (!this.config.reporting.endpoint) return;

    try {
      await fetch(this.config.reporting.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(report)
      });
    } catch (e) {
      console.warn('Failed to send performance data to endpoint');
    }
  }

  /**
   * ⏱️ Measure function performance
   */
  measureFunction<T>(fn: () => T, label?: string): T {
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    
    const duration = end - start;
    this.metrics.renderTime = duration;

    if (label && this.config.reporting.console) {
      console.log(`⚡ ${label}: ${duration.toFixed(2)}ms`);
    }

    return result;
  }

  /**
   * ⏱️ Measure async function performance
   */
  async measureAsyncFunction<T>(fn: () => Promise<T>, label?: string): Promise<T> {
    const start = performance.now();
    const result = await fn();
    const end = performance.now();
    
    const duration = end - start;
    this.metrics.renderTime = duration;

    if (label && this.config.reporting.console) {
      console.log(`⚡ ${label}: ${duration.toFixed(2)}ms`);
    }

    return result;
  }

  /**
   * 📊 Get current metrics
   */
  getMetrics(): Partial<PerformanceMetrics> {
    this.collectMetrics();
    return { ...this.metrics };
  }

  /**
   * 🧹 Cleanup
   */
  destroy(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

/**
 * 🎯 Default performance monitor instance
 */
export const performanceMonitor = new KilatPerformanceMonitor();

/**
 * 🚀 Quick performance measurement
 */
export function measurePerformance<T>(fn: () => T, label?: string): T {
  return performanceMonitor.measureFunction(fn, label);
}

export async function measureAsyncPerformance<T>(fn: () => Promise<T>, label?: string): Promise<T> {
  return performanceMonitor.measureAsyncFunction(fn, label);
}

/**
 * 📊 Get performance metrics
 */
export function getPerformanceMetrics(): Partial<PerformanceMetrics> {
  return performanceMonitor.getMetrics();
}
