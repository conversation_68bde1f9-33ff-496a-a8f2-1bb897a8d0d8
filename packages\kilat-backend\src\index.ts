// 🚀 Kilat Backend - Internal API Server
// Export main server class
export { Ki<PERSON>Backend } from './server';

// Export all types
export * from './types';

// Export middleware
export * from './middleware/auth';
export * from './middleware/validation';
export * from './middleware/error';

// Export utilities
export * from './utils/logger';

// Export convenience functions
import { KilatBackend } from './server';
import type { KilatBackendConfig } from './types';

/**
 * Create a new Kilat Backend server instance
 */
export function createKilatBackend(config?: Partial<KilatBackendConfig>): KilatBackend {
  return new KilatBackend(config);
}

/**
 * Quick start function for simple setups
 */
export async function startKilatBackend(config?: Partial<KilatBackendConfig>): Promise<KilatBackend> {
  const server = new KilatBackend(config);
  await server.start();
  return server;
}

// Default export
export default {
  KilatBackend,
  createK<PERSON>tBackend,
  startKilatBackend
};

// Version info
export const KILAT_BACKEND_VERSION = '1.0.0';
