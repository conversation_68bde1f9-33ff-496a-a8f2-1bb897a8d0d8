{"version": 3, "file": "preload.js", "sourceRoot": "", "sources": ["../src/main/preload.ts"], "names": [], "mappings": ";;AAAA,uCAAsD;AA4CtD,kEAAkE;AAClE,qDAAqD;AACrD,MAAM,WAAW,GAAgB;IAC/B,WAAW;IACX,UAAU,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,iBAAiB,CAAC;IACvD,WAAW,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,kBAAkB,CAAC;IAEzD,mBAAmB;IACnB,cAAc,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,wBAAwB,CAAC;IAClE,cAAc,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,sBAAW,CAAC,IAAI,CAAC,wBAAwB,EAAE,KAAK,CAAC;IAE5E,kBAAkB;IAClB,cAAc,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC;IACzD,cAAc,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC;IACzD,WAAW,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,IAAI,CAAC,cAAc,CAAC;IAEnD,cAAc;IACd,WAAW,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,qBAAqB,EAAE,OAAO,CAAC;IAC5E,cAAc,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,kBAAkB,EAAE,OAAO,CAAC;IAE5E,uBAAuB;IACvB,WAAW,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;QAC/B,MAAM,OAAO,GAAG,QAAQ,KAAK,EAAE,CAAC;QAChC,sBAAW,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IACpC,CAAC;IACD,kBAAkB,EAAE,CAAC,KAAK,EAAE,EAAE;QAC5B,MAAM,OAAO,GAAG,QAAQ,KAAK,EAAE,CAAC;QAChC,sBAAW,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IAC1C,CAAC;IAED,qCAAqC;IACrC,QAAQ,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,cAAc,EAAE,IAAI,CAAC;IAC5D,SAAS,EAAE,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,eAAe,EAAE,IAAI,EAAE,OAAO,CAAC;IAEhF,qBAAqB;IACrB,aAAa,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,gBAAgB,EAAE,IAAI,EAAE,IAAI,CAAC;IAC/E,WAAW,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,cAAc,EAAE,IAAI,CAAC;CAChE,CAAC;AAEF,yCAAyC;AACzC,wBAAa,CAAC,iBAAiB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;AAE5D,wDAAwD;AACxD,wBAAa,CAAC,iBAAiB,CAAC,SAAS,EAAE;IACzC,QAAQ,EAAE,OAAO,CAAC,QAAQ;IAC1B,IAAI,EAAE,OAAO,CAAC,IAAI;IAClB,QAAQ,EAAE,OAAO,CAAC,QAAQ;CAC3B,CAAC,CAAC;AAEH,sBAAsB;AACtB,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;IAC3C,wBAAa,CAAC,iBAAiB,CAAC,QAAQ,EAAE;QACxC,YAAY,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,IAAI,CAAC,mBAAmB,CAAC;QACzD,MAAM,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,IAAI,CAAC,YAAY,CAAC;KAC7C,CAAC,CAAC;AACL,CAAC;AAED,sDAAsD;AACtD,OAAQ,MAAc,CAAC,OAAO,CAAC;AAC/B,OAAQ,MAAc,CAAC,OAAO,CAAC;AAC/B,OAAQ,MAAc,CAAC,MAAM,CAAC;AAE9B,yBAAyB;AACzB,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC"}