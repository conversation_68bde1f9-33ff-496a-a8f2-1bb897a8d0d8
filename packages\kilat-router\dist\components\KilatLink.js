import React from 'react';
import { Link } from 'react-router-dom';

/**
 * 🔗 KilatLink Component
 * Enhanced Link component with Kilat.js features
 */
export function KilatLink({ 
  to,
  children,
  prefetch = false, 
  external = false, 
  variant = 'default',
  className = '',
  ...props 
}) {
  const baseClasses = {
    default: 'k-link',
    button: 'k-btn k-btn-primary',
    nav: 'k-nav-link'
  };

  const classes = `${baseClasses[variant]} ${className}`.trim();

  if (external) {
    return React.createElement('a', {
      href: to,
      className: classes,
      target: '_blank',
      rel: 'noopener noreferrer',
      ...props
    }, children);
  }

  return React.createElement(Link, {
    to,
    className: classes,
    ...props
  }, children);
}

export default KilatLink;
