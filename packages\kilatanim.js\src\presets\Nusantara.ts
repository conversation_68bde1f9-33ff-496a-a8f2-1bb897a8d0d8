import * as THREE from 'three';
import type { PresetFactory, NusantaraConfig } from '../types';

/**
 * Nusantara Preset - Indonesian archipelago with floating islands and batik patterns
 * Creates a mystical Indonesian landscape with traditional elements
 */
export const NusantaraPreset: PresetFactory = {
  create: (config: NusantaraConfig = {}) => {
    const {
      islandCount = 8,
      batikPatterns = true,
      floatingHeight = 5,
      colors = {
        earth: '#8B4513',
        water: '#1E90FF',
        gold: '#FFD700',
        emerald: '#50C878',
        batik: '#B8860B'
      },
      animationSpeed = 1,
      mysticalParticles = true,
      traditionalElements = true
    } = config;

    const group = new THREE.Group();
    group.name = 'nusantara';

    // 🏝️ Create floating islands
    const islands: THREE.Group[] = [];
    
    for (let i = 0; i < islandCount; i++) {
      const islandGroup = new THREE.Group();
      
      // Island base geometry
      const islandGeometry = new THREE.CylinderGeometry(
        Math.random() * 2 + 1, // top radius
        Math.random() * 1.5 + 0.5, // bottom radius
        Math.random() * 1 + 0.5, // height
        8 // segments
      );
      
      const islandMaterial = new THREE.MeshLambertMaterial({
        color: colors.earth,
        transparent: true,
        opacity: 0.9
      });
      
      const island = new THREE.Mesh(islandGeometry, islandMaterial);
      
      // Position islands in a circle
      const angle = (i / islandCount) * Math.PI * 2;
      const radius = 8 + Math.random() * 4;
      const x = Math.cos(angle) * radius;
      const z = Math.sin(angle) * radius;
      const y = Math.random() * floatingHeight;
      
      island.position.set(x, y, z);
      island.rotation.y = Math.random() * Math.PI * 2;
      
      islandGroup.add(island);

      // 🌴 Add vegetation
      if (traditionalElements) {
        const treeCount = Math.floor(Math.random() * 3) + 1;
        for (let t = 0; t < treeCount; t++) {
          const tree = createPalmTree(colors);
          tree.position.set(
            (Math.random() - 0.5) * 2,
            0.5,
            (Math.random() - 0.5) * 2
          );
          tree.scale.setScalar(0.3 + Math.random() * 0.3);
          islandGroup.add(tree);
        }
      }

      // 🏛️ Add traditional structures
      if (traditionalElements && Math.random() > 0.5) {
        const structure = createTraditionalStructure(colors);
        structure.position.set(0, 0.5, 0);
        structure.scale.setScalar(0.4 + Math.random() * 0.2);
        islandGroup.add(structure);
      }

      islands.push(islandGroup);
      group.add(islandGroup);
    }

    // 🌊 Create water surface with waves
    const waterGeometry = new THREE.PlaneGeometry(50, 50, 32, 32);
    const waterMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        color: { value: new THREE.Color(colors.water) },
        opacity: { value: 0.7 }
      },
      vertexShader: `
        uniform float time;
        varying vec2 vUv;
        varying float vElevation;
        
        void main() {
          vUv = uv;
          
          vec4 modelPosition = modelMatrix * vec4(position, 1.0);
          
          // Create wave effect
          float elevation = sin(modelPosition.x * 0.3 + time) * 0.1;
          elevation += sin(modelPosition.z * 0.2 + time * 1.5) * 0.1;
          elevation += sin(modelPosition.x * 0.1 + modelPosition.z * 0.1 + time * 0.5) * 0.2;
          
          modelPosition.y += elevation;
          vElevation = elevation;
          
          vec4 viewPosition = viewMatrix * modelPosition;
          vec4 projectedPosition = projectionMatrix * viewPosition;
          
          gl_Position = projectedPosition;
        }
      `,
      fragmentShader: `
        uniform vec3 color;
        uniform float opacity;
        varying vec2 vUv;
        varying float vElevation;
        
        void main() {
          vec3 finalColor = color;
          
          // Add foam on wave peaks
          if (vElevation > 0.1) {
            finalColor = mix(finalColor, vec3(1.0), 0.3);
          }
          
          gl_FragColor = vec4(finalColor, opacity);
        }
      `,
      transparent: true,
      side: THREE.DoubleSide
    });

    const water = new THREE.Mesh(waterGeometry, waterMaterial);
    water.rotation.x = -Math.PI / 2;
    water.position.y = -2;
    group.add(water);

    // ✨ Create mystical particles
    if (mysticalParticles) {
      const particleCount = 200;
      const particleGeometry = new THREE.BufferGeometry();
      const particlePositions = new Float32Array(particleCount * 3);
      const particleColors = new Float32Array(particleCount * 3);
      const particleSizes = new Float32Array(particleCount);

      for (let i = 0; i < particleCount; i++) {
        const i3 = i * 3;
        
        // Random positions around the scene
        particlePositions[i3] = (Math.random() - 0.5) * 40;
        particlePositions[i3 + 1] = Math.random() * 15;
        particlePositions[i3 + 2] = (Math.random() - 0.5) * 40;
        
        // Golden mystical colors
        const goldColor = new THREE.Color(colors.gold);
        const emeraldColor = new THREE.Color(colors.emerald);
        const mixedColor = goldColor.lerp(emeraldColor, Math.random());
        
        particleColors[i3] = mixedColor.r;
        particleColors[i3 + 1] = mixedColor.g;
        particleColors[i3 + 2] = mixedColor.b;
        
        particleSizes[i] = Math.random() * 0.1 + 0.05;
      }

      particleGeometry.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));
      particleGeometry.setAttribute('color', new THREE.BufferAttribute(particleColors, 3));
      particleGeometry.setAttribute('size', new THREE.BufferAttribute(particleSizes, 1));

      const particleMaterial = new THREE.ShaderMaterial({
        uniforms: {
          time: { value: 0 }
        },
        vertexShader: `
          attribute float size;
          varying vec3 vColor;
          uniform float time;
          
          void main() {
            vColor = color;
            
            vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
            
            // Floating animation
            mvPosition.y += sin(time + position.x * 0.1) * 0.5;
            mvPosition.x += cos(time * 0.5 + position.z * 0.1) * 0.2;
            
            gl_PointSize = size * (300.0 / -mvPosition.z);
            gl_Position = projectionMatrix * mvPosition;
          }
        `,
        fragmentShader: `
          varying vec3 vColor;
          
          void main() {
            float distanceToCenter = distance(gl_PointCoord, vec2(0.5));
            float alpha = 1.0 - smoothstep(0.0, 0.5, distanceToCenter);
            
            gl_FragColor = vec4(vColor, alpha * 0.8);
          }
        `,
        transparent: true,
        vertexColors: true,
        blending: THREE.AdditiveBlending
      });

      const particles = new THREE.Points(particleGeometry, particleMaterial);
      group.add(particles);
    }

    // 🎨 Add batik pattern overlay
    if (batikPatterns) {
      const batikOverlay = createBatikOverlay(colors);
      group.add(batikOverlay);
    }

    // Store references for animation
    (group as any).islands = islands;
    (group as any).water = water;
    (group as any).particles = group.children.find(child => child instanceof THREE.Points);
    (group as any).animationSpeed = animationSpeed;

    return group;
  },

  update: (group: THREE.Object3D, deltaTime: number) => {
    const time = Date.now() * 0.001;
    const islands = (group as any).islands;
    const water = (group as any).water;
    const particles = (group as any).particles;
    const speed = (group as any).animationSpeed || 1;

    // Animate floating islands
    if (islands) {
      islands.forEach((island: THREE.Group, index: number) => {
        island.position.y += Math.sin(time * speed + index) * 0.01;
        island.rotation.y += deltaTime * 0.1 * speed;
      });
    }

    // Animate water
    if (water && water.material.uniforms) {
      water.material.uniforms.time.value = time * speed;
    }

    // Animate particles
    if (particles && particles.material.uniforms) {
      particles.material.uniforms.time.value = time * speed;
      particles.rotation.y += deltaTime * 0.05 * speed;
    }
  }
};

// 🌴 Helper function to create palm tree
function createPalmTree(colors: any): THREE.Group {
  const tree = new THREE.Group();
  
  // Trunk
  const trunkGeometry = new THREE.CylinderGeometry(0.1, 0.15, 2, 8);
  const trunkMaterial = new THREE.MeshLambertMaterial({ color: colors.earth });
  const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
  trunk.position.y = 1;
  tree.add(trunk);
  
  // Leaves
  const leafCount = 6;
  for (let i = 0; i < leafCount; i++) {
    const leafGeometry = new THREE.PlaneGeometry(0.8, 0.2);
    const leafMaterial = new THREE.MeshLambertMaterial({ 
      color: colors.emerald,
      side: THREE.DoubleSide 
    });
    const leaf = new THREE.Mesh(leafGeometry, leafMaterial);
    
    const angle = (i / leafCount) * Math.PI * 2;
    leaf.position.set(
      Math.cos(angle) * 0.4,
      2,
      Math.sin(angle) * 0.4
    );
    leaf.rotation.z = angle;
    leaf.rotation.x = -0.3;
    
    tree.add(leaf);
  }
  
  return tree;
}

// 🏛️ Helper function to create traditional structure
function createTraditionalStructure(colors: any): THREE.Group {
  const structure = new THREE.Group();
  
  // Base platform
  const baseGeometry = new THREE.CylinderGeometry(1, 1, 0.2, 8);
  const baseMaterial = new THREE.MeshLambertMaterial({ color: colors.batik });
  const base = new THREE.Mesh(baseGeometry, baseMaterial);
  structure.add(base);
  
  // Pillars
  for (let i = 0; i < 4; i++) {
    const pillarGeometry = new THREE.CylinderGeometry(0.05, 0.05, 1, 8);
    const pillarMaterial = new THREE.MeshLambertMaterial({ color: colors.gold });
    const pillar = new THREE.Mesh(pillarGeometry, pillarMaterial);
    
    const angle = (i / 4) * Math.PI * 2;
    pillar.position.set(
      Math.cos(angle) * 0.6,
      0.6,
      Math.sin(angle) * 0.6
    );
    
    structure.add(pillar);
  }
  
  // Roof
  const roofGeometry = new THREE.ConeGeometry(1.2, 0.8, 8);
  const roofMaterial = new THREE.MeshLambertMaterial({ color: colors.earth });
  const roof = new THREE.Mesh(roofGeometry, roofMaterial);
  roof.position.y = 1.4;
  structure.add(roof);
  
  return structure;
}

// 🎨 Helper function to create batik overlay
function createBatikOverlay(colors: any): THREE.Group {
  const overlay = new THREE.Group();
  
  // Create subtle batik pattern in the background
  const patternCount = 20;
  for (let i = 0; i < patternCount; i++) {
    const patternGeometry = new THREE.RingGeometry(0.5, 1, 8);
    const patternMaterial = new THREE.MeshBasicMaterial({
      color: colors.batik,
      transparent: true,
      opacity: 0.1,
      side: THREE.DoubleSide
    });
    const pattern = new THREE.Mesh(patternGeometry, patternMaterial);
    
    pattern.position.set(
      (Math.random() - 0.5) * 50,
      Math.random() * 20,
      (Math.random() - 0.5) * 50
    );
    pattern.rotation.set(
      Math.random() * Math.PI,
      Math.random() * Math.PI,
      Math.random() * Math.PI
    );
    
    overlay.add(pattern);
  }
  
  return overlay;
}
