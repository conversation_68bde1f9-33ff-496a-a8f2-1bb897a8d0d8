/**
 * KilatCSS Cyberpunk Theme 🔮
 * Neon glow futuristic theme with glitch effects
 */

[data-kilat-theme="cyberpunk"] {
  /* 🎨 Cyberpunk Color Palette */
  --k-primary: #00ffff;
  --k-secondary: #ff00ff;
  --k-accent: #ffff00;
  --k-background: #000000;
  --k-surface: #0a0a0a;
  --k-text: #ffffff;
  --k-text-muted: #888888;
  
  /* 🌈 Enhanced Neon Colors */
  --k-neon-blue: #00ffff;
  --k-neon-pink: #ff00ff;
  --k-neon-green: #00ff41;
  --k-neon-yellow: #ffff00;
  --k-neon-purple: #8000ff;
  --k-neon-orange: #ff8000;
  --k-neon-red: #ff0040;
  
  /* 🔮 Cyberpunk Specific Colors */
  --k-cyber-matrix: #00ff41;
  --k-cyber-neon: #00ffff;
  --k-cyber-glitch: #ff00ff;
  --k-cyber-warning: #ffff00;
  --k-cyber-danger: #ff0040;
  
  /* 🌊 Glow Intensities */
  --k-glow-sm: 0 0 5px currentColor, 0 0 10px currentColor;
  --k-glow-md: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;
  --k-glow-lg: 0 0 15px currentColor, 0 0 30px currentColor, 0 0 45px currentColor, 0 0 60px currentColor;
  --k-glow-xl: 0 0 20px currentColor, 0 0 40px currentColor, 0 0 60px currentColor, 0 0 80px currentColor, 0 0 100px currentColor;
  
  /* 🎭 Animation Speeds */
  --k-transition-fast: 100ms ease;
  --k-transition-normal: 200ms ease;
  --k-transition-slow: 400ms ease;
}

/* 🔮 Cyberpunk Body Styling */

[data-kilat-theme="cyberpunk"] body,
[data-kilat-theme="cyberpunk"] .kilat {
  background: 
    radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(0, 255, 65, 0.05) 0%, transparent 50%),
    #000000;
  color: var(--k-text);
  font-family: 'Orbitron', 'Exo 2', var(--k-font-sans);
}

/* 🌟 Cyberpunk Glow Text Effects */

[data-kilat-theme="cyberpunk"] .k-text-glow-cyber {
  color: var(--k-cyber-neon);
  text-shadow: 
    0 0 5px currentColor,
    0 0 10px currentColor,
    0 0 15px currentColor,
    0 0 20px currentColor;
  animation: cyber-pulse 2s ease-in-out infinite alternate;
}

[data-kilat-theme="cyberpunk"] .k-text-glow-matrix {
  color: var(--k-cyber-matrix);
  text-shadow: 
    0 0 5px currentColor,
    0 0 10px currentColor,
    0 0 15px currentColor;
  animation: matrix-flicker 3s linear infinite;
}

[data-kilat-theme="cyberpunk"] .k-text-glow-glitch {
  color: var(--k-cyber-glitch);
  text-shadow: 
    0 0 5px currentColor,
    0 0 10px currentColor;
  animation: glitch-text 0.5s ease-in-out infinite;
}

/* 🎭 Cyberpunk Animations */

@keyframes cyber-pulse {
  0% { 
    text-shadow: 
      0 0 5px currentColor,
      0 0 10px currentColor,
      0 0 15px currentColor,
      0 0 20px currentColor;
  }
  100% { 
    text-shadow: 
      0 0 2px currentColor,
      0 0 5px currentColor,
      0 0 8px currentColor,
      0 0 12px currentColor;
  }
}

@keyframes matrix-flicker {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
  75% { opacity: 0.9; }
}

@keyframes glitch-text {
  0% { transform: translate(0); }
  20% { transform: translate(-1px, 1px); }
  40% { transform: translate(-1px, -1px); }
  60% { transform: translate(1px, 1px); }
  80% { transform: translate(1px, -1px); }
  100% { transform: translate(0); }
}

@keyframes cyber-scan {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100vw); }
}

/* 🔲 Cyberpunk Borders */

[data-kilat-theme="cyberpunk"] .k-border-cyber {
  border: 1px solid var(--k-cyber-neon);
  box-shadow: 
    0 0 10px var(--k-cyber-neon),
    inset 0 0 10px rgba(0, 255, 255, 0.1);
  position: relative;
}

[data-kilat-theme="cyberpunk"] .k-border-cyber::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(45deg, var(--k-cyber-neon), var(--k-cyber-glitch), var(--k-cyber-matrix));
  z-index: -1;
  border-radius: inherit;
  opacity: 0.3;
  animation: cyber-border-glow 2s ease-in-out infinite alternate;
}

@keyframes cyber-border-glow {
  0% { opacity: 0.3; }
  100% { opacity: 0.6; }
}

/* 🎯 Cyberpunk Buttons */

[data-kilat-theme="cyberpunk"] .k-btn-cyber {
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(255, 0, 255, 0.1));
  border: 1px solid var(--k-cyber-neon);
  color: var(--k-cyber-neon);
  padding: 0.75rem 1.5rem;
  font-family: 'Orbitron', monospace;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  position: relative;
  overflow: hidden;
  transition: all var(--k-transition-normal);
  box-shadow: 
    0 0 20px rgba(0, 255, 255, 0.3),
    inset 0 0 20px rgba(0, 255, 255, 0.1);
}

[data-kilat-theme="cyberpunk"] .k-btn-cyber::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--k-transition-slow);
}

[data-kilat-theme="cyberpunk"] .k-btn-cyber:hover::before {
  left: 100%;
}

[data-kilat-theme="cyberpunk"] .k-btn-cyber:hover {
  box-shadow: 
    0 0 30px rgba(0, 255, 255, 0.5),
    inset 0 0 30px rgba(0, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* 🎮 Cyberpunk Cards */

[data-kilat-theme="cyberpunk"] .k-card-cyber {
  background: 
    linear-gradient(135deg, rgba(0, 255, 255, 0.05), rgba(255, 0, 255, 0.05)),
    rgba(10, 10, 10, 0.8);
  border: 1px solid rgba(0, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(0, 255, 255, 0.1),
    inset 0 0 20px rgba(0, 255, 255, 0.05);
  position: relative;
  overflow: hidden;
}

[data-kilat-theme="cyberpunk"] .k-card-cyber::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--k-cyber-neon), transparent);
  animation: cyber-scan 3s linear infinite;
}

/* 🔍 Cyberpunk Inputs */

[data-kilat-theme="cyberpunk"] .k-input-cyber {
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(0, 255, 255, 0.3);
  color: var(--k-text);
  padding: 0.75rem 1rem;
  font-family: 'Fira Code', monospace;
  transition: all var(--k-transition-normal);
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.5);
}

[data-kilat-theme="cyberpunk"] .k-input-cyber:focus {
  outline: none;
  border-color: var(--k-cyber-neon);
  box-shadow: 
    0 0 20px rgba(0, 255, 255, 0.3),
    inset 0 0 10px rgba(0, 255, 255, 0.1);
}

[data-kilat-theme="cyberpunk"] .k-input-cyber::-moz-placeholder {
  color: var(--k-text-muted);
  opacity: 0.7;
}

[data-kilat-theme="cyberpunk"] .k-input-cyber::placeholder {
  color: var(--k-text-muted);
  opacity: 0.7;
}

/* 🌐 Cyberpunk Scrollbar */

[data-kilat-theme="cyberpunk"] ::-webkit-scrollbar {
  width: 8px;
}

[data-kilat-theme="cyberpunk"] ::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.3);
}

[data-kilat-theme="cyberpunk"] ::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--k-cyber-neon), var(--k-cyber-glitch));
  border-radius: 4px;
  box-shadow: 0 0 10px currentColor;
}

[data-kilat-theme="cyberpunk"] ::-webkit-scrollbar-thumb:hover {
  box-shadow: 0 0 20px currentColor;
}
