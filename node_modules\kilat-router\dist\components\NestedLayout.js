import React from 'react';
import { Outlet } from 'react-router-dom';

/**
 * 🏗️ NestedLayout Component
 * Layout component for nested routes
 */
export function NestedLayout({ 
  children,
  header: Header,
  footer: Footer,
  sidebar: Sidebar,
  className = ''
}) {
  return React.createElement('div', { className: `k-nested-layout ${className}` },
    Header && React.createElement('header', { className: 'k-layout-header' },
      React.createElement(Header)
    ),
    
    React.createElement('div', { className: 'k-layout-main' },
      Sidebar && React.createElement('aside', { className: 'k-layout-sidebar' },
        React.createElement(Sidebar)
      ),
      
      React.createElement('main', { className: 'k-layout-content' },
        children || React.createElement(Outlet)
      )
    ),
    
    Footer && React.createElement('footer', { className: 'k-layout-footer' },
      React.createElement(Footer)
    )
  );
}

export default NestedLayout;
