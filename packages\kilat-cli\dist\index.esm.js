var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __require = /* @__PURE__ */ ((x) => typeof require !== "undefined" ? require : typeof Proxy !== "undefined" ? new Proxy(x, {
  get: (a, b) => (typeof require !== "undefined" ? require : a)[b]
}) : x)(function(x) {
  if (typeof require !== "undefined")
    return require.apply(this, arguments);
  throw Error('Dynamic require of "' + x + '" is not supported');
});
var __esm = (fn, res) => function __init() {
  return fn && (res = (0, fn[__getOwnPropNames(fn)[0]])(fn = 0)), res;
};
var __commonJS = (cb, mod) => function __require2() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));

// ../../node_modules/isexe/windows.js
var require_windows = __commonJS({
  "../../node_modules/isexe/windows.js"(exports, module) {
    module.exports = isexe;
    isexe.sync = sync;
    var fs = __require("fs");
    function checkPathExt(path3, options) {
      var pathext = options.pathExt !== void 0 ? options.pathExt : process.env.PATHEXT;
      if (!pathext) {
        return true;
      }
      pathext = pathext.split(";");
      if (pathext.indexOf("") !== -1) {
        return true;
      }
      for (var i = 0; i < pathext.length; i++) {
        var p = pathext[i].toLowerCase();
        if (p && path3.substr(-p.length).toLowerCase() === p) {
          return true;
        }
      }
      return false;
    }
    function checkStat(stat2, path3, options) {
      if (!stat2.isSymbolicLink() && !stat2.isFile()) {
        return false;
      }
      return checkPathExt(path3, options);
    }
    function isexe(path3, options, cb) {
      fs.stat(path3, function(er, stat2) {
        cb(er, er ? false : checkStat(stat2, path3, options));
      });
    }
    function sync(path3, options) {
      return checkStat(fs.statSync(path3), path3, options);
    }
  }
});

// ../../node_modules/isexe/mode.js
var require_mode = __commonJS({
  "../../node_modules/isexe/mode.js"(exports, module) {
    module.exports = isexe;
    isexe.sync = sync;
    var fs = __require("fs");
    function isexe(path3, options, cb) {
      fs.stat(path3, function(er, stat2) {
        cb(er, er ? false : checkStat(stat2, options));
      });
    }
    function sync(path3, options) {
      return checkStat(fs.statSync(path3), options);
    }
    function checkStat(stat2, options) {
      return stat2.isFile() && checkMode(stat2, options);
    }
    function checkMode(stat2, options) {
      var mod = stat2.mode;
      var uid = stat2.uid;
      var gid = stat2.gid;
      var myUid = options.uid !== void 0 ? options.uid : process.getuid && process.getuid();
      var myGid = options.gid !== void 0 ? options.gid : process.getgid && process.getgid();
      var u = parseInt("100", 8);
      var g = parseInt("010", 8);
      var o = parseInt("001", 8);
      var ug = u | g;
      var ret = mod & o || mod & g && gid === myGid || mod & u && uid === myUid || mod & ug && myUid === 0;
      return ret;
    }
  }
});

// ../../node_modules/isexe/index.js
var require_isexe = __commonJS({
  "../../node_modules/isexe/index.js"(exports, module) {
    var fs = __require("fs");
    var core;
    if (process.platform === "win32" || global.TESTING_WINDOWS) {
      core = require_windows();
    } else {
      core = require_mode();
    }
    module.exports = isexe;
    isexe.sync = sync;
    function isexe(path3, options, cb) {
      if (typeof options === "function") {
        cb = options;
        options = {};
      }
      if (!cb) {
        if (typeof Promise !== "function") {
          throw new TypeError("callback not provided");
        }
        return new Promise(function(resolve, reject) {
          isexe(path3, options || {}, function(er, is) {
            if (er) {
              reject(er);
            } else {
              resolve(is);
            }
          });
        });
      }
      core(path3, options || {}, function(er, is) {
        if (er) {
          if (er.code === "EACCES" || options && options.ignoreErrors) {
            er = null;
            is = false;
          }
        }
        cb(er, is);
      });
    }
    function sync(path3, options) {
      try {
        return core.sync(path3, options || {});
      } catch (er) {
        if (options && options.ignoreErrors || er.code === "EACCES") {
          return false;
        } else {
          throw er;
        }
      }
    }
  }
});

// ../../node_modules/which/which.js
var require_which = __commonJS({
  "../../node_modules/which/which.js"(exports, module) {
    var isWindows = process.platform === "win32" || process.env.OSTYPE === "cygwin" || process.env.OSTYPE === "msys";
    var path3 = __require("path");
    var COLON = isWindows ? ";" : ":";
    var isexe = require_isexe();
    var getNotFoundError = (cmd) => Object.assign(new Error(`not found: ${cmd}`), { code: "ENOENT" });
    var getPathInfo = (cmd, opt) => {
      const colon = opt.colon || COLON;
      const pathEnv = cmd.match(/\//) || isWindows && cmd.match(/\\/) ? [""] : [
        // windows always checks the cwd first
        ...isWindows ? [process.cwd()] : [],
        ...(opt.path || process.env.PATH || /* istanbul ignore next: very unusual */
        "").split(colon)
      ];
      const pathExtExe = isWindows ? opt.pathExt || process.env.PATHEXT || ".EXE;.CMD;.BAT;.COM" : "";
      const pathExt = isWindows ? pathExtExe.split(colon) : [""];
      if (isWindows) {
        if (cmd.indexOf(".") !== -1 && pathExt[0] !== "")
          pathExt.unshift("");
      }
      return {
        pathEnv,
        pathExt,
        pathExtExe
      };
    };
    var which = (cmd, opt, cb) => {
      if (typeof opt === "function") {
        cb = opt;
        opt = {};
      }
      if (!opt)
        opt = {};
      const { pathEnv, pathExt, pathExtExe } = getPathInfo(cmd, opt);
      const found = [];
      const step = (i) => new Promise((resolve, reject) => {
        if (i === pathEnv.length)
          return opt.all && found.length ? resolve(found) : reject(getNotFoundError(cmd));
        const ppRaw = pathEnv[i];
        const pathPart = /^".*"$/.test(ppRaw) ? ppRaw.slice(1, -1) : ppRaw;
        const pCmd = path3.join(pathPart, cmd);
        const p = !pathPart && /^\.[\\\/]/.test(cmd) ? cmd.slice(0, 2) + pCmd : pCmd;
        resolve(subStep(p, i, 0));
      });
      const subStep = (p, i, ii) => new Promise((resolve, reject) => {
        if (ii === pathExt.length)
          return resolve(step(i + 1));
        const ext = pathExt[ii];
        isexe(p + ext, { pathExt: pathExtExe }, (er, is) => {
          if (!er && is) {
            if (opt.all)
              found.push(p + ext);
            else
              return resolve(p + ext);
          }
          return resolve(subStep(p, i, ii + 1));
        });
      });
      return cb ? step(0).then((res) => cb(null, res), cb) : step(0);
    };
    var whichSync = (cmd, opt) => {
      opt = opt || {};
      const { pathEnv, pathExt, pathExtExe } = getPathInfo(cmd, opt);
      const found = [];
      for (let i = 0; i < pathEnv.length; i++) {
        const ppRaw = pathEnv[i];
        const pathPart = /^".*"$/.test(ppRaw) ? ppRaw.slice(1, -1) : ppRaw;
        const pCmd = path3.join(pathPart, cmd);
        const p = !pathPart && /^\.[\\\/]/.test(cmd) ? cmd.slice(0, 2) + pCmd : pCmd;
        for (let j = 0; j < pathExt.length; j++) {
          const cur = p + pathExt[j];
          try {
            const is = isexe.sync(cur, { pathExt: pathExtExe });
            if (is) {
              if (opt.all)
                found.push(cur);
              else
                return cur;
            }
          } catch (ex) {
          }
        }
      }
      if (opt.all && found.length)
        return found;
      if (opt.nothrow)
        return null;
      throw getNotFoundError(cmd);
    };
    module.exports = which;
    which.sync = whichSync;
  }
});

// ../../node_modules/path-key/index.js
var require_path_key = __commonJS({
  "../../node_modules/path-key/index.js"(exports, module) {
    "use strict";
    var pathKey2 = (options = {}) => {
      const environment = options.env || process.env;
      const platform = options.platform || process.platform;
      if (platform !== "win32") {
        return "PATH";
      }
      return Object.keys(environment).reverse().find((key) => key.toUpperCase() === "PATH") || "Path";
    };
    module.exports = pathKey2;
    module.exports.default = pathKey2;
  }
});

// ../../node_modules/cross-spawn/lib/util/resolveCommand.js
var require_resolveCommand = __commonJS({
  "../../node_modules/cross-spawn/lib/util/resolveCommand.js"(exports, module) {
    "use strict";
    var path3 = __require("path");
    var which = require_which();
    var getPathKey = require_path_key();
    function resolveCommandAttempt(parsed, withoutPathExt) {
      const env = parsed.options.env || process.env;
      const cwd = process.cwd();
      const hasCustomCwd = parsed.options.cwd != null;
      const shouldSwitchCwd = hasCustomCwd && process.chdir !== void 0 && !process.chdir.disabled;
      if (shouldSwitchCwd) {
        try {
          process.chdir(parsed.options.cwd);
        } catch (err) {
        }
      }
      let resolved;
      try {
        resolved = which.sync(parsed.command, {
          path: env[getPathKey({ env })],
          pathExt: withoutPathExt ? path3.delimiter : void 0
        });
      } catch (e) {
      } finally {
        if (shouldSwitchCwd) {
          process.chdir(cwd);
        }
      }
      if (resolved) {
        resolved = path3.resolve(hasCustomCwd ? parsed.options.cwd : "", resolved);
      }
      return resolved;
    }
    function resolveCommand(parsed) {
      return resolveCommandAttempt(parsed) || resolveCommandAttempt(parsed, true);
    }
    module.exports = resolveCommand;
  }
});

// ../../node_modules/cross-spawn/lib/util/escape.js
var require_escape = __commonJS({
  "../../node_modules/cross-spawn/lib/util/escape.js"(exports, module) {
    "use strict";
    var metaCharsRegExp = /([()\][%!^"`<>&|;, *?])/g;
    function escapeCommand(arg) {
      arg = arg.replace(metaCharsRegExp, "^$1");
      return arg;
    }
    function escapeArgument(arg, doubleEscapeMetaChars) {
      arg = `${arg}`;
      arg = arg.replace(/(?=(\\+?)?)\1"/g, '$1$1\\"');
      arg = arg.replace(/(?=(\\+?)?)\1$/, "$1$1");
      arg = `"${arg}"`;
      arg = arg.replace(metaCharsRegExp, "^$1");
      if (doubleEscapeMetaChars) {
        arg = arg.replace(metaCharsRegExp, "^$1");
      }
      return arg;
    }
    module.exports.command = escapeCommand;
    module.exports.argument = escapeArgument;
  }
});

// ../../node_modules/shebang-regex/index.js
var require_shebang_regex = __commonJS({
  "../../node_modules/shebang-regex/index.js"(exports, module) {
    "use strict";
    module.exports = /^#!(.*)/;
  }
});

// ../../node_modules/shebang-command/index.js
var require_shebang_command = __commonJS({
  "../../node_modules/shebang-command/index.js"(exports, module) {
    "use strict";
    var shebangRegex = require_shebang_regex();
    module.exports = (string = "") => {
      const match = string.match(shebangRegex);
      if (!match) {
        return null;
      }
      const [path3, argument] = match[0].replace(/#! ?/, "").split(" ");
      const binary = path3.split("/").pop();
      if (binary === "env") {
        return argument;
      }
      return argument ? `${binary} ${argument}` : binary;
    };
  }
});

// ../../node_modules/cross-spawn/lib/util/readShebang.js
var require_readShebang = __commonJS({
  "../../node_modules/cross-spawn/lib/util/readShebang.js"(exports, module) {
    "use strict";
    var fs = __require("fs");
    var shebangCommand = require_shebang_command();
    function readShebang(command) {
      const size = 150;
      const buffer = Buffer.alloc(size);
      let fd;
      try {
        fd = fs.openSync(command, "r");
        fs.readSync(fd, buffer, 0, size, 0);
        fs.closeSync(fd);
      } catch (e) {
      }
      return shebangCommand(buffer.toString());
    }
    module.exports = readShebang;
  }
});

// ../../node_modules/cross-spawn/lib/parse.js
var require_parse = __commonJS({
  "../../node_modules/cross-spawn/lib/parse.js"(exports, module) {
    "use strict";
    var path3 = __require("path");
    var resolveCommand = require_resolveCommand();
    var escape = require_escape();
    var readShebang = require_readShebang();
    var isWin = process.platform === "win32";
    var isExecutableRegExp = /\.(?:com|exe)$/i;
    var isCmdShimRegExp = /node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i;
    function detectShebang(parsed) {
      parsed.file = resolveCommand(parsed);
      const shebang = parsed.file && readShebang(parsed.file);
      if (shebang) {
        parsed.args.unshift(parsed.file);
        parsed.command = shebang;
        return resolveCommand(parsed);
      }
      return parsed.file;
    }
    function parseNonShell(parsed) {
      if (!isWin) {
        return parsed;
      }
      const commandFile = detectShebang(parsed);
      const needsShell = !isExecutableRegExp.test(commandFile);
      if (parsed.options.forceShell || needsShell) {
        const needsDoubleEscapeMetaChars = isCmdShimRegExp.test(commandFile);
        parsed.command = path3.normalize(parsed.command);
        parsed.command = escape.command(parsed.command);
        parsed.args = parsed.args.map((arg) => escape.argument(arg, needsDoubleEscapeMetaChars));
        const shellCommand = [parsed.command].concat(parsed.args).join(" ");
        parsed.args = ["/d", "/s", "/c", `"${shellCommand}"`];
        parsed.command = process.env.comspec || "cmd.exe";
        parsed.options.windowsVerbatimArguments = true;
      }
      return parsed;
    }
    function parse(command, args, options) {
      if (args && !Array.isArray(args)) {
        options = args;
        args = null;
      }
      args = args ? args.slice(0) : [];
      options = Object.assign({}, options);
      const parsed = {
        command,
        args,
        options,
        file: void 0,
        original: {
          command,
          args
        }
      };
      return options.shell ? parsed : parseNonShell(parsed);
    }
    module.exports = parse;
  }
});

// ../../node_modules/cross-spawn/lib/enoent.js
var require_enoent = __commonJS({
  "../../node_modules/cross-spawn/lib/enoent.js"(exports, module) {
    "use strict";
    var isWin = process.platform === "win32";
    function notFoundError(original, syscall) {
      return Object.assign(new Error(`${syscall} ${original.command} ENOENT`), {
        code: "ENOENT",
        errno: "ENOENT",
        syscall: `${syscall} ${original.command}`,
        path: original.command,
        spawnargs: original.args
      });
    }
    function hookChildProcess(cp, parsed) {
      if (!isWin) {
        return;
      }
      const originalEmit = cp.emit;
      cp.emit = function(name, arg1) {
        if (name === "exit") {
          const err = verifyENOENT(arg1, parsed);
          if (err) {
            return originalEmit.call(cp, "error", err);
          }
        }
        return originalEmit.apply(cp, arguments);
      };
    }
    function verifyENOENT(status, parsed) {
      if (isWin && status === 1 && !parsed.file) {
        return notFoundError(parsed.original, "spawn");
      }
      return null;
    }
    function verifyENOENTSync(status, parsed) {
      if (isWin && status === 1 && !parsed.file) {
        return notFoundError(parsed.original, "spawnSync");
      }
      return null;
    }
    module.exports = {
      hookChildProcess,
      verifyENOENT,
      verifyENOENTSync,
      notFoundError
    };
  }
});

// ../../node_modules/cross-spawn/index.js
var require_cross_spawn = __commonJS({
  "../../node_modules/cross-spawn/index.js"(exports, module) {
    "use strict";
    var cp = __require("child_process");
    var parse = require_parse();
    var enoent = require_enoent();
    function spawn2(command, args, options) {
      const parsed = parse(command, args, options);
      const spawned = cp.spawn(parsed.command, parsed.args, parsed.options);
      enoent.hookChildProcess(spawned, parsed);
      return spawned;
    }
    function spawnSync(command, args, options) {
      const parsed = parse(command, args, options);
      const result = cp.spawnSync(parsed.command, parsed.args, parsed.options);
      result.error = result.error || enoent.verifyENOENTSync(result.status, parsed);
      return result;
    }
    module.exports = spawn2;
    module.exports.spawn = spawn2;
    module.exports.sync = spawnSync;
    module.exports._parse = parse;
    module.exports._enoent = enoent;
  }
});

// ../../node_modules/strip-final-newline/index.js
function stripFinalNewline(input) {
  const LF = typeof input === "string" ? "\n" : "\n".charCodeAt();
  const CR = typeof input === "string" ? "\r" : "\r".charCodeAt();
  if (input[input.length - 1] === LF) {
    input = input.slice(0, -1);
  }
  if (input[input.length - 1] === CR) {
    input = input.slice(0, -1);
  }
  return input;
}
var init_strip_final_newline = __esm({
  "../../node_modules/strip-final-newline/index.js"() {
  }
});

// ../../node_modules/npm-run-path/node_modules/path-key/index.js
function pathKey(options = {}) {
  const {
    env = process.env,
    platform = process.platform
  } = options;
  if (platform !== "win32") {
    return "PATH";
  }
  return Object.keys(env).reverse().find((key) => key.toUpperCase() === "PATH") || "Path";
}
var init_path_key = __esm({
  "../../node_modules/npm-run-path/node_modules/path-key/index.js"() {
  }
});

// ../../node_modules/npm-run-path/index.js
import process2 from "node:process";
import path from "node:path";
import { fileURLToPath } from "node:url";
var npmRunPath, applyPreferLocal, applyExecPath, npmRunPathEnv;
var init_npm_run_path = __esm({
  "../../node_modules/npm-run-path/index.js"() {
    init_path_key();
    npmRunPath = ({
      cwd = process2.cwd(),
      path: pathOption = process2.env[pathKey()],
      preferLocal = true,
      execPath = process2.execPath,
      addExecPath = true
    } = {}) => {
      const cwdString = cwd instanceof URL ? fileURLToPath(cwd) : cwd;
      const cwdPath = path.resolve(cwdString);
      const result = [];
      if (preferLocal) {
        applyPreferLocal(result, cwdPath);
      }
      if (addExecPath) {
        applyExecPath(result, execPath, cwdPath);
      }
      return [...result, pathOption].join(path.delimiter);
    };
    applyPreferLocal = (result, cwdPath) => {
      let previous;
      while (previous !== cwdPath) {
        result.push(path.join(cwdPath, "node_modules/.bin"));
        previous = cwdPath;
        cwdPath = path.resolve(cwdPath, "..");
      }
    };
    applyExecPath = (result, execPath, cwdPath) => {
      const execPathString = execPath instanceof URL ? fileURLToPath(execPath) : execPath;
      result.push(path.resolve(cwdPath, execPathString, ".."));
    };
    npmRunPathEnv = ({ env = process2.env, ...options } = {}) => {
      env = { ...env };
      const pathName = pathKey({ env });
      options.path = env[pathName];
      env[pathName] = npmRunPath(options);
      return env;
    };
  }
});

// ../../node_modules/mimic-fn/index.js
function mimicFunction(to, from, { ignoreNonConfigurable = false } = {}) {
  const { name } = to;
  for (const property of Reflect.ownKeys(from)) {
    copyProperty(to, from, property, ignoreNonConfigurable);
  }
  changePrototype(to, from);
  changeToString(to, from, name);
  return to;
}
var copyProperty, canCopyProperty, changePrototype, wrappedToString, toStringDescriptor, toStringName, changeToString;
var init_mimic_fn = __esm({
  "../../node_modules/mimic-fn/index.js"() {
    copyProperty = (to, from, property, ignoreNonConfigurable) => {
      if (property === "length" || property === "prototype") {
        return;
      }
      if (property === "arguments" || property === "caller") {
        return;
      }
      const toDescriptor = Object.getOwnPropertyDescriptor(to, property);
      const fromDescriptor = Object.getOwnPropertyDescriptor(from, property);
      if (!canCopyProperty(toDescriptor, fromDescriptor) && ignoreNonConfigurable) {
        return;
      }
      Object.defineProperty(to, property, fromDescriptor);
    };
    canCopyProperty = function(toDescriptor, fromDescriptor) {
      return toDescriptor === void 0 || toDescriptor.configurable || toDescriptor.writable === fromDescriptor.writable && toDescriptor.enumerable === fromDescriptor.enumerable && toDescriptor.configurable === fromDescriptor.configurable && (toDescriptor.writable || toDescriptor.value === fromDescriptor.value);
    };
    changePrototype = (to, from) => {
      const fromPrototype = Object.getPrototypeOf(from);
      if (fromPrototype === Object.getPrototypeOf(to)) {
        return;
      }
      Object.setPrototypeOf(to, fromPrototype);
    };
    wrappedToString = (withName, fromBody) => `/* Wrapped ${withName}*/
${fromBody}`;
    toStringDescriptor = Object.getOwnPropertyDescriptor(Function.prototype, "toString");
    toStringName = Object.getOwnPropertyDescriptor(Function.prototype.toString, "name");
    changeToString = (to, from, name) => {
      const withName = name === "" ? "" : `with ${name.trim()}() `;
      const newToString = wrappedToString.bind(null, withName, from.toString());
      Object.defineProperty(newToString, "name", toStringName);
      Object.defineProperty(to, "toString", { ...toStringDescriptor, value: newToString });
    };
  }
});

// ../../node_modules/onetime/index.js
var calledFunctions, onetime, onetime_default;
var init_onetime = __esm({
  "../../node_modules/onetime/index.js"() {
    init_mimic_fn();
    calledFunctions = /* @__PURE__ */ new WeakMap();
    onetime = (function_, options = {}) => {
      if (typeof function_ !== "function") {
        throw new TypeError("Expected a function");
      }
      let returnValue;
      let callCount = 0;
      const functionName = function_.displayName || function_.name || "<anonymous>";
      const onetime2 = function(...arguments_) {
        calledFunctions.set(onetime2, ++callCount);
        if (callCount === 1) {
          returnValue = function_.apply(this, arguments_);
          function_ = null;
        } else if (options.throw === true) {
          throw new Error(`Function \`${functionName}\` can only be called once`);
        }
        return returnValue;
      };
      mimicFunction(onetime2, function_);
      calledFunctions.set(onetime2, callCount);
      return onetime2;
    };
    onetime.callCount = (function_) => {
      if (!calledFunctions.has(function_)) {
        throw new Error(`The given function \`${function_.name}\` is not wrapped by the \`onetime\` package`);
      }
      return calledFunctions.get(function_);
    };
    onetime_default = onetime;
  }
});

// ../../node_modules/human-signals/build/src/realtime.js
var getRealtimeSignals, getRealtimeSignal, SIGRTMIN, SIGRTMAX;
var init_realtime = __esm({
  "../../node_modules/human-signals/build/src/realtime.js"() {
    getRealtimeSignals = () => {
      const length = SIGRTMAX - SIGRTMIN + 1;
      return Array.from({ length }, getRealtimeSignal);
    };
    getRealtimeSignal = (value, index) => ({
      name: `SIGRT${index + 1}`,
      number: SIGRTMIN + index,
      action: "terminate",
      description: "Application-specific signal (realtime)",
      standard: "posix"
    });
    SIGRTMIN = 34;
    SIGRTMAX = 64;
  }
});

// ../../node_modules/human-signals/build/src/core.js
var SIGNALS;
var init_core = __esm({
  "../../node_modules/human-signals/build/src/core.js"() {
    SIGNALS = [
      {
        name: "SIGHUP",
        number: 1,
        action: "terminate",
        description: "Terminal closed",
        standard: "posix"
      },
      {
        name: "SIGINT",
        number: 2,
        action: "terminate",
        description: "User interruption with CTRL-C",
        standard: "ansi"
      },
      {
        name: "SIGQUIT",
        number: 3,
        action: "core",
        description: "User interruption with CTRL-\\",
        standard: "posix"
      },
      {
        name: "SIGILL",
        number: 4,
        action: "core",
        description: "Invalid machine instruction",
        standard: "ansi"
      },
      {
        name: "SIGTRAP",
        number: 5,
        action: "core",
        description: "Debugger breakpoint",
        standard: "posix"
      },
      {
        name: "SIGABRT",
        number: 6,
        action: "core",
        description: "Aborted",
        standard: "ansi"
      },
      {
        name: "SIGIOT",
        number: 6,
        action: "core",
        description: "Aborted",
        standard: "bsd"
      },
      {
        name: "SIGBUS",
        number: 7,
        action: "core",
        description: "Bus error due to misaligned, non-existing address or paging error",
        standard: "bsd"
      },
      {
        name: "SIGEMT",
        number: 7,
        action: "terminate",
        description: "Command should be emulated but is not implemented",
        standard: "other"
      },
      {
        name: "SIGFPE",
        number: 8,
        action: "core",
        description: "Floating point arithmetic error",
        standard: "ansi"
      },
      {
        name: "SIGKILL",
        number: 9,
        action: "terminate",
        description: "Forced termination",
        standard: "posix",
        forced: true
      },
      {
        name: "SIGUSR1",
        number: 10,
        action: "terminate",
        description: "Application-specific signal",
        standard: "posix"
      },
      {
        name: "SIGSEGV",
        number: 11,
        action: "core",
        description: "Segmentation fault",
        standard: "ansi"
      },
      {
        name: "SIGUSR2",
        number: 12,
        action: "terminate",
        description: "Application-specific signal",
        standard: "posix"
      },
      {
        name: "SIGPIPE",
        number: 13,
        action: "terminate",
        description: "Broken pipe or socket",
        standard: "posix"
      },
      {
        name: "SIGALRM",
        number: 14,
        action: "terminate",
        description: "Timeout or timer",
        standard: "posix"
      },
      {
        name: "SIGTERM",
        number: 15,
        action: "terminate",
        description: "Termination",
        standard: "ansi"
      },
      {
        name: "SIGSTKFLT",
        number: 16,
        action: "terminate",
        description: "Stack is empty or overflowed",
        standard: "other"
      },
      {
        name: "SIGCHLD",
        number: 17,
        action: "ignore",
        description: "Child process terminated, paused or unpaused",
        standard: "posix"
      },
      {
        name: "SIGCLD",
        number: 17,
        action: "ignore",
        description: "Child process terminated, paused or unpaused",
        standard: "other"
      },
      {
        name: "SIGCONT",
        number: 18,
        action: "unpause",
        description: "Unpaused",
        standard: "posix",
        forced: true
      },
      {
        name: "SIGSTOP",
        number: 19,
        action: "pause",
        description: "Paused",
        standard: "posix",
        forced: true
      },
      {
        name: "SIGTSTP",
        number: 20,
        action: "pause",
        description: 'Paused using CTRL-Z or "suspend"',
        standard: "posix"
      },
      {
        name: "SIGTTIN",
        number: 21,
        action: "pause",
        description: "Background process cannot read terminal input",
        standard: "posix"
      },
      {
        name: "SIGBREAK",
        number: 21,
        action: "terminate",
        description: "User interruption with CTRL-BREAK",
        standard: "other"
      },
      {
        name: "SIGTTOU",
        number: 22,
        action: "pause",
        description: "Background process cannot write to terminal output",
        standard: "posix"
      },
      {
        name: "SIGURG",
        number: 23,
        action: "ignore",
        description: "Socket received out-of-band data",
        standard: "bsd"
      },
      {
        name: "SIGXCPU",
        number: 24,
        action: "core",
        description: "Process timed out",
        standard: "bsd"
      },
      {
        name: "SIGXFSZ",
        number: 25,
        action: "core",
        description: "File too big",
        standard: "bsd"
      },
      {
        name: "SIGVTALRM",
        number: 26,
        action: "terminate",
        description: "Timeout or timer",
        standard: "bsd"
      },
      {
        name: "SIGPROF",
        number: 27,
        action: "terminate",
        description: "Timeout or timer",
        standard: "bsd"
      },
      {
        name: "SIGWINCH",
        number: 28,
        action: "ignore",
        description: "Terminal window size changed",
        standard: "bsd"
      },
      {
        name: "SIGIO",
        number: 29,
        action: "terminate",
        description: "I/O is available",
        standard: "other"
      },
      {
        name: "SIGPOLL",
        number: 29,
        action: "terminate",
        description: "Watched event",
        standard: "other"
      },
      {
        name: "SIGINFO",
        number: 29,
        action: "ignore",
        description: "Request for process information",
        standard: "other"
      },
      {
        name: "SIGPWR",
        number: 30,
        action: "terminate",
        description: "Device running out of power",
        standard: "systemv"
      },
      {
        name: "SIGSYS",
        number: 31,
        action: "core",
        description: "Invalid system call",
        standard: "other"
      },
      {
        name: "SIGUNUSED",
        number: 31,
        action: "terminate",
        description: "Invalid system call",
        standard: "other"
      }
    ];
  }
});

// ../../node_modules/human-signals/build/src/signals.js
import { constants } from "node:os";
var getSignals, normalizeSignal;
var init_signals = __esm({
  "../../node_modules/human-signals/build/src/signals.js"() {
    init_core();
    init_realtime();
    getSignals = () => {
      const realtimeSignals = getRealtimeSignals();
      const signals2 = [...SIGNALS, ...realtimeSignals].map(normalizeSignal);
      return signals2;
    };
    normalizeSignal = ({
      name,
      number: defaultNumber,
      description,
      action,
      forced = false,
      standard
    }) => {
      const {
        signals: { [name]: constantSignal }
      } = constants;
      const supported = constantSignal !== void 0;
      const number = supported ? constantSignal : defaultNumber;
      return { name, number, description, supported, action, forced, standard };
    };
  }
});

// ../../node_modules/human-signals/build/src/main.js
import { constants as constants2 } from "node:os";
var getSignalsByName, getSignalByName, signalsByName, getSignalsByNumber, getSignalByNumber, findSignalByNumber, signalsByNumber;
var init_main = __esm({
  "../../node_modules/human-signals/build/src/main.js"() {
    init_realtime();
    init_signals();
    getSignalsByName = () => {
      const signals2 = getSignals();
      return Object.fromEntries(signals2.map(getSignalByName));
    };
    getSignalByName = ({
      name,
      number,
      description,
      supported,
      action,
      forced,
      standard
    }) => [name, { name, number, description, supported, action, forced, standard }];
    signalsByName = getSignalsByName();
    getSignalsByNumber = () => {
      const signals2 = getSignals();
      const length = SIGRTMAX + 1;
      const signalsA = Array.from(
        { length },
        (value, number) => getSignalByNumber(number, signals2)
      );
      return Object.assign({}, ...signalsA);
    };
    getSignalByNumber = (number, signals2) => {
      const signal = findSignalByNumber(number, signals2);
      if (signal === void 0) {
        return {};
      }
      const { name, description, supported, action, forced, standard } = signal;
      return {
        [number]: {
          name,
          number,
          description,
          supported,
          action,
          forced,
          standard
        }
      };
    };
    findSignalByNumber = (number, signals2) => {
      const signal = signals2.find(({ name }) => constants2.signals[name] === number);
      if (signal !== void 0) {
        return signal;
      }
      return signals2.find((signalA) => signalA.number === number);
    };
    signalsByNumber = getSignalsByNumber();
  }
});

// ../../node_modules/execa/lib/error.js
import process3 from "node:process";
var getErrorPrefix, makeError;
var init_error = __esm({
  "../../node_modules/execa/lib/error.js"() {
    init_main();
    getErrorPrefix = ({ timedOut, timeout, errorCode, signal, signalDescription, exitCode, isCanceled }) => {
      if (timedOut) {
        return `timed out after ${timeout} milliseconds`;
      }
      if (isCanceled) {
        return "was canceled";
      }
      if (errorCode !== void 0) {
        return `failed with ${errorCode}`;
      }
      if (signal !== void 0) {
        return `was killed with ${signal} (${signalDescription})`;
      }
      if (exitCode !== void 0) {
        return `failed with exit code ${exitCode}`;
      }
      return "failed";
    };
    makeError = ({
      stdout,
      stderr,
      all,
      error,
      signal,
      exitCode,
      command,
      escapedCommand,
      timedOut,
      isCanceled,
      killed,
      parsed: { options: { timeout, cwd = process3.cwd() } }
    }) => {
      exitCode = exitCode === null ? void 0 : exitCode;
      signal = signal === null ? void 0 : signal;
      const signalDescription = signal === void 0 ? void 0 : signalsByName[signal].description;
      const errorCode = error && error.code;
      const prefix = getErrorPrefix({ timedOut, timeout, errorCode, signal, signalDescription, exitCode, isCanceled });
      const execaMessage = `Command ${prefix}: ${command}`;
      const isError = Object.prototype.toString.call(error) === "[object Error]";
      const shortMessage = isError ? `${execaMessage}
${error.message}` : execaMessage;
      const message = [shortMessage, stderr, stdout].filter(Boolean).join("\n");
      if (isError) {
        error.originalMessage = error.message;
        error.message = message;
      } else {
        error = new Error(message);
      }
      error.shortMessage = shortMessage;
      error.command = command;
      error.escapedCommand = escapedCommand;
      error.exitCode = exitCode;
      error.signal = signal;
      error.signalDescription = signalDescription;
      error.stdout = stdout;
      error.stderr = stderr;
      error.cwd = cwd;
      if (all !== void 0) {
        error.all = all;
      }
      if ("bufferedData" in error) {
        delete error.bufferedData;
      }
      error.failed = true;
      error.timedOut = Boolean(timedOut);
      error.isCanceled = isCanceled;
      error.killed = killed && !timedOut;
      return error;
    };
  }
});

// ../../node_modules/execa/lib/stdio.js
var aliases, hasAlias, normalizeStdio, normalizeStdioNode;
var init_stdio = __esm({
  "../../node_modules/execa/lib/stdio.js"() {
    aliases = ["stdin", "stdout", "stderr"];
    hasAlias = (options) => aliases.some((alias) => options[alias] !== void 0);
    normalizeStdio = (options) => {
      if (!options) {
        return;
      }
      const { stdio } = options;
      if (stdio === void 0) {
        return aliases.map((alias) => options[alias]);
      }
      if (hasAlias(options)) {
        throw new Error(`It's not possible to provide \`stdio\` in combination with one of ${aliases.map((alias) => `\`${alias}\``).join(", ")}`);
      }
      if (typeof stdio === "string") {
        return stdio;
      }
      if (!Array.isArray(stdio)) {
        throw new TypeError(`Expected \`stdio\` to be of type \`string\` or \`Array\`, got \`${typeof stdio}\``);
      }
      const length = Math.max(stdio.length, aliases.length);
      return Array.from({ length }, (value, index) => stdio[index]);
    };
    normalizeStdioNode = (options) => {
      const stdio = normalizeStdio(options);
      if (stdio === "ipc") {
        return "ipc";
      }
      if (stdio === void 0 || typeof stdio === "string") {
        return [stdio, stdio, stdio, "ipc"];
      }
      if (stdio.includes("ipc")) {
        return stdio;
      }
      return [...stdio, "ipc"];
    };
  }
});

// ../../node_modules/signal-exit/dist/mjs/signals.js
var signals;
var init_signals2 = __esm({
  "../../node_modules/signal-exit/dist/mjs/signals.js"() {
    signals = [];
    signals.push("SIGHUP", "SIGINT", "SIGTERM");
    if (process.platform !== "win32") {
      signals.push(
        "SIGALRM",
        "SIGABRT",
        "SIGVTALRM",
        "SIGXCPU",
        "SIGXFSZ",
        "SIGUSR2",
        "SIGTRAP",
        "SIGSYS",
        "SIGQUIT",
        "SIGIOT"
        // should detect profiler and enable/disable accordingly.
        // see #21
        // 'SIGPROF'
      );
    }
    if (process.platform === "linux") {
      signals.push("SIGIO", "SIGPOLL", "SIGPWR", "SIGSTKFLT");
    }
  }
});

// ../../node_modules/signal-exit/dist/mjs/index.js
var processOk, kExitEmitter, global2, ObjectDefineProperty, Emitter, SignalExitBase, signalExitWrap, SignalExitFallback, SignalExit, process4, onExit, load, unload;
var init_mjs = __esm({
  "../../node_modules/signal-exit/dist/mjs/index.js"() {
    init_signals2();
    processOk = (process7) => !!process7 && typeof process7 === "object" && typeof process7.removeListener === "function" && typeof process7.emit === "function" && typeof process7.reallyExit === "function" && typeof process7.listeners === "function" && typeof process7.kill === "function" && typeof process7.pid === "number" && typeof process7.on === "function";
    kExitEmitter = Symbol.for("signal-exit emitter");
    global2 = globalThis;
    ObjectDefineProperty = Object.defineProperty.bind(Object);
    Emitter = class {
      emitted = {
        afterExit: false,
        exit: false
      };
      listeners = {
        afterExit: [],
        exit: []
      };
      count = 0;
      id = Math.random();
      constructor() {
        if (global2[kExitEmitter]) {
          return global2[kExitEmitter];
        }
        ObjectDefineProperty(global2, kExitEmitter, {
          value: this,
          writable: false,
          enumerable: false,
          configurable: false
        });
      }
      on(ev, fn) {
        this.listeners[ev].push(fn);
      }
      removeListener(ev, fn) {
        const list = this.listeners[ev];
        const i = list.indexOf(fn);
        if (i === -1) {
          return;
        }
        if (i === 0 && list.length === 1) {
          list.length = 0;
        } else {
          list.splice(i, 1);
        }
      }
      emit(ev, code, signal) {
        if (this.emitted[ev]) {
          return false;
        }
        this.emitted[ev] = true;
        let ret = false;
        for (const fn of this.listeners[ev]) {
          ret = fn(code, signal) === true || ret;
        }
        if (ev === "exit") {
          ret = this.emit("afterExit", code, signal) || ret;
        }
        return ret;
      }
    };
    SignalExitBase = class {
    };
    signalExitWrap = (handler) => {
      return {
        onExit(cb, opts) {
          return handler.onExit(cb, opts);
        },
        load() {
          return handler.load();
        },
        unload() {
          return handler.unload();
        }
      };
    };
    SignalExitFallback = class extends SignalExitBase {
      onExit() {
        return () => {
        };
      }
      load() {
      }
      unload() {
      }
    };
    SignalExit = class extends SignalExitBase {
      // "SIGHUP" throws an `ENOSYS` error on Windows,
      // so use a supported signal instead
      /* c8 ignore start */
      #hupSig = process4.platform === "win32" ? "SIGINT" : "SIGHUP";
      /* c8 ignore stop */
      #emitter = new Emitter();
      #process;
      #originalProcessEmit;
      #originalProcessReallyExit;
      #sigListeners = {};
      #loaded = false;
      constructor(process7) {
        super();
        this.#process = process7;
        this.#sigListeners = {};
        for (const sig of signals) {
          this.#sigListeners[sig] = () => {
            const listeners = this.#process.listeners(sig);
            let { count } = this.#emitter;
            const p = process7;
            if (typeof p.__signal_exit_emitter__ === "object" && typeof p.__signal_exit_emitter__.count === "number") {
              count += p.__signal_exit_emitter__.count;
            }
            if (listeners.length === count) {
              this.unload();
              const ret = this.#emitter.emit("exit", null, sig);
              const s = sig === "SIGHUP" ? this.#hupSig : sig;
              if (!ret)
                process7.kill(process7.pid, s);
            }
          };
        }
        this.#originalProcessReallyExit = process7.reallyExit;
        this.#originalProcessEmit = process7.emit;
      }
      onExit(cb, opts) {
        if (!processOk(this.#process)) {
          return () => {
          };
        }
        if (this.#loaded === false) {
          this.load();
        }
        const ev = opts?.alwaysLast ? "afterExit" : "exit";
        this.#emitter.on(ev, cb);
        return () => {
          this.#emitter.removeListener(ev, cb);
          if (this.#emitter.listeners["exit"].length === 0 && this.#emitter.listeners["afterExit"].length === 0) {
            this.unload();
          }
        };
      }
      load() {
        if (this.#loaded) {
          return;
        }
        this.#loaded = true;
        this.#emitter.count += 1;
        for (const sig of signals) {
          try {
            const fn = this.#sigListeners[sig];
            if (fn)
              this.#process.on(sig, fn);
          } catch (_) {
          }
        }
        this.#process.emit = (ev, ...a) => {
          return this.#processEmit(ev, ...a);
        };
        this.#process.reallyExit = (code) => {
          return this.#processReallyExit(code);
        };
      }
      unload() {
        if (!this.#loaded) {
          return;
        }
        this.#loaded = false;
        signals.forEach((sig) => {
          const listener = this.#sigListeners[sig];
          if (!listener) {
            throw new Error("Listener not defined for signal: " + sig);
          }
          try {
            this.#process.removeListener(sig, listener);
          } catch (_) {
          }
        });
        this.#process.emit = this.#originalProcessEmit;
        this.#process.reallyExit = this.#originalProcessReallyExit;
        this.#emitter.count -= 1;
      }
      #processReallyExit(code) {
        if (!processOk(this.#process)) {
          return 0;
        }
        this.#process.exitCode = code || 0;
        this.#emitter.emit("exit", this.#process.exitCode, null);
        return this.#originalProcessReallyExit.call(this.#process, this.#process.exitCode);
      }
      #processEmit(ev, ...args) {
        const og = this.#originalProcessEmit;
        if (ev === "exit" && processOk(this.#process)) {
          if (typeof args[0] === "number") {
            this.#process.exitCode = args[0];
          }
          const ret = og.call(this.#process, ev, ...args);
          this.#emitter.emit("exit", this.#process.exitCode, null);
          return ret;
        } else {
          return og.call(this.#process, ev, ...args);
        }
      }
    };
    process4 = globalThis.process;
    ({
      onExit: (
        /**
         * Called when the process is exiting, whether via signal, explicit
         * exit, or running out of stuff to do.
         *
         * If the global process object is not suitable for instrumentation,
         * then this will be a no-op.
         *
         * Returns a function that may be used to unload signal-exit.
         */
        onExit
      ),
      load: (
        /**
         * Load the listeners.  Likely you never need to call this, unless
         * doing a rather deep integration with signal-exit functionality.
         * Mostly exposed for the benefit of testing.
         *
         * @internal
         */
        load
      ),
      unload: (
        /**
         * Unload the listeners.  Likely you never need to call this, unless
         * doing a rather deep integration with signal-exit functionality.
         * Mostly exposed for the benefit of testing.
         *
         * @internal
         */
        unload
      )
    } = signalExitWrap(processOk(process4) ? new SignalExit(process4) : new SignalExitFallback()));
  }
});

// ../../node_modules/execa/lib/kill.js
import os from "node:os";
var DEFAULT_FORCE_KILL_TIMEOUT, spawnedKill, setKillTimeout, shouldForceKill, isSigterm, getForceKillAfterTimeout, spawnedCancel, timeoutKill, setupTimeout, validateTimeout, setExitHandler;
var init_kill = __esm({
  "../../node_modules/execa/lib/kill.js"() {
    init_mjs();
    DEFAULT_FORCE_KILL_TIMEOUT = 1e3 * 5;
    spawnedKill = (kill, signal = "SIGTERM", options = {}) => {
      const killResult = kill(signal);
      setKillTimeout(kill, signal, options, killResult);
      return killResult;
    };
    setKillTimeout = (kill, signal, options, killResult) => {
      if (!shouldForceKill(signal, options, killResult)) {
        return;
      }
      const timeout = getForceKillAfterTimeout(options);
      const t = setTimeout(() => {
        kill("SIGKILL");
      }, timeout);
      if (t.unref) {
        t.unref();
      }
    };
    shouldForceKill = (signal, { forceKillAfterTimeout }, killResult) => isSigterm(signal) && forceKillAfterTimeout !== false && killResult;
    isSigterm = (signal) => signal === os.constants.signals.SIGTERM || typeof signal === "string" && signal.toUpperCase() === "SIGTERM";
    getForceKillAfterTimeout = ({ forceKillAfterTimeout = true }) => {
      if (forceKillAfterTimeout === true) {
        return DEFAULT_FORCE_KILL_TIMEOUT;
      }
      if (!Number.isFinite(forceKillAfterTimeout) || forceKillAfterTimeout < 0) {
        throw new TypeError(`Expected the \`forceKillAfterTimeout\` option to be a non-negative integer, got \`${forceKillAfterTimeout}\` (${typeof forceKillAfterTimeout})`);
      }
      return forceKillAfterTimeout;
    };
    spawnedCancel = (spawned, context) => {
      const killResult = spawned.kill();
      if (killResult) {
        context.isCanceled = true;
      }
    };
    timeoutKill = (spawned, signal, reject) => {
      spawned.kill(signal);
      reject(Object.assign(new Error("Timed out"), { timedOut: true, signal }));
    };
    setupTimeout = (spawned, { timeout, killSignal = "SIGTERM" }, spawnedPromise) => {
      if (timeout === 0 || timeout === void 0) {
        return spawnedPromise;
      }
      let timeoutId;
      const timeoutPromise = new Promise((resolve, reject) => {
        timeoutId = setTimeout(() => {
          timeoutKill(spawned, killSignal, reject);
        }, timeout);
      });
      const safeSpawnedPromise = spawnedPromise.finally(() => {
        clearTimeout(timeoutId);
      });
      return Promise.race([timeoutPromise, safeSpawnedPromise]);
    };
    validateTimeout = ({ timeout }) => {
      if (timeout !== void 0 && (!Number.isFinite(timeout) || timeout < 0)) {
        throw new TypeError(`Expected the \`timeout\` option to be a non-negative integer, got \`${timeout}\` (${typeof timeout})`);
      }
    };
    setExitHandler = async (spawned, { cleanup, detached }, timedPromise) => {
      if (!cleanup || detached) {
        return timedPromise;
      }
      const removeExitHandler = onExit(() => {
        spawned.kill();
      });
      return timedPromise.finally(() => {
        removeExitHandler();
      });
    };
  }
});

// ../../node_modules/is-stream/index.js
function isStream(stream) {
  return stream !== null && typeof stream === "object" && typeof stream.pipe === "function";
}
function isWritableStream(stream) {
  return isStream(stream) && stream.writable !== false && typeof stream._write === "function" && typeof stream._writableState === "object";
}
var init_is_stream = __esm({
  "../../node_modules/is-stream/index.js"() {
  }
});

// ../../node_modules/execa/lib/pipe.js
import { createWriteStream } from "node:fs";
import { ChildProcess } from "node:child_process";
var isExecaChildProcess, pipeToTarget, addPipeMethods;
var init_pipe = __esm({
  "../../node_modules/execa/lib/pipe.js"() {
    init_is_stream();
    isExecaChildProcess = (target) => target instanceof ChildProcess && typeof target.then === "function";
    pipeToTarget = (spawned, streamName, target) => {
      if (typeof target === "string") {
        spawned[streamName].pipe(createWriteStream(target));
        return spawned;
      }
      if (isWritableStream(target)) {
        spawned[streamName].pipe(target);
        return spawned;
      }
      if (!isExecaChildProcess(target)) {
        throw new TypeError("The second argument must be a string, a stream or an Execa child process.");
      }
      if (!isWritableStream(target.stdin)) {
        throw new TypeError("The target child process's stdin must be available.");
      }
      spawned[streamName].pipe(target.stdin);
      return target;
    };
    addPipeMethods = (spawned) => {
      if (spawned.stdout !== null) {
        spawned.pipeStdout = pipeToTarget.bind(void 0, spawned, "stdout");
      }
      if (spawned.stderr !== null) {
        spawned.pipeStderr = pipeToTarget.bind(void 0, spawned, "stderr");
      }
      if (spawned.all !== void 0) {
        spawned.pipeAll = pipeToTarget.bind(void 0, spawned, "all");
      }
    };
  }
});

// ../../node_modules/get-stream/source/contents.js
var getStreamContents, appendFinalChunk, appendChunk, addNewChunk, isAsyncIterable, getChunkType, objectToString, MaxBufferError;
var init_contents = __esm({
  "../../node_modules/get-stream/source/contents.js"() {
    getStreamContents = async (stream, { init, convertChunk, getSize, truncateChunk, addChunk, getFinalChunk, finalize }, { maxBuffer = Number.POSITIVE_INFINITY } = {}) => {
      if (!isAsyncIterable(stream)) {
        throw new Error("The first argument must be a Readable, a ReadableStream, or an async iterable.");
      }
      const state = init();
      state.length = 0;
      try {
        for await (const chunk of stream) {
          const chunkType = getChunkType(chunk);
          const convertedChunk = convertChunk[chunkType](chunk, state);
          appendChunk({ convertedChunk, state, getSize, truncateChunk, addChunk, maxBuffer });
        }
        appendFinalChunk({ state, convertChunk, getSize, truncateChunk, addChunk, getFinalChunk, maxBuffer });
        return finalize(state);
      } catch (error) {
        error.bufferedData = finalize(state);
        throw error;
      }
    };
    appendFinalChunk = ({ state, getSize, truncateChunk, addChunk, getFinalChunk, maxBuffer }) => {
      const convertedChunk = getFinalChunk(state);
      if (convertedChunk !== void 0) {
        appendChunk({ convertedChunk, state, getSize, truncateChunk, addChunk, maxBuffer });
      }
    };
    appendChunk = ({ convertedChunk, state, getSize, truncateChunk, addChunk, maxBuffer }) => {
      const chunkSize = getSize(convertedChunk);
      const newLength = state.length + chunkSize;
      if (newLength <= maxBuffer) {
        addNewChunk(convertedChunk, state, addChunk, newLength);
        return;
      }
      const truncatedChunk = truncateChunk(convertedChunk, maxBuffer - state.length);
      if (truncatedChunk !== void 0) {
        addNewChunk(truncatedChunk, state, addChunk, maxBuffer);
      }
      throw new MaxBufferError();
    };
    addNewChunk = (convertedChunk, state, addChunk, newLength) => {
      state.contents = addChunk(convertedChunk, state, newLength);
      state.length = newLength;
    };
    isAsyncIterable = (stream) => typeof stream === "object" && stream !== null && typeof stream[Symbol.asyncIterator] === "function";
    getChunkType = (chunk) => {
      const typeOfChunk = typeof chunk;
      if (typeOfChunk === "string") {
        return "string";
      }
      if (typeOfChunk !== "object" || chunk === null) {
        return "others";
      }
      if (globalThis.Buffer?.isBuffer(chunk)) {
        return "buffer";
      }
      const prototypeName = objectToString.call(chunk);
      if (prototypeName === "[object ArrayBuffer]") {
        return "arrayBuffer";
      }
      if (prototypeName === "[object DataView]") {
        return "dataView";
      }
      if (Number.isInteger(chunk.byteLength) && Number.isInteger(chunk.byteOffset) && objectToString.call(chunk.buffer) === "[object ArrayBuffer]") {
        return "typedArray";
      }
      return "others";
    };
    ({ toString: objectToString } = Object.prototype);
    MaxBufferError = class extends Error {
      name = "MaxBufferError";
      constructor() {
        super("maxBuffer exceeded");
      }
    };
  }
});

// ../../node_modules/get-stream/source/utils.js
var identity, noop, getContentsProp, throwObjectStream, getLengthProp;
var init_utils = __esm({
  "../../node_modules/get-stream/source/utils.js"() {
    identity = (value) => value;
    noop = () => void 0;
    getContentsProp = ({ contents }) => contents;
    throwObjectStream = (chunk) => {
      throw new Error(`Streams in object mode are not supported: ${String(chunk)}`);
    };
    getLengthProp = (convertedChunk) => convertedChunk.length;
  }
});

// ../../node_modules/get-stream/source/array.js
var init_array = __esm({
  "../../node_modules/get-stream/source/array.js"() {
    init_contents();
    init_utils();
  }
});

// ../../node_modules/get-stream/source/array-buffer.js
async function getStreamAsArrayBuffer(stream, options) {
  return getStreamContents(stream, arrayBufferMethods, options);
}
var initArrayBuffer, useTextEncoder, textEncoder, useUint8Array, useUint8ArrayWithOffset, truncateArrayBufferChunk, addArrayBufferChunk, resizeArrayBufferSlow, resizeArrayBuffer, getNewContentsLength, SCALE_FACTOR, finalizeArrayBuffer, hasArrayBufferResize, arrayBufferMethods;
var init_array_buffer = __esm({
  "../../node_modules/get-stream/source/array-buffer.js"() {
    init_contents();
    init_utils();
    initArrayBuffer = () => ({ contents: new ArrayBuffer(0) });
    useTextEncoder = (chunk) => textEncoder.encode(chunk);
    textEncoder = new TextEncoder();
    useUint8Array = (chunk) => new Uint8Array(chunk);
    useUint8ArrayWithOffset = (chunk) => new Uint8Array(chunk.buffer, chunk.byteOffset, chunk.byteLength);
    truncateArrayBufferChunk = (convertedChunk, chunkSize) => convertedChunk.slice(0, chunkSize);
    addArrayBufferChunk = (convertedChunk, { contents, length: previousLength }, length) => {
      const newContents = hasArrayBufferResize() ? resizeArrayBuffer(contents, length) : resizeArrayBufferSlow(contents, length);
      new Uint8Array(newContents).set(convertedChunk, previousLength);
      return newContents;
    };
    resizeArrayBufferSlow = (contents, length) => {
      if (length <= contents.byteLength) {
        return contents;
      }
      const arrayBuffer = new ArrayBuffer(getNewContentsLength(length));
      new Uint8Array(arrayBuffer).set(new Uint8Array(contents), 0);
      return arrayBuffer;
    };
    resizeArrayBuffer = (contents, length) => {
      if (length <= contents.maxByteLength) {
        contents.resize(length);
        return contents;
      }
      const arrayBuffer = new ArrayBuffer(length, { maxByteLength: getNewContentsLength(length) });
      new Uint8Array(arrayBuffer).set(new Uint8Array(contents), 0);
      return arrayBuffer;
    };
    getNewContentsLength = (length) => SCALE_FACTOR ** Math.ceil(Math.log(length) / Math.log(SCALE_FACTOR));
    SCALE_FACTOR = 2;
    finalizeArrayBuffer = ({ contents, length }) => hasArrayBufferResize() ? contents : contents.slice(0, length);
    hasArrayBufferResize = () => "resize" in ArrayBuffer.prototype;
    arrayBufferMethods = {
      init: initArrayBuffer,
      convertChunk: {
        string: useTextEncoder,
        buffer: useUint8Array,
        arrayBuffer: useUint8Array,
        dataView: useUint8ArrayWithOffset,
        typedArray: useUint8ArrayWithOffset,
        others: throwObjectStream
      },
      getSize: getLengthProp,
      truncateChunk: truncateArrayBufferChunk,
      addChunk: addArrayBufferChunk,
      getFinalChunk: noop,
      finalize: finalizeArrayBuffer
    };
  }
});

// ../../node_modules/get-stream/source/buffer.js
async function getStreamAsBuffer(stream, options) {
  if (!("Buffer" in globalThis)) {
    throw new Error("getStreamAsBuffer() is only supported in Node.js");
  }
  try {
    return arrayBufferToNodeBuffer(await getStreamAsArrayBuffer(stream, options));
  } catch (error) {
    if (error.bufferedData !== void 0) {
      error.bufferedData = arrayBufferToNodeBuffer(error.bufferedData);
    }
    throw error;
  }
}
var arrayBufferToNodeBuffer;
var init_buffer = __esm({
  "../../node_modules/get-stream/source/buffer.js"() {
    init_array_buffer();
    arrayBufferToNodeBuffer = (arrayBuffer) => globalThis.Buffer.from(arrayBuffer);
  }
});

// ../../node_modules/get-stream/source/string.js
async function getStreamAsString(stream, options) {
  return getStreamContents(stream, stringMethods, options);
}
var initString, useTextDecoder, addStringChunk, truncateStringChunk, getFinalStringChunk, stringMethods;
var init_string = __esm({
  "../../node_modules/get-stream/source/string.js"() {
    init_contents();
    init_utils();
    initString = () => ({ contents: "", textDecoder: new TextDecoder() });
    useTextDecoder = (chunk, { textDecoder }) => textDecoder.decode(chunk, { stream: true });
    addStringChunk = (convertedChunk, { contents }) => contents + convertedChunk;
    truncateStringChunk = (convertedChunk, chunkSize) => convertedChunk.slice(0, chunkSize);
    getFinalStringChunk = ({ textDecoder }) => {
      const finalChunk = textDecoder.decode();
      return finalChunk === "" ? void 0 : finalChunk;
    };
    stringMethods = {
      init: initString,
      convertChunk: {
        string: identity,
        buffer: useTextDecoder,
        arrayBuffer: useTextDecoder,
        dataView: useTextDecoder,
        typedArray: useTextDecoder,
        others: throwObjectStream
      },
      getSize: getLengthProp,
      truncateChunk: truncateStringChunk,
      addChunk: addStringChunk,
      getFinalChunk: getFinalStringChunk,
      finalize: getContentsProp
    };
  }
});

// ../../node_modules/get-stream/source/index.js
var init_source = __esm({
  "../../node_modules/get-stream/source/index.js"() {
    init_array();
    init_array_buffer();
    init_buffer();
    init_string();
    init_contents();
  }
});

// ../../node_modules/merge-stream/index.js
var require_merge_stream = __commonJS({
  "../../node_modules/merge-stream/index.js"(exports, module) {
    "use strict";
    var { PassThrough } = __require("stream");
    module.exports = function() {
      var sources = [];
      var output = new PassThrough({ objectMode: true });
      output.setMaxListeners(0);
      output.add = add;
      output.isEmpty = isEmpty;
      output.on("unpipe", remove2);
      Array.prototype.slice.call(arguments).forEach(add);
      return output;
      function add(source) {
        if (Array.isArray(source)) {
          source.forEach(add);
          return this;
        }
        sources.push(source);
        source.once("end", remove2.bind(null, source));
        source.once("error", output.emit.bind(output, "error"));
        source.pipe(output, { end: false });
        return this;
      }
      function isEmpty() {
        return sources.length == 0;
      }
      function remove2(source) {
        sources = sources.filter(function(it) {
          return it !== source;
        });
        if (!sources.length && output.readable) {
          output.end();
        }
      }
    };
  }
});

// ../../node_modules/execa/lib/stream.js
import { createReadStream, readFileSync } from "node:fs";
import { setTimeout as setTimeout2 } from "node:timers/promises";
var import_merge_stream, validateInputOptions, getInputSync, handleInputSync, getInput, handleInput, makeAllStream, getBufferedData, getStreamPromise, applyEncoding, getSpawnedResult;
var init_stream = __esm({
  "../../node_modules/execa/lib/stream.js"() {
    init_is_stream();
    init_source();
    import_merge_stream = __toESM(require_merge_stream(), 1);
    validateInputOptions = (input) => {
      if (input !== void 0) {
        throw new TypeError("The `input` and `inputFile` options cannot be both set.");
      }
    };
    getInputSync = ({ input, inputFile }) => {
      if (typeof inputFile !== "string") {
        return input;
      }
      validateInputOptions(input);
      return readFileSync(inputFile);
    };
    handleInputSync = (options) => {
      const input = getInputSync(options);
      if (isStream(input)) {
        throw new TypeError("The `input` option cannot be a stream in sync mode");
      }
      return input;
    };
    getInput = ({ input, inputFile }) => {
      if (typeof inputFile !== "string") {
        return input;
      }
      validateInputOptions(input);
      return createReadStream(inputFile);
    };
    handleInput = (spawned, options) => {
      const input = getInput(options);
      if (input === void 0) {
        return;
      }
      if (isStream(input)) {
        input.pipe(spawned.stdin);
      } else {
        spawned.stdin.end(input);
      }
    };
    makeAllStream = (spawned, { all }) => {
      if (!all || !spawned.stdout && !spawned.stderr) {
        return;
      }
      const mixed = (0, import_merge_stream.default)();
      if (spawned.stdout) {
        mixed.add(spawned.stdout);
      }
      if (spawned.stderr) {
        mixed.add(spawned.stderr);
      }
      return mixed;
    };
    getBufferedData = async (stream, streamPromise) => {
      if (!stream || streamPromise === void 0) {
        return;
      }
      await setTimeout2(0);
      stream.destroy();
      try {
        return await streamPromise;
      } catch (error) {
        return error.bufferedData;
      }
    };
    getStreamPromise = (stream, { encoding, buffer, maxBuffer }) => {
      if (!stream || !buffer) {
        return;
      }
      if (encoding === "utf8" || encoding === "utf-8") {
        return getStreamAsString(stream, { maxBuffer });
      }
      if (encoding === null || encoding === "buffer") {
        return getStreamAsBuffer(stream, { maxBuffer });
      }
      return applyEncoding(stream, maxBuffer, encoding);
    };
    applyEncoding = async (stream, maxBuffer, encoding) => {
      const buffer = await getStreamAsBuffer(stream, { maxBuffer });
      return buffer.toString(encoding);
    };
    getSpawnedResult = async ({ stdout, stderr, all }, { encoding, buffer, maxBuffer }, processDone) => {
      const stdoutPromise = getStreamPromise(stdout, { encoding, buffer, maxBuffer });
      const stderrPromise = getStreamPromise(stderr, { encoding, buffer, maxBuffer });
      const allPromise = getStreamPromise(all, { encoding, buffer, maxBuffer: maxBuffer * 2 });
      try {
        return await Promise.all([processDone, stdoutPromise, stderrPromise, allPromise]);
      } catch (error) {
        return Promise.all([
          { error, signal: error.signal, timedOut: error.timedOut },
          getBufferedData(stdout, stdoutPromise),
          getBufferedData(stderr, stderrPromise),
          getBufferedData(all, allPromise)
        ]);
      }
    };
  }
});

// ../../node_modules/execa/lib/promise.js
var nativePromisePrototype, descriptors, mergePromise, getSpawnedPromise;
var init_promise = __esm({
  "../../node_modules/execa/lib/promise.js"() {
    nativePromisePrototype = (async () => {
    })().constructor.prototype;
    descriptors = ["then", "catch", "finally"].map((property) => [
      property,
      Reflect.getOwnPropertyDescriptor(nativePromisePrototype, property)
    ]);
    mergePromise = (spawned, promise) => {
      for (const [property, descriptor] of descriptors) {
        const value = typeof promise === "function" ? (...args) => Reflect.apply(descriptor.value, promise(), args) : descriptor.value.bind(promise);
        Reflect.defineProperty(spawned, property, { ...descriptor, value });
      }
    };
    getSpawnedPromise = (spawned) => new Promise((resolve, reject) => {
      spawned.on("exit", (exitCode, signal) => {
        resolve({ exitCode, signal });
      });
      spawned.on("error", (error) => {
        reject(error);
      });
      if (spawned.stdin) {
        spawned.stdin.on("error", (error) => {
          reject(error);
        });
      }
    });
  }
});

// ../../node_modules/execa/lib/command.js
import { Buffer as Buffer2 } from "node:buffer";
import { ChildProcess as ChildProcess2 } from "node:child_process";
var normalizeArgs, NO_ESCAPE_REGEXP, escapeArg, joinCommand, getEscapedCommand, SPACES_REGEXP, parseCommand, parseExpression, concatTokens, parseTemplate, parseTemplates;
var init_command = __esm({
  "../../node_modules/execa/lib/command.js"() {
    normalizeArgs = (file, args = []) => {
      if (!Array.isArray(args)) {
        return [file];
      }
      return [file, ...args];
    };
    NO_ESCAPE_REGEXP = /^[\w.-]+$/;
    escapeArg = (arg) => {
      if (typeof arg !== "string" || NO_ESCAPE_REGEXP.test(arg)) {
        return arg;
      }
      return `"${arg.replaceAll('"', '\\"')}"`;
    };
    joinCommand = (file, args) => normalizeArgs(file, args).join(" ");
    getEscapedCommand = (file, args) => normalizeArgs(file, args).map((arg) => escapeArg(arg)).join(" ");
    SPACES_REGEXP = / +/g;
    parseCommand = (command) => {
      const tokens = [];
      for (const token of command.trim().split(SPACES_REGEXP)) {
        const previousToken = tokens.at(-1);
        if (previousToken && previousToken.endsWith("\\")) {
          tokens[tokens.length - 1] = `${previousToken.slice(0, -1)} ${token}`;
        } else {
          tokens.push(token);
        }
      }
      return tokens;
    };
    parseExpression = (expression) => {
      const typeOfExpression = typeof expression;
      if (typeOfExpression === "string") {
        return expression;
      }
      if (typeOfExpression === "number") {
        return String(expression);
      }
      if (typeOfExpression === "object" && expression !== null && !(expression instanceof ChildProcess2) && "stdout" in expression) {
        const typeOfStdout = typeof expression.stdout;
        if (typeOfStdout === "string") {
          return expression.stdout;
        }
        if (Buffer2.isBuffer(expression.stdout)) {
          return expression.stdout.toString();
        }
        throw new TypeError(`Unexpected "${typeOfStdout}" stdout in template expression`);
      }
      throw new TypeError(`Unexpected "${typeOfExpression}" in template expression`);
    };
    concatTokens = (tokens, nextTokens, isNew) => isNew || tokens.length === 0 || nextTokens.length === 0 ? [...tokens, ...nextTokens] : [
      ...tokens.slice(0, -1),
      `${tokens.at(-1)}${nextTokens[0]}`,
      ...nextTokens.slice(1)
    ];
    parseTemplate = ({ templates, expressions, tokens, index, template }) => {
      const templateString = template ?? templates.raw[index];
      const templateTokens = templateString.split(SPACES_REGEXP).filter(Boolean);
      const newTokens = concatTokens(
        tokens,
        templateTokens,
        templateString.startsWith(" ")
      );
      if (index === expressions.length) {
        return newTokens;
      }
      const expression = expressions[index];
      const expressionTokens = Array.isArray(expression) ? expression.map((expression2) => parseExpression(expression2)) : [parseExpression(expression)];
      return concatTokens(
        newTokens,
        expressionTokens,
        templateString.endsWith(" ")
      );
    };
    parseTemplates = (templates, expressions) => {
      let tokens = [];
      for (const [index, template] of templates.entries()) {
        tokens = parseTemplate({ templates, expressions, tokens, index, template });
      }
      return tokens;
    };
  }
});

// ../../node_modules/execa/lib/verbose.js
import { debuglog } from "node:util";
import process5 from "node:process";
var verboseDefault, padField, getTimestamp, logCommand;
var init_verbose = __esm({
  "../../node_modules/execa/lib/verbose.js"() {
    verboseDefault = debuglog("execa").enabled;
    padField = (field, padding) => String(field).padStart(padding, "0");
    getTimestamp = () => {
      const date = /* @__PURE__ */ new Date();
      return `${padField(date.getHours(), 2)}:${padField(date.getMinutes(), 2)}:${padField(date.getSeconds(), 2)}.${padField(date.getMilliseconds(), 3)}`;
    };
    logCommand = (escapedCommand, { verbose }) => {
      if (!verbose) {
        return;
      }
      process5.stderr.write(`[${getTimestamp()}] ${escapedCommand}
`);
    };
  }
});

// ../../node_modules/execa/index.js
var execa_exports = {};
__export(execa_exports, {
  $: () => $,
  execa: () => execa,
  execaCommand: () => execaCommand,
  execaCommandSync: () => execaCommandSync,
  execaNode: () => execaNode,
  execaSync: () => execaSync
});
import { Buffer as Buffer3 } from "node:buffer";
import path2 from "node:path";
import childProcess from "node:child_process";
import process6 from "node:process";
function execa(file, args, options) {
  const parsed = handleArguments(file, args, options);
  const command = joinCommand(file, args);
  const escapedCommand = getEscapedCommand(file, args);
  logCommand(escapedCommand, parsed.options);
  validateTimeout(parsed.options);
  let spawned;
  try {
    spawned = childProcess.spawn(parsed.file, parsed.args, parsed.options);
  } catch (error) {
    const dummySpawned = new childProcess.ChildProcess();
    const errorPromise = Promise.reject(makeError({
      error,
      stdout: "",
      stderr: "",
      all: "",
      command,
      escapedCommand,
      parsed,
      timedOut: false,
      isCanceled: false,
      killed: false
    }));
    mergePromise(dummySpawned, errorPromise);
    return dummySpawned;
  }
  const spawnedPromise = getSpawnedPromise(spawned);
  const timedPromise = setupTimeout(spawned, parsed.options, spawnedPromise);
  const processDone = setExitHandler(spawned, parsed.options, timedPromise);
  const context = { isCanceled: false };
  spawned.kill = spawnedKill.bind(null, spawned.kill.bind(spawned));
  spawned.cancel = spawnedCancel.bind(null, spawned, context);
  const handlePromise = async () => {
    const [{ error, exitCode, signal, timedOut }, stdoutResult, stderrResult, allResult] = await getSpawnedResult(spawned, parsed.options, processDone);
    const stdout = handleOutput(parsed.options, stdoutResult);
    const stderr = handleOutput(parsed.options, stderrResult);
    const all = handleOutput(parsed.options, allResult);
    if (error || exitCode !== 0 || signal !== null) {
      const returnedError = makeError({
        error,
        exitCode,
        signal,
        stdout,
        stderr,
        all,
        command,
        escapedCommand,
        parsed,
        timedOut,
        isCanceled: context.isCanceled || (parsed.options.signal ? parsed.options.signal.aborted : false),
        killed: spawned.killed
      });
      if (!parsed.options.reject) {
        return returnedError;
      }
      throw returnedError;
    }
    return {
      command,
      escapedCommand,
      exitCode: 0,
      stdout,
      stderr,
      all,
      failed: false,
      timedOut: false,
      isCanceled: false,
      killed: false
    };
  };
  const handlePromiseOnce = onetime_default(handlePromise);
  handleInput(spawned, parsed.options);
  spawned.all = makeAllStream(spawned, parsed.options);
  addPipeMethods(spawned);
  mergePromise(spawned, handlePromiseOnce);
  return spawned;
}
function execaSync(file, args, options) {
  const parsed = handleArguments(file, args, options);
  const command = joinCommand(file, args);
  const escapedCommand = getEscapedCommand(file, args);
  logCommand(escapedCommand, parsed.options);
  const input = handleInputSync(parsed.options);
  let result;
  try {
    result = childProcess.spawnSync(parsed.file, parsed.args, { ...parsed.options, input });
  } catch (error) {
    throw makeError({
      error,
      stdout: "",
      stderr: "",
      all: "",
      command,
      escapedCommand,
      parsed,
      timedOut: false,
      isCanceled: false,
      killed: false
    });
  }
  const stdout = handleOutput(parsed.options, result.stdout, result.error);
  const stderr = handleOutput(parsed.options, result.stderr, result.error);
  if (result.error || result.status !== 0 || result.signal !== null) {
    const error = makeError({
      stdout,
      stderr,
      error: result.error,
      signal: result.signal,
      exitCode: result.status,
      command,
      escapedCommand,
      parsed,
      timedOut: result.error && result.error.code === "ETIMEDOUT",
      isCanceled: false,
      killed: result.signal !== null
    });
    if (!parsed.options.reject) {
      return error;
    }
    throw error;
  }
  return {
    command,
    escapedCommand,
    exitCode: 0,
    stdout,
    stderr,
    failed: false,
    timedOut: false,
    isCanceled: false,
    killed: false
  };
}
function create$(options) {
  function $2(templatesOrOptions, ...expressions) {
    if (!Array.isArray(templatesOrOptions)) {
      return create$({ ...options, ...templatesOrOptions });
    }
    const [file, ...args] = parseTemplates(templatesOrOptions, expressions);
    return execa(file, args, normalizeScriptOptions(options));
  }
  $2.sync = (templates, ...expressions) => {
    if (!Array.isArray(templates)) {
      throw new TypeError("Please use $(options).sync`command` instead of $.sync(options)`command`.");
    }
    const [file, ...args] = parseTemplates(templates, expressions);
    return execaSync(file, args, normalizeScriptOptions(options));
  };
  return $2;
}
function execaCommand(command, options) {
  const [file, ...args] = parseCommand(command);
  return execa(file, args, options);
}
function execaCommandSync(command, options) {
  const [file, ...args] = parseCommand(command);
  return execaSync(file, args, options);
}
function execaNode(scriptPath, args, options = {}) {
  if (args && !Array.isArray(args) && typeof args === "object") {
    options = args;
    args = [];
  }
  const stdio = normalizeStdioNode(options);
  const defaultExecArgv = process6.execArgv.filter((arg) => !arg.startsWith("--inspect"));
  const {
    nodePath = process6.execPath,
    nodeOptions = defaultExecArgv
  } = options;
  return execa(
    nodePath,
    [
      ...nodeOptions,
      scriptPath,
      ...Array.isArray(args) ? args : []
    ],
    {
      ...options,
      stdin: void 0,
      stdout: void 0,
      stderr: void 0,
      stdio,
      shell: false
    }
  );
}
var import_cross_spawn, DEFAULT_MAX_BUFFER, getEnv, handleArguments, handleOutput, normalizeScriptStdin, normalizeScriptOptions, $;
var init_execa = __esm({
  "../../node_modules/execa/index.js"() {
    import_cross_spawn = __toESM(require_cross_spawn(), 1);
    init_strip_final_newline();
    init_npm_run_path();
    init_onetime();
    init_error();
    init_stdio();
    init_kill();
    init_pipe();
    init_stream();
    init_promise();
    init_command();
    init_verbose();
    DEFAULT_MAX_BUFFER = 1e3 * 1e3 * 100;
    getEnv = ({ env: envOption, extendEnv, preferLocal, localDir, execPath }) => {
      const env = extendEnv ? { ...process6.env, ...envOption } : envOption;
      if (preferLocal) {
        return npmRunPathEnv({ env, cwd: localDir, execPath });
      }
      return env;
    };
    handleArguments = (file, args, options = {}) => {
      const parsed = import_cross_spawn.default._parse(file, args, options);
      file = parsed.command;
      args = parsed.args;
      options = parsed.options;
      options = {
        maxBuffer: DEFAULT_MAX_BUFFER,
        buffer: true,
        stripFinalNewline: true,
        extendEnv: true,
        preferLocal: false,
        localDir: options.cwd || process6.cwd(),
        execPath: process6.execPath,
        encoding: "utf8",
        reject: true,
        cleanup: true,
        all: false,
        windowsHide: true,
        verbose: verboseDefault,
        ...options
      };
      options.env = getEnv(options);
      options.stdio = normalizeStdio(options);
      if (process6.platform === "win32" && path2.basename(file, ".exe") === "cmd") {
        args.unshift("/q");
      }
      return { file, args, options, parsed };
    };
    handleOutput = (options, value, error) => {
      if (typeof value !== "string" && !Buffer3.isBuffer(value)) {
        return error === void 0 ? void 0 : "";
      }
      if (options.stripFinalNewline) {
        return stripFinalNewline(value);
      }
      return value;
    };
    normalizeScriptStdin = ({ input, inputFile, stdio }) => input === void 0 && inputFile === void 0 && stdio === void 0 ? { stdin: "inherit" } : {};
    normalizeScriptOptions = (options = {}) => ({
      preferLocal: true,
      ...normalizeScriptStdin(options),
      ...options
    });
    $ = create$();
  }
});

// src/utils/package-manager.ts
var package_manager_exports = {};
__export(package_manager_exports, {
  addPackage: () => addPackage,
  detectPackageManager: () => detectPackageManager,
  getAvailablePackageManagers: () => getAvailablePackageManagers,
  getInstalledPackages: () => getInstalledPackages,
  getOutdatedPackages: () => getOutdatedPackages,
  getPackageInfo: () => getPackageInfo,
  getPackageManagerRecommendations: () => getPackageManagerRecommendations,
  installDependencies: () => installDependencies,
  isPackageInstalled: () => isPackageInstalled,
  removePackage: () => removePackage,
  runScript: () => runScript,
  updatePackages: () => updatePackages
});
import { pathExists, readFile } from "fs-extra";
import ora from "ora";
async function detectPackageManager() {
  if (await pathExists("bun.lockb"))
    return "bun";
  if (await pathExists("pnpm-lock.yaml"))
    return "pnpm";
  if (await pathExists("yarn.lock"))
    return "yarn";
  if (await pathExists("package-lock.json"))
    return "npm";
  try {
    const packageJson = JSON.parse(await readFile("package.json", "utf-8"));
    if (packageJson.packageManager) {
      const pm = packageJson.packageManager.split("@")[0];
      if (["npm", "yarn", "pnpm", "bun"].includes(pm)) {
        return pm;
      }
    }
  } catch {
  }
  const available = await getAvailablePackageManagers();
  if (available.includes("bun"))
    return "bun";
  if (available.includes("pnpm"))
    return "pnpm";
  if (available.includes("yarn"))
    return "yarn";
  return "npm";
}
async function getAvailablePackageManagers() {
  const managers = ["npm", "yarn", "pnpm", "bun"];
  const available = [];
  for (const manager of managers) {
    try {
      await execa(manager, ["--version"], { stdio: "pipe" });
      available.push(manager);
    } catch {
    }
  }
  return available;
}
async function installDependencies(projectPath, packageManager = "npm", options = {}) {
  const spinner = ora("Installing dependencies...").start();
  try {
    const args = getInstallArgs(packageManager, options);
    await execa(packageManager, args, {
      cwd: projectPath,
      stdio: "pipe"
    });
    spinner.succeed("Dependencies installed successfully");
  } catch (error) {
    spinner.fail("Failed to install dependencies");
    throw new Error(`Installation failed: ${error.message}`);
  }
}
async function addPackage(packageName, packageManager = "npm", options = {}) {
  const packages = Array.isArray(packageName) ? packageName : [packageName];
  const spinner = ora(`Adding ${packages.join(", ")}...`).start();
  try {
    const args = getAddArgs(packageManager, packages, options);
    await execa(packageManager, args, {
      stdio: "pipe"
    });
    spinner.succeed(`Added ${packages.join(", ")}`);
  } catch (error) {
    spinner.fail(`Failed to add ${packages.join(", ")}`);
    throw new Error(`Add package failed: ${error.message}`);
  }
}
async function removePackage(packageName, packageManager = "npm") {
  const packages = Array.isArray(packageName) ? packageName : [packageName];
  const spinner = ora(`Removing ${packages.join(", ")}...`).start();
  try {
    const args = getRemoveArgs(packageManager, packages);
    await execa(packageManager, args, {
      stdio: "pipe"
    });
    spinner.succeed(`Removed ${packages.join(", ")}`);
  } catch (error) {
    spinner.fail(`Failed to remove ${packages.join(", ")}`);
    throw new Error(`Remove package failed: ${error.message}`);
  }
}
async function updatePackages(packageManager = "npm", packageName) {
  const spinner = ora("Updating packages...").start();
  try {
    const args = getUpdateArgs(packageManager, packageName);
    await execa(packageManager, args, {
      stdio: "pipe"
    });
    spinner.succeed("Packages updated successfully");
  } catch (error) {
    spinner.fail("Failed to update packages");
    throw new Error(`Update failed: ${error.message}`);
  }
}
async function runScript(scriptName, packageManager = "npm", args = []) {
  const runArgs = getRunArgs(packageManager, scriptName, args);
  await execa(packageManager, runArgs, {
    stdio: "inherit"
  });
}
function getInstallArgs(packageManager, options) {
  switch (packageManager) {
    case "npm":
      const npmArgs = ["install"];
      if (options.exact)
        npmArgs.push("--save-exact");
      return npmArgs;
    case "yarn":
      const yarnArgs = ["install"];
      if (options.exact)
        yarnArgs.push("--exact");
      return yarnArgs;
    case "pnpm":
      const pnpmArgs = ["install"];
      if (options.exact)
        pnpmArgs.push("--save-exact");
      return pnpmArgs;
    case "bun":
      const bunArgs = ["install"];
      if (options.exact)
        bunArgs.push("--exact");
      return bunArgs;
    default:
      return ["install"];
  }
}
function getAddArgs(packageManager, packages, options) {
  switch (packageManager) {
    case "npm":
      const npmArgs = ["install", ...packages];
      if (options.dev)
        npmArgs.push("--save-dev");
      if (options.exact)
        npmArgs.push("--save-exact");
      if (options.global)
        npmArgs.push("--global");
      return npmArgs;
    case "yarn":
      const yarnArgs = options.global ? ["global", "add", ...packages] : ["add", ...packages];
      if (options.dev && !options.global)
        yarnArgs.push("--dev");
      if (options.exact)
        yarnArgs.push("--exact");
      return yarnArgs;
    case "pnpm":
      const pnpmArgs = ["add", ...packages];
      if (options.dev)
        pnpmArgs.push("--save-dev");
      if (options.exact)
        pnpmArgs.push("--save-exact");
      if (options.global)
        pnpmArgs.push("--global");
      return pnpmArgs;
    case "bun":
      const bunArgs = ["add", ...packages];
      if (options.dev)
        bunArgs.push("--dev");
      if (options.exact)
        bunArgs.push("--exact");
      if (options.global)
        bunArgs.push("--global");
      return bunArgs;
    default:
      return ["add", ...packages];
  }
}
function getRemoveArgs(packageManager, packages) {
  switch (packageManager) {
    case "npm":
      return ["uninstall", ...packages];
    case "yarn":
      return ["remove", ...packages];
    case "pnpm":
      return ["remove", ...packages];
    case "bun":
      return ["remove", ...packages];
    default:
      return ["remove", ...packages];
  }
}
function getUpdateArgs(packageManager, packageName) {
  const packages = packageName ? [packageName] : [];
  switch (packageManager) {
    case "npm":
      return ["update", ...packages];
    case "yarn":
      return ["upgrade", ...packages];
    case "pnpm":
      return ["update", ...packages];
    case "bun":
      return ["update", ...packages];
    default:
      return ["update", ...packages];
  }
}
function getRunArgs(packageManager, scriptName, args) {
  switch (packageManager) {
    case "npm":
      return ["run", scriptName, "--", ...args];
    case "yarn":
      return ["run", scriptName, ...args];
    case "pnpm":
      return ["run", scriptName, ...args];
    case "bun":
      return ["run", scriptName, ...args];
    default:
      return ["run", scriptName, ...args];
  }
}
async function getPackageInfo(packageName) {
  try {
    const result = await execa("npm", ["view", packageName, "--json"], {
      stdio: "pipe"
    });
    return JSON.parse(result.stdout);
  } catch (error) {
    throw new Error(`Failed to get package info: ${error.message}`);
  }
}
async function isPackageInstalled(packageName, global3 = false) {
  try {
    const args = global3 ? ["list", "--global", "--depth=0"] : ["list", "--depth=0"];
    const result = await execa("npm", args, { stdio: "pipe" });
    return result.stdout.includes(packageName);
  } catch {
    return false;
  }
}
async function getInstalledPackages() {
  try {
    const packageJson = JSON.parse(await readFile("package.json", "utf-8"));
    return {
      ...packageJson.dependencies || {},
      ...packageJson.devDependencies || {}
    };
  } catch {
    return {};
  }
}
async function getOutdatedPackages(packageManager = "npm") {
  try {
    let result;
    switch (packageManager) {
      case "npm":
        result = await execa("npm", ["outdated", "--json"], { stdio: "pipe" });
        break;
      case "yarn":
        result = await execa("yarn", ["outdated", "--json"], { stdio: "pipe" });
        break;
      case "pnpm":
        result = await execa("pnpm", ["outdated", "--format", "json"], { stdio: "pipe" });
        break;
      case "bun":
        result = await execa("bun", ["outdated"], { stdio: "pipe" });
        break;
    }
    return JSON.parse(result.stdout);
  } catch {
    return [];
  }
}
function getPackageManagerRecommendations() {
  const recommendations = [];
  recommendations.push("\u{1F680} Bun - Fastest package manager and runtime");
  recommendations.push("\u26A1 pnpm - Fast, disk space efficient");
  recommendations.push("\u{1F9F6} Yarn - Reliable with good workspace support");
  recommendations.push("\u{1F4E6} npm - Default Node.js package manager");
  return recommendations;
}
var init_package_manager = __esm({
  "src/utils/package-manager.ts"() {
    "use strict";
    init_execa();
  }
});

// ../../node_modules/semver/internal/constants.js
var require_constants = __commonJS({
  "../../node_modules/semver/internal/constants.js"(exports, module) {
    "use strict";
    var SEMVER_SPEC_VERSION = "2.0.0";
    var MAX_LENGTH = 256;
    var MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER || /* istanbul ignore next */
    9007199254740991;
    var MAX_SAFE_COMPONENT_LENGTH = 16;
    var MAX_SAFE_BUILD_LENGTH = MAX_LENGTH - 6;
    var RELEASE_TYPES = [
      "major",
      "premajor",
      "minor",
      "preminor",
      "patch",
      "prepatch",
      "prerelease"
    ];
    module.exports = {
      MAX_LENGTH,
      MAX_SAFE_COMPONENT_LENGTH,
      MAX_SAFE_BUILD_LENGTH,
      MAX_SAFE_INTEGER,
      RELEASE_TYPES,
      SEMVER_SPEC_VERSION,
      FLAG_INCLUDE_PRERELEASE: 1,
      FLAG_LOOSE: 2
    };
  }
});

// ../../node_modules/semver/internal/debug.js
var require_debug = __commonJS({
  "../../node_modules/semver/internal/debug.js"(exports, module) {
    "use strict";
    var debug = typeof process === "object" && process.env && process.env.NODE_DEBUG && /\bsemver\b/i.test(process.env.NODE_DEBUG) ? (...args) => console.error("SEMVER", ...args) : () => {
    };
    module.exports = debug;
  }
});

// ../../node_modules/semver/internal/re.js
var require_re = __commonJS({
  "../../node_modules/semver/internal/re.js"(exports, module) {
    "use strict";
    var {
      MAX_SAFE_COMPONENT_LENGTH,
      MAX_SAFE_BUILD_LENGTH,
      MAX_LENGTH
    } = require_constants();
    var debug = require_debug();
    exports = module.exports = {};
    var re = exports.re = [];
    var safeRe = exports.safeRe = [];
    var src = exports.src = [];
    var safeSrc = exports.safeSrc = [];
    var t = exports.t = {};
    var R = 0;
    var LETTERDASHNUMBER = "[a-zA-Z0-9-]";
    var safeRegexReplacements = [
      ["\\s", 1],
      ["\\d", MAX_LENGTH],
      [LETTERDASHNUMBER, MAX_SAFE_BUILD_LENGTH]
    ];
    var makeSafeRegex = (value) => {
      for (const [token, max] of safeRegexReplacements) {
        value = value.split(`${token}*`).join(`${token}{0,${max}}`).split(`${token}+`).join(`${token}{1,${max}}`);
      }
      return value;
    };
    var createToken = (name, value, isGlobal) => {
      const safe = makeSafeRegex(value);
      const index = R++;
      debug(name, index, value);
      t[name] = index;
      src[index] = value;
      safeSrc[index] = safe;
      re[index] = new RegExp(value, isGlobal ? "g" : void 0);
      safeRe[index] = new RegExp(safe, isGlobal ? "g" : void 0);
    };
    createToken("NUMERICIDENTIFIER", "0|[1-9]\\d*");
    createToken("NUMERICIDENTIFIERLOOSE", "\\d+");
    createToken("NONNUMERICIDENTIFIER", `\\d*[a-zA-Z-]${LETTERDASHNUMBER}*`);
    createToken("MAINVERSION", `(${src[t.NUMERICIDENTIFIER]})\\.(${src[t.NUMERICIDENTIFIER]})\\.(${src[t.NUMERICIDENTIFIER]})`);
    createToken("MAINVERSIONLOOSE", `(${src[t.NUMERICIDENTIFIERLOOSE]})\\.(${src[t.NUMERICIDENTIFIERLOOSE]})\\.(${src[t.NUMERICIDENTIFIERLOOSE]})`);
    createToken("PRERELEASEIDENTIFIER", `(?:${src[t.NONNUMERICIDENTIFIER]}|${src[t.NUMERICIDENTIFIER]})`);
    createToken("PRERELEASEIDENTIFIERLOOSE", `(?:${src[t.NONNUMERICIDENTIFIER]}|${src[t.NUMERICIDENTIFIERLOOSE]})`);
    createToken("PRERELEASE", `(?:-(${src[t.PRERELEASEIDENTIFIER]}(?:\\.${src[t.PRERELEASEIDENTIFIER]})*))`);
    createToken("PRERELEASELOOSE", `(?:-?(${src[t.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${src[t.PRERELEASEIDENTIFIERLOOSE]})*))`);
    createToken("BUILDIDENTIFIER", `${LETTERDASHNUMBER}+`);
    createToken("BUILD", `(?:\\+(${src[t.BUILDIDENTIFIER]}(?:\\.${src[t.BUILDIDENTIFIER]})*))`);
    createToken("FULLPLAIN", `v?${src[t.MAINVERSION]}${src[t.PRERELEASE]}?${src[t.BUILD]}?`);
    createToken("FULL", `^${src[t.FULLPLAIN]}$`);
    createToken("LOOSEPLAIN", `[v=\\s]*${src[t.MAINVERSIONLOOSE]}${src[t.PRERELEASELOOSE]}?${src[t.BUILD]}?`);
    createToken("LOOSE", `^${src[t.LOOSEPLAIN]}$`);
    createToken("GTLT", "((?:<|>)?=?)");
    createToken("XRANGEIDENTIFIERLOOSE", `${src[t.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`);
    createToken("XRANGEIDENTIFIER", `${src[t.NUMERICIDENTIFIER]}|x|X|\\*`);
    createToken("XRANGEPLAIN", `[v=\\s]*(${src[t.XRANGEIDENTIFIER]})(?:\\.(${src[t.XRANGEIDENTIFIER]})(?:\\.(${src[t.XRANGEIDENTIFIER]})(?:${src[t.PRERELEASE]})?${src[t.BUILD]}?)?)?`);
    createToken("XRANGEPLAINLOOSE", `[v=\\s]*(${src[t.XRANGEIDENTIFIERLOOSE]})(?:\\.(${src[t.XRANGEIDENTIFIERLOOSE]})(?:\\.(${src[t.XRANGEIDENTIFIERLOOSE]})(?:${src[t.PRERELEASELOOSE]})?${src[t.BUILD]}?)?)?`);
    createToken("XRANGE", `^${src[t.GTLT]}\\s*${src[t.XRANGEPLAIN]}$`);
    createToken("XRANGELOOSE", `^${src[t.GTLT]}\\s*${src[t.XRANGEPLAINLOOSE]}$`);
    createToken("COERCEPLAIN", `${"(^|[^\\d])(\\d{1,"}${MAX_SAFE_COMPONENT_LENGTH}})(?:\\.(\\d{1,${MAX_SAFE_COMPONENT_LENGTH}}))?(?:\\.(\\d{1,${MAX_SAFE_COMPONENT_LENGTH}}))?`);
    createToken("COERCE", `${src[t.COERCEPLAIN]}(?:$|[^\\d])`);
    createToken("COERCEFULL", src[t.COERCEPLAIN] + `(?:${src[t.PRERELEASE]})?(?:${src[t.BUILD]})?(?:$|[^\\d])`);
    createToken("COERCERTL", src[t.COERCE], true);
    createToken("COERCERTLFULL", src[t.COERCEFULL], true);
    createToken("LONETILDE", "(?:~>?)");
    createToken("TILDETRIM", `(\\s*)${src[t.LONETILDE]}\\s+`, true);
    exports.tildeTrimReplace = "$1~";
    createToken("TILDE", `^${src[t.LONETILDE]}${src[t.XRANGEPLAIN]}$`);
    createToken("TILDELOOSE", `^${src[t.LONETILDE]}${src[t.XRANGEPLAINLOOSE]}$`);
    createToken("LONECARET", "(?:\\^)");
    createToken("CARETTRIM", `(\\s*)${src[t.LONECARET]}\\s+`, true);
    exports.caretTrimReplace = "$1^";
    createToken("CARET", `^${src[t.LONECARET]}${src[t.XRANGEPLAIN]}$`);
    createToken("CARETLOOSE", `^${src[t.LONECARET]}${src[t.XRANGEPLAINLOOSE]}$`);
    createToken("COMPARATORLOOSE", `^${src[t.GTLT]}\\s*(${src[t.LOOSEPLAIN]})$|^$`);
    createToken("COMPARATOR", `^${src[t.GTLT]}\\s*(${src[t.FULLPLAIN]})$|^$`);
    createToken("COMPARATORTRIM", `(\\s*)${src[t.GTLT]}\\s*(${src[t.LOOSEPLAIN]}|${src[t.XRANGEPLAIN]})`, true);
    exports.comparatorTrimReplace = "$1$2$3";
    createToken("HYPHENRANGE", `^\\s*(${src[t.XRANGEPLAIN]})\\s+-\\s+(${src[t.XRANGEPLAIN]})\\s*$`);
    createToken("HYPHENRANGELOOSE", `^\\s*(${src[t.XRANGEPLAINLOOSE]})\\s+-\\s+(${src[t.XRANGEPLAINLOOSE]})\\s*$`);
    createToken("STAR", "(<|>)?=?\\s*\\*");
    createToken("GTE0", "^\\s*>=\\s*0\\.0\\.0\\s*$");
    createToken("GTE0PRE", "^\\s*>=\\s*0\\.0\\.0-0\\s*$");
  }
});

// ../../node_modules/semver/internal/parse-options.js
var require_parse_options = __commonJS({
  "../../node_modules/semver/internal/parse-options.js"(exports, module) {
    "use strict";
    var looseOption = Object.freeze({ loose: true });
    var emptyOpts = Object.freeze({});
    var parseOptions = (options) => {
      if (!options) {
        return emptyOpts;
      }
      if (typeof options !== "object") {
        return looseOption;
      }
      return options;
    };
    module.exports = parseOptions;
  }
});

// ../../node_modules/semver/internal/identifiers.js
var require_identifiers = __commonJS({
  "../../node_modules/semver/internal/identifiers.js"(exports, module) {
    "use strict";
    var numeric = /^[0-9]+$/;
    var compareIdentifiers = (a, b) => {
      const anum = numeric.test(a);
      const bnum = numeric.test(b);
      if (anum && bnum) {
        a = +a;
        b = +b;
      }
      return a === b ? 0 : anum && !bnum ? -1 : bnum && !anum ? 1 : a < b ? -1 : 1;
    };
    var rcompareIdentifiers = (a, b) => compareIdentifiers(b, a);
    module.exports = {
      compareIdentifiers,
      rcompareIdentifiers
    };
  }
});

// ../../node_modules/semver/classes/semver.js
var require_semver = __commonJS({
  "../../node_modules/semver/classes/semver.js"(exports, module) {
    "use strict";
    var debug = require_debug();
    var { MAX_LENGTH, MAX_SAFE_INTEGER } = require_constants();
    var { safeRe: re, t } = require_re();
    var parseOptions = require_parse_options();
    var { compareIdentifiers } = require_identifiers();
    var SemVer = class _SemVer {
      constructor(version, options) {
        options = parseOptions(options);
        if (version instanceof _SemVer) {
          if (version.loose === !!options.loose && version.includePrerelease === !!options.includePrerelease) {
            return version;
          } else {
            version = version.version;
          }
        } else if (typeof version !== "string") {
          throw new TypeError(`Invalid version. Must be a string. Got type "${typeof version}".`);
        }
        if (version.length > MAX_LENGTH) {
          throw new TypeError(
            `version is longer than ${MAX_LENGTH} characters`
          );
        }
        debug("SemVer", version, options);
        this.options = options;
        this.loose = !!options.loose;
        this.includePrerelease = !!options.includePrerelease;
        const m = version.trim().match(options.loose ? re[t.LOOSE] : re[t.FULL]);
        if (!m) {
          throw new TypeError(`Invalid Version: ${version}`);
        }
        this.raw = version;
        this.major = +m[1];
        this.minor = +m[2];
        this.patch = +m[3];
        if (this.major > MAX_SAFE_INTEGER || this.major < 0) {
          throw new TypeError("Invalid major version");
        }
        if (this.minor > MAX_SAFE_INTEGER || this.minor < 0) {
          throw new TypeError("Invalid minor version");
        }
        if (this.patch > MAX_SAFE_INTEGER || this.patch < 0) {
          throw new TypeError("Invalid patch version");
        }
        if (!m[4]) {
          this.prerelease = [];
        } else {
          this.prerelease = m[4].split(".").map((id) => {
            if (/^[0-9]+$/.test(id)) {
              const num = +id;
              if (num >= 0 && num < MAX_SAFE_INTEGER) {
                return num;
              }
            }
            return id;
          });
        }
        this.build = m[5] ? m[5].split(".") : [];
        this.format();
      }
      format() {
        this.version = `${this.major}.${this.minor}.${this.patch}`;
        if (this.prerelease.length) {
          this.version += `-${this.prerelease.join(".")}`;
        }
        return this.version;
      }
      toString() {
        return this.version;
      }
      compare(other) {
        debug("SemVer.compare", this.version, this.options, other);
        if (!(other instanceof _SemVer)) {
          if (typeof other === "string" && other === this.version) {
            return 0;
          }
          other = new _SemVer(other, this.options);
        }
        if (other.version === this.version) {
          return 0;
        }
        return this.compareMain(other) || this.comparePre(other);
      }
      compareMain(other) {
        if (!(other instanceof _SemVer)) {
          other = new _SemVer(other, this.options);
        }
        return compareIdentifiers(this.major, other.major) || compareIdentifiers(this.minor, other.minor) || compareIdentifiers(this.patch, other.patch);
      }
      comparePre(other) {
        if (!(other instanceof _SemVer)) {
          other = new _SemVer(other, this.options);
        }
        if (this.prerelease.length && !other.prerelease.length) {
          return -1;
        } else if (!this.prerelease.length && other.prerelease.length) {
          return 1;
        } else if (!this.prerelease.length && !other.prerelease.length) {
          return 0;
        }
        let i = 0;
        do {
          const a = this.prerelease[i];
          const b = other.prerelease[i];
          debug("prerelease compare", i, a, b);
          if (a === void 0 && b === void 0) {
            return 0;
          } else if (b === void 0) {
            return 1;
          } else if (a === void 0) {
            return -1;
          } else if (a === b) {
            continue;
          } else {
            return compareIdentifiers(a, b);
          }
        } while (++i);
      }
      compareBuild(other) {
        if (!(other instanceof _SemVer)) {
          other = new _SemVer(other, this.options);
        }
        let i = 0;
        do {
          const a = this.build[i];
          const b = other.build[i];
          debug("build compare", i, a, b);
          if (a === void 0 && b === void 0) {
            return 0;
          } else if (b === void 0) {
            return 1;
          } else if (a === void 0) {
            return -1;
          } else if (a === b) {
            continue;
          } else {
            return compareIdentifiers(a, b);
          }
        } while (++i);
      }
      // preminor will bump the version up to the next minor release, and immediately
      // down to pre-release. premajor and prepatch work the same way.
      inc(release, identifier, identifierBase) {
        if (release.startsWith("pre")) {
          if (!identifier && identifierBase === false) {
            throw new Error("invalid increment argument: identifier is empty");
          }
          if (identifier) {
            const match = `-${identifier}`.match(this.options.loose ? re[t.PRERELEASELOOSE] : re[t.PRERELEASE]);
            if (!match || match[1] !== identifier) {
              throw new Error(`invalid identifier: ${identifier}`);
            }
          }
        }
        switch (release) {
          case "premajor":
            this.prerelease.length = 0;
            this.patch = 0;
            this.minor = 0;
            this.major++;
            this.inc("pre", identifier, identifierBase);
            break;
          case "preminor":
            this.prerelease.length = 0;
            this.patch = 0;
            this.minor++;
            this.inc("pre", identifier, identifierBase);
            break;
          case "prepatch":
            this.prerelease.length = 0;
            this.inc("patch", identifier, identifierBase);
            this.inc("pre", identifier, identifierBase);
            break;
          case "prerelease":
            if (this.prerelease.length === 0) {
              this.inc("patch", identifier, identifierBase);
            }
            this.inc("pre", identifier, identifierBase);
            break;
          case "release":
            if (this.prerelease.length === 0) {
              throw new Error(`version ${this.raw} is not a prerelease`);
            }
            this.prerelease.length = 0;
            break;
          case "major":
            if (this.minor !== 0 || this.patch !== 0 || this.prerelease.length === 0) {
              this.major++;
            }
            this.minor = 0;
            this.patch = 0;
            this.prerelease = [];
            break;
          case "minor":
            if (this.patch !== 0 || this.prerelease.length === 0) {
              this.minor++;
            }
            this.patch = 0;
            this.prerelease = [];
            break;
          case "patch":
            if (this.prerelease.length === 0) {
              this.patch++;
            }
            this.prerelease = [];
            break;
          case "pre": {
            const base = Number(identifierBase) ? 1 : 0;
            if (this.prerelease.length === 0) {
              this.prerelease = [base];
            } else {
              let i = this.prerelease.length;
              while (--i >= 0) {
                if (typeof this.prerelease[i] === "number") {
                  this.prerelease[i]++;
                  i = -2;
                }
              }
              if (i === -1) {
                if (identifier === this.prerelease.join(".") && identifierBase === false) {
                  throw new Error("invalid increment argument: identifier already exists");
                }
                this.prerelease.push(base);
              }
            }
            if (identifier) {
              let prerelease = [identifier, base];
              if (identifierBase === false) {
                prerelease = [identifier];
              }
              if (compareIdentifiers(this.prerelease[0], identifier) === 0) {
                if (isNaN(this.prerelease[1])) {
                  this.prerelease = prerelease;
                }
              } else {
                this.prerelease = prerelease;
              }
            }
            break;
          }
          default:
            throw new Error(`invalid increment argument: ${release}`);
        }
        this.raw = this.format();
        if (this.build.length) {
          this.raw += `+${this.build.join(".")}`;
        }
        return this;
      }
    };
    module.exports = SemVer;
  }
});

// ../../node_modules/semver/functions/parse.js
var require_parse2 = __commonJS({
  "../../node_modules/semver/functions/parse.js"(exports, module) {
    "use strict";
    var SemVer = require_semver();
    var parse = (version, options, throwErrors = false) => {
      if (version instanceof SemVer) {
        return version;
      }
      try {
        return new SemVer(version, options);
      } catch (er) {
        if (!throwErrors) {
          return null;
        }
        throw er;
      }
    };
    module.exports = parse;
  }
});

// ../../node_modules/semver/functions/valid.js
var require_valid = __commonJS({
  "../../node_modules/semver/functions/valid.js"(exports, module) {
    "use strict";
    var parse = require_parse2();
    var valid = (version, options) => {
      const v = parse(version, options);
      return v ? v.version : null;
    };
    module.exports = valid;
  }
});

// ../../node_modules/semver/functions/clean.js
var require_clean = __commonJS({
  "../../node_modules/semver/functions/clean.js"(exports, module) {
    "use strict";
    var parse = require_parse2();
    var clean = (version, options) => {
      const s = parse(version.trim().replace(/^[=v]+/, ""), options);
      return s ? s.version : null;
    };
    module.exports = clean;
  }
});

// ../../node_modules/semver/functions/inc.js
var require_inc = __commonJS({
  "../../node_modules/semver/functions/inc.js"(exports, module) {
    "use strict";
    var SemVer = require_semver();
    var inc = (version, release, options, identifier, identifierBase) => {
      if (typeof options === "string") {
        identifierBase = identifier;
        identifier = options;
        options = void 0;
      }
      try {
        return new SemVer(
          version instanceof SemVer ? version.version : version,
          options
        ).inc(release, identifier, identifierBase).version;
      } catch (er) {
        return null;
      }
    };
    module.exports = inc;
  }
});

// ../../node_modules/semver/functions/diff.js
var require_diff = __commonJS({
  "../../node_modules/semver/functions/diff.js"(exports, module) {
    "use strict";
    var parse = require_parse2();
    var diff = (version1, version2) => {
      const v1 = parse(version1, null, true);
      const v2 = parse(version2, null, true);
      const comparison = v1.compare(v2);
      if (comparison === 0) {
        return null;
      }
      const v1Higher = comparison > 0;
      const highVersion = v1Higher ? v1 : v2;
      const lowVersion = v1Higher ? v2 : v1;
      const highHasPre = !!highVersion.prerelease.length;
      const lowHasPre = !!lowVersion.prerelease.length;
      if (lowHasPre && !highHasPre) {
        if (!lowVersion.patch && !lowVersion.minor) {
          return "major";
        }
        if (lowVersion.compareMain(highVersion) === 0) {
          if (lowVersion.minor && !lowVersion.patch) {
            return "minor";
          }
          return "patch";
        }
      }
      const prefix = highHasPre ? "pre" : "";
      if (v1.major !== v2.major) {
        return prefix + "major";
      }
      if (v1.minor !== v2.minor) {
        return prefix + "minor";
      }
      if (v1.patch !== v2.patch) {
        return prefix + "patch";
      }
      return "prerelease";
    };
    module.exports = diff;
  }
});

// ../../node_modules/semver/functions/major.js
var require_major = __commonJS({
  "../../node_modules/semver/functions/major.js"(exports, module) {
    "use strict";
    var SemVer = require_semver();
    var major = (a, loose) => new SemVer(a, loose).major;
    module.exports = major;
  }
});

// ../../node_modules/semver/functions/minor.js
var require_minor = __commonJS({
  "../../node_modules/semver/functions/minor.js"(exports, module) {
    "use strict";
    var SemVer = require_semver();
    var minor = (a, loose) => new SemVer(a, loose).minor;
    module.exports = minor;
  }
});

// ../../node_modules/semver/functions/patch.js
var require_patch = __commonJS({
  "../../node_modules/semver/functions/patch.js"(exports, module) {
    "use strict";
    var SemVer = require_semver();
    var patch = (a, loose) => new SemVer(a, loose).patch;
    module.exports = patch;
  }
});

// ../../node_modules/semver/functions/prerelease.js
var require_prerelease = __commonJS({
  "../../node_modules/semver/functions/prerelease.js"(exports, module) {
    "use strict";
    var parse = require_parse2();
    var prerelease = (version, options) => {
      const parsed = parse(version, options);
      return parsed && parsed.prerelease.length ? parsed.prerelease : null;
    };
    module.exports = prerelease;
  }
});

// ../../node_modules/semver/functions/compare.js
var require_compare = __commonJS({
  "../../node_modules/semver/functions/compare.js"(exports, module) {
    "use strict";
    var SemVer = require_semver();
    var compare = (a, b, loose) => new SemVer(a, loose).compare(new SemVer(b, loose));
    module.exports = compare;
  }
});

// ../../node_modules/semver/functions/rcompare.js
var require_rcompare = __commonJS({
  "../../node_modules/semver/functions/rcompare.js"(exports, module) {
    "use strict";
    var compare = require_compare();
    var rcompare = (a, b, loose) => compare(b, a, loose);
    module.exports = rcompare;
  }
});

// ../../node_modules/semver/functions/compare-loose.js
var require_compare_loose = __commonJS({
  "../../node_modules/semver/functions/compare-loose.js"(exports, module) {
    "use strict";
    var compare = require_compare();
    var compareLoose = (a, b) => compare(a, b, true);
    module.exports = compareLoose;
  }
});

// ../../node_modules/semver/functions/compare-build.js
var require_compare_build = __commonJS({
  "../../node_modules/semver/functions/compare-build.js"(exports, module) {
    "use strict";
    var SemVer = require_semver();
    var compareBuild = (a, b, loose) => {
      const versionA = new SemVer(a, loose);
      const versionB = new SemVer(b, loose);
      return versionA.compare(versionB) || versionA.compareBuild(versionB);
    };
    module.exports = compareBuild;
  }
});

// ../../node_modules/semver/functions/sort.js
var require_sort = __commonJS({
  "../../node_modules/semver/functions/sort.js"(exports, module) {
    "use strict";
    var compareBuild = require_compare_build();
    var sort = (list, loose) => list.sort((a, b) => compareBuild(a, b, loose));
    module.exports = sort;
  }
});

// ../../node_modules/semver/functions/rsort.js
var require_rsort = __commonJS({
  "../../node_modules/semver/functions/rsort.js"(exports, module) {
    "use strict";
    var compareBuild = require_compare_build();
    var rsort = (list, loose) => list.sort((a, b) => compareBuild(b, a, loose));
    module.exports = rsort;
  }
});

// ../../node_modules/semver/functions/gt.js
var require_gt = __commonJS({
  "../../node_modules/semver/functions/gt.js"(exports, module) {
    "use strict";
    var compare = require_compare();
    var gt = (a, b, loose) => compare(a, b, loose) > 0;
    module.exports = gt;
  }
});

// ../../node_modules/semver/functions/lt.js
var require_lt = __commonJS({
  "../../node_modules/semver/functions/lt.js"(exports, module) {
    "use strict";
    var compare = require_compare();
    var lt = (a, b, loose) => compare(a, b, loose) < 0;
    module.exports = lt;
  }
});

// ../../node_modules/semver/functions/eq.js
var require_eq = __commonJS({
  "../../node_modules/semver/functions/eq.js"(exports, module) {
    "use strict";
    var compare = require_compare();
    var eq = (a, b, loose) => compare(a, b, loose) === 0;
    module.exports = eq;
  }
});

// ../../node_modules/semver/functions/neq.js
var require_neq = __commonJS({
  "../../node_modules/semver/functions/neq.js"(exports, module) {
    "use strict";
    var compare = require_compare();
    var neq = (a, b, loose) => compare(a, b, loose) !== 0;
    module.exports = neq;
  }
});

// ../../node_modules/semver/functions/gte.js
var require_gte = __commonJS({
  "../../node_modules/semver/functions/gte.js"(exports, module) {
    "use strict";
    var compare = require_compare();
    var gte = (a, b, loose) => compare(a, b, loose) >= 0;
    module.exports = gte;
  }
});

// ../../node_modules/semver/functions/lte.js
var require_lte = __commonJS({
  "../../node_modules/semver/functions/lte.js"(exports, module) {
    "use strict";
    var compare = require_compare();
    var lte = (a, b, loose) => compare(a, b, loose) <= 0;
    module.exports = lte;
  }
});

// ../../node_modules/semver/functions/cmp.js
var require_cmp = __commonJS({
  "../../node_modules/semver/functions/cmp.js"(exports, module) {
    "use strict";
    var eq = require_eq();
    var neq = require_neq();
    var gt = require_gt();
    var gte = require_gte();
    var lt = require_lt();
    var lte = require_lte();
    var cmp = (a, op, b, loose) => {
      switch (op) {
        case "===":
          if (typeof a === "object") {
            a = a.version;
          }
          if (typeof b === "object") {
            b = b.version;
          }
          return a === b;
        case "!==":
          if (typeof a === "object") {
            a = a.version;
          }
          if (typeof b === "object") {
            b = b.version;
          }
          return a !== b;
        case "":
        case "=":
        case "==":
          return eq(a, b, loose);
        case "!=":
          return neq(a, b, loose);
        case ">":
          return gt(a, b, loose);
        case ">=":
          return gte(a, b, loose);
        case "<":
          return lt(a, b, loose);
        case "<=":
          return lte(a, b, loose);
        default:
          throw new TypeError(`Invalid operator: ${op}`);
      }
    };
    module.exports = cmp;
  }
});

// ../../node_modules/semver/functions/coerce.js
var require_coerce = __commonJS({
  "../../node_modules/semver/functions/coerce.js"(exports, module) {
    "use strict";
    var SemVer = require_semver();
    var parse = require_parse2();
    var { safeRe: re, t } = require_re();
    var coerce = (version, options) => {
      if (version instanceof SemVer) {
        return version;
      }
      if (typeof version === "number") {
        version = String(version);
      }
      if (typeof version !== "string") {
        return null;
      }
      options = options || {};
      let match = null;
      if (!options.rtl) {
        match = version.match(options.includePrerelease ? re[t.COERCEFULL] : re[t.COERCE]);
      } else {
        const coerceRtlRegex = options.includePrerelease ? re[t.COERCERTLFULL] : re[t.COERCERTL];
        let next;
        while ((next = coerceRtlRegex.exec(version)) && (!match || match.index + match[0].length !== version.length)) {
          if (!match || next.index + next[0].length !== match.index + match[0].length) {
            match = next;
          }
          coerceRtlRegex.lastIndex = next.index + next[1].length + next[2].length;
        }
        coerceRtlRegex.lastIndex = -1;
      }
      if (match === null) {
        return null;
      }
      const major = match[2];
      const minor = match[3] || "0";
      const patch = match[4] || "0";
      const prerelease = options.includePrerelease && match[5] ? `-${match[5]}` : "";
      const build = options.includePrerelease && match[6] ? `+${match[6]}` : "";
      return parse(`${major}.${minor}.${patch}${prerelease}${build}`, options);
    };
    module.exports = coerce;
  }
});

// ../../node_modules/semver/internal/lrucache.js
var require_lrucache = __commonJS({
  "../../node_modules/semver/internal/lrucache.js"(exports, module) {
    "use strict";
    var LRUCache = class {
      constructor() {
        this.max = 1e3;
        this.map = /* @__PURE__ */ new Map();
      }
      get(key) {
        const value = this.map.get(key);
        if (value === void 0) {
          return void 0;
        } else {
          this.map.delete(key);
          this.map.set(key, value);
          return value;
        }
      }
      delete(key) {
        return this.map.delete(key);
      }
      set(key, value) {
        const deleted = this.delete(key);
        if (!deleted && value !== void 0) {
          if (this.map.size >= this.max) {
            const firstKey = this.map.keys().next().value;
            this.delete(firstKey);
          }
          this.map.set(key, value);
        }
        return this;
      }
    };
    module.exports = LRUCache;
  }
});

// ../../node_modules/semver/classes/range.js
var require_range = __commonJS({
  "../../node_modules/semver/classes/range.js"(exports, module) {
    "use strict";
    var SPACE_CHARACTERS = /\s+/g;
    var Range = class _Range {
      constructor(range, options) {
        options = parseOptions(options);
        if (range instanceof _Range) {
          if (range.loose === !!options.loose && range.includePrerelease === !!options.includePrerelease) {
            return range;
          } else {
            return new _Range(range.raw, options);
          }
        }
        if (range instanceof Comparator) {
          this.raw = range.value;
          this.set = [[range]];
          this.formatted = void 0;
          return this;
        }
        this.options = options;
        this.loose = !!options.loose;
        this.includePrerelease = !!options.includePrerelease;
        this.raw = range.trim().replace(SPACE_CHARACTERS, " ");
        this.set = this.raw.split("||").map((r) => this.parseRange(r.trim())).filter((c) => c.length);
        if (!this.set.length) {
          throw new TypeError(`Invalid SemVer Range: ${this.raw}`);
        }
        if (this.set.length > 1) {
          const first = this.set[0];
          this.set = this.set.filter((c) => !isNullSet(c[0]));
          if (this.set.length === 0) {
            this.set = [first];
          } else if (this.set.length > 1) {
            for (const c of this.set) {
              if (c.length === 1 && isAny(c[0])) {
                this.set = [c];
                break;
              }
            }
          }
        }
        this.formatted = void 0;
      }
      get range() {
        if (this.formatted === void 0) {
          this.formatted = "";
          for (let i = 0; i < this.set.length; i++) {
            if (i > 0) {
              this.formatted += "||";
            }
            const comps = this.set[i];
            for (let k = 0; k < comps.length; k++) {
              if (k > 0) {
                this.formatted += " ";
              }
              this.formatted += comps[k].toString().trim();
            }
          }
        }
        return this.formatted;
      }
      format() {
        return this.range;
      }
      toString() {
        return this.range;
      }
      parseRange(range) {
        const memoOpts = (this.options.includePrerelease && FLAG_INCLUDE_PRERELEASE) | (this.options.loose && FLAG_LOOSE);
        const memoKey = memoOpts + ":" + range;
        const cached = cache.get(memoKey);
        if (cached) {
          return cached;
        }
        const loose = this.options.loose;
        const hr = loose ? re[t.HYPHENRANGELOOSE] : re[t.HYPHENRANGE];
        range = range.replace(hr, hyphenReplace(this.options.includePrerelease));
        debug("hyphen replace", range);
        range = range.replace(re[t.COMPARATORTRIM], comparatorTrimReplace);
        debug("comparator trim", range);
        range = range.replace(re[t.TILDETRIM], tildeTrimReplace);
        debug("tilde trim", range);
        range = range.replace(re[t.CARETTRIM], caretTrimReplace);
        debug("caret trim", range);
        let rangeList = range.split(" ").map((comp) => parseComparator(comp, this.options)).join(" ").split(/\s+/).map((comp) => replaceGTE0(comp, this.options));
        if (loose) {
          rangeList = rangeList.filter((comp) => {
            debug("loose invalid filter", comp, this.options);
            return !!comp.match(re[t.COMPARATORLOOSE]);
          });
        }
        debug("range list", rangeList);
        const rangeMap = /* @__PURE__ */ new Map();
        const comparators = rangeList.map((comp) => new Comparator(comp, this.options));
        for (const comp of comparators) {
          if (isNullSet(comp)) {
            return [comp];
          }
          rangeMap.set(comp.value, comp);
        }
        if (rangeMap.size > 1 && rangeMap.has("")) {
          rangeMap.delete("");
        }
        const result = [...rangeMap.values()];
        cache.set(memoKey, result);
        return result;
      }
      intersects(range, options) {
        if (!(range instanceof _Range)) {
          throw new TypeError("a Range is required");
        }
        return this.set.some((thisComparators) => {
          return isSatisfiable(thisComparators, options) && range.set.some((rangeComparators) => {
            return isSatisfiable(rangeComparators, options) && thisComparators.every((thisComparator) => {
              return rangeComparators.every((rangeComparator) => {
                return thisComparator.intersects(rangeComparator, options);
              });
            });
          });
        });
      }
      // if ANY of the sets match ALL of its comparators, then pass
      test(version) {
        if (!version) {
          return false;
        }
        if (typeof version === "string") {
          try {
            version = new SemVer(version, this.options);
          } catch (er) {
            return false;
          }
        }
        for (let i = 0; i < this.set.length; i++) {
          if (testSet(this.set[i], version, this.options)) {
            return true;
          }
        }
        return false;
      }
    };
    module.exports = Range;
    var LRU = require_lrucache();
    var cache = new LRU();
    var parseOptions = require_parse_options();
    var Comparator = require_comparator();
    var debug = require_debug();
    var SemVer = require_semver();
    var {
      safeRe: re,
      t,
      comparatorTrimReplace,
      tildeTrimReplace,
      caretTrimReplace
    } = require_re();
    var { FLAG_INCLUDE_PRERELEASE, FLAG_LOOSE } = require_constants();
    var isNullSet = (c) => c.value === "<0.0.0-0";
    var isAny = (c) => c.value === "";
    var isSatisfiable = (comparators, options) => {
      let result = true;
      const remainingComparators = comparators.slice();
      let testComparator = remainingComparators.pop();
      while (result && remainingComparators.length) {
        result = remainingComparators.every((otherComparator) => {
          return testComparator.intersects(otherComparator, options);
        });
        testComparator = remainingComparators.pop();
      }
      return result;
    };
    var parseComparator = (comp, options) => {
      debug("comp", comp, options);
      comp = replaceCarets(comp, options);
      debug("caret", comp);
      comp = replaceTildes(comp, options);
      debug("tildes", comp);
      comp = replaceXRanges(comp, options);
      debug("xrange", comp);
      comp = replaceStars(comp, options);
      debug("stars", comp);
      return comp;
    };
    var isX = (id) => !id || id.toLowerCase() === "x" || id === "*";
    var replaceTildes = (comp, options) => {
      return comp.trim().split(/\s+/).map((c) => replaceTilde(c, options)).join(" ");
    };
    var replaceTilde = (comp, options) => {
      const r = options.loose ? re[t.TILDELOOSE] : re[t.TILDE];
      return comp.replace(r, (_, M, m, p, pr) => {
        debug("tilde", comp, _, M, m, p, pr);
        let ret;
        if (isX(M)) {
          ret = "";
        } else if (isX(m)) {
          ret = `>=${M}.0.0 <${+M + 1}.0.0-0`;
        } else if (isX(p)) {
          ret = `>=${M}.${m}.0 <${M}.${+m + 1}.0-0`;
        } else if (pr) {
          debug("replaceTilde pr", pr);
          ret = `>=${M}.${m}.${p}-${pr} <${M}.${+m + 1}.0-0`;
        } else {
          ret = `>=${M}.${m}.${p} <${M}.${+m + 1}.0-0`;
        }
        debug("tilde return", ret);
        return ret;
      });
    };
    var replaceCarets = (comp, options) => {
      return comp.trim().split(/\s+/).map((c) => replaceCaret(c, options)).join(" ");
    };
    var replaceCaret = (comp, options) => {
      debug("caret", comp, options);
      const r = options.loose ? re[t.CARETLOOSE] : re[t.CARET];
      const z = options.includePrerelease ? "-0" : "";
      return comp.replace(r, (_, M, m, p, pr) => {
        debug("caret", comp, _, M, m, p, pr);
        let ret;
        if (isX(M)) {
          ret = "";
        } else if (isX(m)) {
          ret = `>=${M}.0.0${z} <${+M + 1}.0.0-0`;
        } else if (isX(p)) {
          if (M === "0") {
            ret = `>=${M}.${m}.0${z} <${M}.${+m + 1}.0-0`;
          } else {
            ret = `>=${M}.${m}.0${z} <${+M + 1}.0.0-0`;
          }
        } else if (pr) {
          debug("replaceCaret pr", pr);
          if (M === "0") {
            if (m === "0") {
              ret = `>=${M}.${m}.${p}-${pr} <${M}.${m}.${+p + 1}-0`;
            } else {
              ret = `>=${M}.${m}.${p}-${pr} <${M}.${+m + 1}.0-0`;
            }
          } else {
            ret = `>=${M}.${m}.${p}-${pr} <${+M + 1}.0.0-0`;
          }
        } else {
          debug("no pr");
          if (M === "0") {
            if (m === "0") {
              ret = `>=${M}.${m}.${p}${z} <${M}.${m}.${+p + 1}-0`;
            } else {
              ret = `>=${M}.${m}.${p}${z} <${M}.${+m + 1}.0-0`;
            }
          } else {
            ret = `>=${M}.${m}.${p} <${+M + 1}.0.0-0`;
          }
        }
        debug("caret return", ret);
        return ret;
      });
    };
    var replaceXRanges = (comp, options) => {
      debug("replaceXRanges", comp, options);
      return comp.split(/\s+/).map((c) => replaceXRange(c, options)).join(" ");
    };
    var replaceXRange = (comp, options) => {
      comp = comp.trim();
      const r = options.loose ? re[t.XRANGELOOSE] : re[t.XRANGE];
      return comp.replace(r, (ret, gtlt, M, m, p, pr) => {
        debug("xRange", comp, ret, gtlt, M, m, p, pr);
        const xM = isX(M);
        const xm = xM || isX(m);
        const xp = xm || isX(p);
        const anyX = xp;
        if (gtlt === "=" && anyX) {
          gtlt = "";
        }
        pr = options.includePrerelease ? "-0" : "";
        if (xM) {
          if (gtlt === ">" || gtlt === "<") {
            ret = "<0.0.0-0";
          } else {
            ret = "*";
          }
        } else if (gtlt && anyX) {
          if (xm) {
            m = 0;
          }
          p = 0;
          if (gtlt === ">") {
            gtlt = ">=";
            if (xm) {
              M = +M + 1;
              m = 0;
              p = 0;
            } else {
              m = +m + 1;
              p = 0;
            }
          } else if (gtlt === "<=") {
            gtlt = "<";
            if (xm) {
              M = +M + 1;
            } else {
              m = +m + 1;
            }
          }
          if (gtlt === "<") {
            pr = "-0";
          }
          ret = `${gtlt + M}.${m}.${p}${pr}`;
        } else if (xm) {
          ret = `>=${M}.0.0${pr} <${+M + 1}.0.0-0`;
        } else if (xp) {
          ret = `>=${M}.${m}.0${pr} <${M}.${+m + 1}.0-0`;
        }
        debug("xRange return", ret);
        return ret;
      });
    };
    var replaceStars = (comp, options) => {
      debug("replaceStars", comp, options);
      return comp.trim().replace(re[t.STAR], "");
    };
    var replaceGTE0 = (comp, options) => {
      debug("replaceGTE0", comp, options);
      return comp.trim().replace(re[options.includePrerelease ? t.GTE0PRE : t.GTE0], "");
    };
    var hyphenReplace = (incPr) => ($0, from, fM, fm, fp, fpr, fb, to, tM, tm, tp, tpr) => {
      if (isX(fM)) {
        from = "";
      } else if (isX(fm)) {
        from = `>=${fM}.0.0${incPr ? "-0" : ""}`;
      } else if (isX(fp)) {
        from = `>=${fM}.${fm}.0${incPr ? "-0" : ""}`;
      } else if (fpr) {
        from = `>=${from}`;
      } else {
        from = `>=${from}${incPr ? "-0" : ""}`;
      }
      if (isX(tM)) {
        to = "";
      } else if (isX(tm)) {
        to = `<${+tM + 1}.0.0-0`;
      } else if (isX(tp)) {
        to = `<${tM}.${+tm + 1}.0-0`;
      } else if (tpr) {
        to = `<=${tM}.${tm}.${tp}-${tpr}`;
      } else if (incPr) {
        to = `<${tM}.${tm}.${+tp + 1}-0`;
      } else {
        to = `<=${to}`;
      }
      return `${from} ${to}`.trim();
    };
    var testSet = (set, version, options) => {
      for (let i = 0; i < set.length; i++) {
        if (!set[i].test(version)) {
          return false;
        }
      }
      if (version.prerelease.length && !options.includePrerelease) {
        for (let i = 0; i < set.length; i++) {
          debug(set[i].semver);
          if (set[i].semver === Comparator.ANY) {
            continue;
          }
          if (set[i].semver.prerelease.length > 0) {
            const allowed = set[i].semver;
            if (allowed.major === version.major && allowed.minor === version.minor && allowed.patch === version.patch) {
              return true;
            }
          }
        }
        return false;
      }
      return true;
    };
  }
});

// ../../node_modules/semver/classes/comparator.js
var require_comparator = __commonJS({
  "../../node_modules/semver/classes/comparator.js"(exports, module) {
    "use strict";
    var ANY = Symbol("SemVer ANY");
    var Comparator = class _Comparator {
      static get ANY() {
        return ANY;
      }
      constructor(comp, options) {
        options = parseOptions(options);
        if (comp instanceof _Comparator) {
          if (comp.loose === !!options.loose) {
            return comp;
          } else {
            comp = comp.value;
          }
        }
        comp = comp.trim().split(/\s+/).join(" ");
        debug("comparator", comp, options);
        this.options = options;
        this.loose = !!options.loose;
        this.parse(comp);
        if (this.semver === ANY) {
          this.value = "";
        } else {
          this.value = this.operator + this.semver.version;
        }
        debug("comp", this);
      }
      parse(comp) {
        const r = this.options.loose ? re[t.COMPARATORLOOSE] : re[t.COMPARATOR];
        const m = comp.match(r);
        if (!m) {
          throw new TypeError(`Invalid comparator: ${comp}`);
        }
        this.operator = m[1] !== void 0 ? m[1] : "";
        if (this.operator === "=") {
          this.operator = "";
        }
        if (!m[2]) {
          this.semver = ANY;
        } else {
          this.semver = new SemVer(m[2], this.options.loose);
        }
      }
      toString() {
        return this.value;
      }
      test(version) {
        debug("Comparator.test", version, this.options.loose);
        if (this.semver === ANY || version === ANY) {
          return true;
        }
        if (typeof version === "string") {
          try {
            version = new SemVer(version, this.options);
          } catch (er) {
            return false;
          }
        }
        return cmp(version, this.operator, this.semver, this.options);
      }
      intersects(comp, options) {
        if (!(comp instanceof _Comparator)) {
          throw new TypeError("a Comparator is required");
        }
        if (this.operator === "") {
          if (this.value === "") {
            return true;
          }
          return new Range(comp.value, options).test(this.value);
        } else if (comp.operator === "") {
          if (comp.value === "") {
            return true;
          }
          return new Range(this.value, options).test(comp.semver);
        }
        options = parseOptions(options);
        if (options.includePrerelease && (this.value === "<0.0.0-0" || comp.value === "<0.0.0-0")) {
          return false;
        }
        if (!options.includePrerelease && (this.value.startsWith("<0.0.0") || comp.value.startsWith("<0.0.0"))) {
          return false;
        }
        if (this.operator.startsWith(">") && comp.operator.startsWith(">")) {
          return true;
        }
        if (this.operator.startsWith("<") && comp.operator.startsWith("<")) {
          return true;
        }
        if (this.semver.version === comp.semver.version && this.operator.includes("=") && comp.operator.includes("=")) {
          return true;
        }
        if (cmp(this.semver, "<", comp.semver, options) && this.operator.startsWith(">") && comp.operator.startsWith("<")) {
          return true;
        }
        if (cmp(this.semver, ">", comp.semver, options) && this.operator.startsWith("<") && comp.operator.startsWith(">")) {
          return true;
        }
        return false;
      }
    };
    module.exports = Comparator;
    var parseOptions = require_parse_options();
    var { safeRe: re, t } = require_re();
    var cmp = require_cmp();
    var debug = require_debug();
    var SemVer = require_semver();
    var Range = require_range();
  }
});

// ../../node_modules/semver/functions/satisfies.js
var require_satisfies = __commonJS({
  "../../node_modules/semver/functions/satisfies.js"(exports, module) {
    "use strict";
    var Range = require_range();
    var satisfies = (version, range, options) => {
      try {
        range = new Range(range, options);
      } catch (er) {
        return false;
      }
      return range.test(version);
    };
    module.exports = satisfies;
  }
});

// ../../node_modules/semver/ranges/to-comparators.js
var require_to_comparators = __commonJS({
  "../../node_modules/semver/ranges/to-comparators.js"(exports, module) {
    "use strict";
    var Range = require_range();
    var toComparators = (range, options) => new Range(range, options).set.map((comp) => comp.map((c) => c.value).join(" ").trim().split(" "));
    module.exports = toComparators;
  }
});

// ../../node_modules/semver/ranges/max-satisfying.js
var require_max_satisfying = __commonJS({
  "../../node_modules/semver/ranges/max-satisfying.js"(exports, module) {
    "use strict";
    var SemVer = require_semver();
    var Range = require_range();
    var maxSatisfying = (versions, range, options) => {
      let max = null;
      let maxSV = null;
      let rangeObj = null;
      try {
        rangeObj = new Range(range, options);
      } catch (er) {
        return null;
      }
      versions.forEach((v) => {
        if (rangeObj.test(v)) {
          if (!max || maxSV.compare(v) === -1) {
            max = v;
            maxSV = new SemVer(max, options);
          }
        }
      });
      return max;
    };
    module.exports = maxSatisfying;
  }
});

// ../../node_modules/semver/ranges/min-satisfying.js
var require_min_satisfying = __commonJS({
  "../../node_modules/semver/ranges/min-satisfying.js"(exports, module) {
    "use strict";
    var SemVer = require_semver();
    var Range = require_range();
    var minSatisfying = (versions, range, options) => {
      let min = null;
      let minSV = null;
      let rangeObj = null;
      try {
        rangeObj = new Range(range, options);
      } catch (er) {
        return null;
      }
      versions.forEach((v) => {
        if (rangeObj.test(v)) {
          if (!min || minSV.compare(v) === 1) {
            min = v;
            minSV = new SemVer(min, options);
          }
        }
      });
      return min;
    };
    module.exports = minSatisfying;
  }
});

// ../../node_modules/semver/ranges/min-version.js
var require_min_version = __commonJS({
  "../../node_modules/semver/ranges/min-version.js"(exports, module) {
    "use strict";
    var SemVer = require_semver();
    var Range = require_range();
    var gt = require_gt();
    var minVersion = (range, loose) => {
      range = new Range(range, loose);
      let minver = new SemVer("0.0.0");
      if (range.test(minver)) {
        return minver;
      }
      minver = new SemVer("0.0.0-0");
      if (range.test(minver)) {
        return minver;
      }
      minver = null;
      for (let i = 0; i < range.set.length; ++i) {
        const comparators = range.set[i];
        let setMin = null;
        comparators.forEach((comparator) => {
          const compver = new SemVer(comparator.semver.version);
          switch (comparator.operator) {
            case ">":
              if (compver.prerelease.length === 0) {
                compver.patch++;
              } else {
                compver.prerelease.push(0);
              }
              compver.raw = compver.format();
            case "":
            case ">=":
              if (!setMin || gt(compver, setMin)) {
                setMin = compver;
              }
              break;
            case "<":
            case "<=":
              break;
            default:
              throw new Error(`Unexpected operation: ${comparator.operator}`);
          }
        });
        if (setMin && (!minver || gt(minver, setMin))) {
          minver = setMin;
        }
      }
      if (minver && range.test(minver)) {
        return minver;
      }
      return null;
    };
    module.exports = minVersion;
  }
});

// ../../node_modules/semver/ranges/valid.js
var require_valid2 = __commonJS({
  "../../node_modules/semver/ranges/valid.js"(exports, module) {
    "use strict";
    var Range = require_range();
    var validRange = (range, options) => {
      try {
        return new Range(range, options).range || "*";
      } catch (er) {
        return null;
      }
    };
    module.exports = validRange;
  }
});

// ../../node_modules/semver/ranges/outside.js
var require_outside = __commonJS({
  "../../node_modules/semver/ranges/outside.js"(exports, module) {
    "use strict";
    var SemVer = require_semver();
    var Comparator = require_comparator();
    var { ANY } = Comparator;
    var Range = require_range();
    var satisfies = require_satisfies();
    var gt = require_gt();
    var lt = require_lt();
    var lte = require_lte();
    var gte = require_gte();
    var outside = (version, range, hilo, options) => {
      version = new SemVer(version, options);
      range = new Range(range, options);
      let gtfn, ltefn, ltfn, comp, ecomp;
      switch (hilo) {
        case ">":
          gtfn = gt;
          ltefn = lte;
          ltfn = lt;
          comp = ">";
          ecomp = ">=";
          break;
        case "<":
          gtfn = lt;
          ltefn = gte;
          ltfn = gt;
          comp = "<";
          ecomp = "<=";
          break;
        default:
          throw new TypeError('Must provide a hilo val of "<" or ">"');
      }
      if (satisfies(version, range, options)) {
        return false;
      }
      for (let i = 0; i < range.set.length; ++i) {
        const comparators = range.set[i];
        let high = null;
        let low = null;
        comparators.forEach((comparator) => {
          if (comparator.semver === ANY) {
            comparator = new Comparator(">=0.0.0");
          }
          high = high || comparator;
          low = low || comparator;
          if (gtfn(comparator.semver, high.semver, options)) {
            high = comparator;
          } else if (ltfn(comparator.semver, low.semver, options)) {
            low = comparator;
          }
        });
        if (high.operator === comp || high.operator === ecomp) {
          return false;
        }
        if ((!low.operator || low.operator === comp) && ltefn(version, low.semver)) {
          return false;
        } else if (low.operator === ecomp && ltfn(version, low.semver)) {
          return false;
        }
      }
      return true;
    };
    module.exports = outside;
  }
});

// ../../node_modules/semver/ranges/gtr.js
var require_gtr = __commonJS({
  "../../node_modules/semver/ranges/gtr.js"(exports, module) {
    "use strict";
    var outside = require_outside();
    var gtr = (version, range, options) => outside(version, range, ">", options);
    module.exports = gtr;
  }
});

// ../../node_modules/semver/ranges/ltr.js
var require_ltr = __commonJS({
  "../../node_modules/semver/ranges/ltr.js"(exports, module) {
    "use strict";
    var outside = require_outside();
    var ltr = (version, range, options) => outside(version, range, "<", options);
    module.exports = ltr;
  }
});

// ../../node_modules/semver/ranges/intersects.js
var require_intersects = __commonJS({
  "../../node_modules/semver/ranges/intersects.js"(exports, module) {
    "use strict";
    var Range = require_range();
    var intersects = (r1, r2, options) => {
      r1 = new Range(r1, options);
      r2 = new Range(r2, options);
      return r1.intersects(r2, options);
    };
    module.exports = intersects;
  }
});

// ../../node_modules/semver/ranges/simplify.js
var require_simplify = __commonJS({
  "../../node_modules/semver/ranges/simplify.js"(exports, module) {
    "use strict";
    var satisfies = require_satisfies();
    var compare = require_compare();
    module.exports = (versions, range, options) => {
      const set = [];
      let first = null;
      let prev = null;
      const v = versions.sort((a, b) => compare(a, b, options));
      for (const version of v) {
        const included = satisfies(version, range, options);
        if (included) {
          prev = version;
          if (!first) {
            first = version;
          }
        } else {
          if (prev) {
            set.push([first, prev]);
          }
          prev = null;
          first = null;
        }
      }
      if (first) {
        set.push([first, null]);
      }
      const ranges = [];
      for (const [min, max] of set) {
        if (min === max) {
          ranges.push(min);
        } else if (!max && min === v[0]) {
          ranges.push("*");
        } else if (!max) {
          ranges.push(`>=${min}`);
        } else if (min === v[0]) {
          ranges.push(`<=${max}`);
        } else {
          ranges.push(`${min} - ${max}`);
        }
      }
      const simplified = ranges.join(" || ");
      const original = typeof range.raw === "string" ? range.raw : String(range);
      return simplified.length < original.length ? simplified : range;
    };
  }
});

// ../../node_modules/semver/ranges/subset.js
var require_subset = __commonJS({
  "../../node_modules/semver/ranges/subset.js"(exports, module) {
    "use strict";
    var Range = require_range();
    var Comparator = require_comparator();
    var { ANY } = Comparator;
    var satisfies = require_satisfies();
    var compare = require_compare();
    var subset = (sub, dom, options = {}) => {
      if (sub === dom) {
        return true;
      }
      sub = new Range(sub, options);
      dom = new Range(dom, options);
      let sawNonNull = false;
      OUTER:
        for (const simpleSub of sub.set) {
          for (const simpleDom of dom.set) {
            const isSub = simpleSubset(simpleSub, simpleDom, options);
            sawNonNull = sawNonNull || isSub !== null;
            if (isSub) {
              continue OUTER;
            }
          }
          if (sawNonNull) {
            return false;
          }
        }
      return true;
    };
    var minimumVersionWithPreRelease = [new Comparator(">=0.0.0-0")];
    var minimumVersion = [new Comparator(">=0.0.0")];
    var simpleSubset = (sub, dom, options) => {
      if (sub === dom) {
        return true;
      }
      if (sub.length === 1 && sub[0].semver === ANY) {
        if (dom.length === 1 && dom[0].semver === ANY) {
          return true;
        } else if (options.includePrerelease) {
          sub = minimumVersionWithPreRelease;
        } else {
          sub = minimumVersion;
        }
      }
      if (dom.length === 1 && dom[0].semver === ANY) {
        if (options.includePrerelease) {
          return true;
        } else {
          dom = minimumVersion;
        }
      }
      const eqSet = /* @__PURE__ */ new Set();
      let gt, lt;
      for (const c of sub) {
        if (c.operator === ">" || c.operator === ">=") {
          gt = higherGT(gt, c, options);
        } else if (c.operator === "<" || c.operator === "<=") {
          lt = lowerLT(lt, c, options);
        } else {
          eqSet.add(c.semver);
        }
      }
      if (eqSet.size > 1) {
        return null;
      }
      let gtltComp;
      if (gt && lt) {
        gtltComp = compare(gt.semver, lt.semver, options);
        if (gtltComp > 0) {
          return null;
        } else if (gtltComp === 0 && (gt.operator !== ">=" || lt.operator !== "<=")) {
          return null;
        }
      }
      for (const eq of eqSet) {
        if (gt && !satisfies(eq, String(gt), options)) {
          return null;
        }
        if (lt && !satisfies(eq, String(lt), options)) {
          return null;
        }
        for (const c of dom) {
          if (!satisfies(eq, String(c), options)) {
            return false;
          }
        }
        return true;
      }
      let higher, lower;
      let hasDomLT, hasDomGT;
      let needDomLTPre = lt && !options.includePrerelease && lt.semver.prerelease.length ? lt.semver : false;
      let needDomGTPre = gt && !options.includePrerelease && gt.semver.prerelease.length ? gt.semver : false;
      if (needDomLTPre && needDomLTPre.prerelease.length === 1 && lt.operator === "<" && needDomLTPre.prerelease[0] === 0) {
        needDomLTPre = false;
      }
      for (const c of dom) {
        hasDomGT = hasDomGT || c.operator === ">" || c.operator === ">=";
        hasDomLT = hasDomLT || c.operator === "<" || c.operator === "<=";
        if (gt) {
          if (needDomGTPre) {
            if (c.semver.prerelease && c.semver.prerelease.length && c.semver.major === needDomGTPre.major && c.semver.minor === needDomGTPre.minor && c.semver.patch === needDomGTPre.patch) {
              needDomGTPre = false;
            }
          }
          if (c.operator === ">" || c.operator === ">=") {
            higher = higherGT(gt, c, options);
            if (higher === c && higher !== gt) {
              return false;
            }
          } else if (gt.operator === ">=" && !satisfies(gt.semver, String(c), options)) {
            return false;
          }
        }
        if (lt) {
          if (needDomLTPre) {
            if (c.semver.prerelease && c.semver.prerelease.length && c.semver.major === needDomLTPre.major && c.semver.minor === needDomLTPre.minor && c.semver.patch === needDomLTPre.patch) {
              needDomLTPre = false;
            }
          }
          if (c.operator === "<" || c.operator === "<=") {
            lower = lowerLT(lt, c, options);
            if (lower === c && lower !== lt) {
              return false;
            }
          } else if (lt.operator === "<=" && !satisfies(lt.semver, String(c), options)) {
            return false;
          }
        }
        if (!c.operator && (lt || gt) && gtltComp !== 0) {
          return false;
        }
      }
      if (gt && hasDomLT && !lt && gtltComp !== 0) {
        return false;
      }
      if (lt && hasDomGT && !gt && gtltComp !== 0) {
        return false;
      }
      if (needDomGTPre || needDomLTPre) {
        return false;
      }
      return true;
    };
    var higherGT = (a, b, options) => {
      if (!a) {
        return b;
      }
      const comp = compare(a.semver, b.semver, options);
      return comp > 0 ? a : comp < 0 ? b : b.operator === ">" && a.operator === ">=" ? b : a;
    };
    var lowerLT = (a, b, options) => {
      if (!a) {
        return b;
      }
      const comp = compare(a.semver, b.semver, options);
      return comp < 0 ? a : comp > 0 ? b : b.operator === "<" && a.operator === "<=" ? b : a;
    };
    module.exports = subset;
  }
});

// ../../node_modules/semver/index.js
var require_semver2 = __commonJS({
  "../../node_modules/semver/index.js"(exports, module) {
    "use strict";
    var internalRe = require_re();
    var constants3 = require_constants();
    var SemVer = require_semver();
    var identifiers = require_identifiers();
    var parse = require_parse2();
    var valid = require_valid();
    var clean = require_clean();
    var inc = require_inc();
    var diff = require_diff();
    var major = require_major();
    var minor = require_minor();
    var patch = require_patch();
    var prerelease = require_prerelease();
    var compare = require_compare();
    var rcompare = require_rcompare();
    var compareLoose = require_compare_loose();
    var compareBuild = require_compare_build();
    var sort = require_sort();
    var rsort = require_rsort();
    var gt = require_gt();
    var lt = require_lt();
    var eq = require_eq();
    var neq = require_neq();
    var gte = require_gte();
    var lte = require_lte();
    var cmp = require_cmp();
    var coerce = require_coerce();
    var Comparator = require_comparator();
    var Range = require_range();
    var satisfies = require_satisfies();
    var toComparators = require_to_comparators();
    var maxSatisfying = require_max_satisfying();
    var minSatisfying = require_min_satisfying();
    var minVersion = require_min_version();
    var validRange = require_valid2();
    var outside = require_outside();
    var gtr = require_gtr();
    var ltr = require_ltr();
    var intersects = require_intersects();
    var simplifyRange = require_simplify();
    var subset = require_subset();
    module.exports = {
      parse,
      valid,
      clean,
      inc,
      diff,
      major,
      minor,
      patch,
      prerelease,
      compare,
      rcompare,
      compareLoose,
      compareBuild,
      sort,
      rsort,
      gt,
      lt,
      eq,
      neq,
      gte,
      lte,
      cmp,
      coerce,
      Comparator,
      Range,
      satisfies,
      toComparators,
      maxSatisfying,
      minSatisfying,
      minVersion,
      validRange,
      outside,
      gtr,
      ltr,
      intersects,
      simplifyRange,
      subset,
      SemVer,
      re: internalRe.re,
      src: internalRe.src,
      tokens: internalRe.t,
      SEMVER_SPEC_VERSION: constants3.SEMVER_SPEC_VERSION,
      RELEASE_TYPES: constants3.RELEASE_TYPES,
      compareIdentifiers: identifiers.compareIdentifiers,
      rcompareIdentifiers: identifiers.rcompareIdentifiers
    };
  }
});

// src/utils/config.ts
var config_exports = {};
__export(config_exports, {
  getDefaultConfig: () => getDefaultConfig,
  loadKilatConfig: () => loadKilatConfig,
  saveKilatConfig: () => saveKilatConfig,
  updateKilatConfig: () => updateKilatConfig,
  validateKilatConfig: () => validateKilatConfig
});
import { pathExists as pathExists2, readFile as readFile2, writeFile as writeFile2 } from "fs-extra";
import { join as join2 } from "path";
async function loadKilatConfig(configPath) {
  const possiblePaths = [
    configPath,
    "kilat.config.ts",
    "kilat.config.js",
    "kilat.config.mjs",
    ".kilatrc.json",
    ".kilatrc.js"
  ].filter(Boolean);
  for (const path3 of possiblePaths) {
    if (await pathExists2(path3)) {
      return await loadConfigFile(path3);
    }
  }
  return getDefaultConfig();
}
async function loadConfigFile(configPath) {
  try {
    if (configPath.endsWith(".json")) {
      const content = await readFile2(configPath, "utf-8");
      return JSON.parse(content);
    }
    if (configPath.endsWith(".ts") || configPath.endsWith(".js") || configPath.endsWith(".mjs")) {
      const fullPath = join2(process.cwd(), configPath);
      delete __require.cache[fullPath];
      const configModule = __require(fullPath);
      return configModule.default || configModule;
    }
    throw new Error(`Unsupported config file format: ${configPath}`);
  } catch (error) {
    throw new Error(`Failed to load config from ${configPath}: ${error.message}`);
  }
}
async function saveKilatConfig(config, configPath = "kilat.config.ts") {
  try {
    if (configPath.endsWith(".json")) {
      await writeFile2(configPath, JSON.stringify(config, null, 2));
    } else {
      const configContent = generateConfigFile(config);
      await writeFile2(configPath, configContent);
    }
  } catch (error) {
    throw new Error(`Failed to save config to ${configPath}: ${error.message}`);
  }
}
function generateConfigFile(config) {
  return `import type { KilatConfig } from 'kilat-core';

export default {
  // \u{1F3AF} Project Configuration
  name: "${config.name}",
  version: "${config.version}",
  platform: "${config.platform}",
  
  // \u{1F3A8} UI Theme
  theme: "${config.theme}",
  mode: "${config.mode}",
  
  // \u{1F30C} Animation Settings
  presetScene: "${config.presetScene}",
  animation: {
    autoRotate: ${config.animation.autoRotate},
    background: "${config.animation.background}",
    interactive: ${config.animation.interactive},
    ambientSound: ${config.animation.ambientSound}
  },
  
  // \u{1F6E3}\uFE0F Router Configuration
  router: {
    basePath: "${config.router.basePath}",
    middleware: ${JSON.stringify(config.router.middleware)},
    fileBasedRouting: ${config.router.fileBasedRouting},
    dynamicRoutes: ${config.router.dynamicRoutes},
    layoutNesting: ${config.router.layoutNesting}
  },
  
  // \u{1F5C4}\uFE0F Database Configuration
  database: {
    driver: "${config.database.driver}",
    connection: ${JSON.stringify(config.database.connection, null, 4)},
    migrations: {
      directory: "${config.database.migrations.directory}",
      autoRun: ${config.database.migrations.autoRun}
    }
  },
  
  // \u{1F3D7}\uFE0F Build Configuration
  build: {
    engine: "${config.build.engine}",
    target: "${config.build.target}",
    minify: ${config.build.minify},
    sourcemap: ${config.build.sourcemap},
    debugOverlay: ${config.build.debugOverlay},
    hotReload: ${config.build.hotReload},
    analyze: ${config.build.analyze},
    outputDir: "${config.build.outputDir}",
    publicDir: "${config.build.publicDir}",
    assetsDir: "${config.build.assetsDir}"
  },
  
  // \u{1F680} Backend Configuration
  backend: {
    enabled: ${config.backend.enabled},
    port: ${config.backend.port},
    autoStart: ${config.backend.autoStart},
    apiPrefix: "${config.backend.apiPrefix}"
  },
  
  // \u{1F50C} Plugin Configuration
  plugins: ${JSON.stringify(config.plugins, null, 4)},
  
  // \u{1F916} AI Assistant
  aiAssistant: {
    enabled: ${config.aiAssistant.enabled},
    endpoint: "${config.aiAssistant.endpoint}",
    model: "${config.aiAssistant.model}",
    features: ${JSON.stringify(config.aiAssistant.features)}
  },
  
  // \u{1F4F1} Platform Configuration
  platform: {
    web: {
      enabled: ${config.platform.web.enabled},
      ssr: ${config.platform.web.ssr},
      pwa: ${config.platform.web.pwa}
    },
    desktop: {
      enabled: ${config.platform.desktop.enabled},
      electron: ${config.platform.desktop.electron},
      tauri: ${config.platform.desktop.tauri}
    },
    mobile: {
      enabled: ${config.platform.mobile.enabled},
      expo: ${config.platform.mobile.expo},
      capacitor: ${config.platform.mobile.capacitor}
    }
  },
  
  // \u{1F527} Development Configuration
  dev: {
    port: ${config.dev.port},
    host: "${config.dev.host}",
    open: ${config.dev.open},
    cors: ${config.dev.cors},
    proxy: ${JSON.stringify(config.dev.proxy)},
    hmr: {
      port: ${config.dev.hmr.port},
      overlay: ${config.dev.hmr.overlay}
    }
  },
  
  // \u26A1 Performance Configuration
  performance: {
    bundleSplitting: ${config.performance.bundleSplitting},
    treeshaking: ${config.performance.treeshaking},
    compression: "${config.performance.compression}",
    caching: {
      enabled: ${config.performance.caching.enabled},
      strategy: "${config.performance.caching.strategy}"
    }
  },
  
  // \u{1F512} Security Configuration
  security: {
    csp: {
      enabled: ${config.security.csp.enabled},
      directives: ${JSON.stringify(config.security.csp.directives, null, 6)}
    },
    cors: {
      origin: ${JSON.stringify(config.security.cors.origin)},
      credentials: ${config.security.cors.credentials}
    }
  },
  
  // \u{1F6E1}\uFE0F Error Handling
  errorHandling: {
    killSwitch: ${config.errorHandling.killSwitch},
    safeMode: ${config.errorHandling.safeMode},
    crashReport: {
      enabled: ${config.errorHandling.crashReport.enabled},
      ${config.errorHandling.crashReport.webhookURL ? `webhookURL: "${config.errorHandling.crashReport.webhookURL}",` : ""}
      includeLogs: ${config.errorHandling.crashReport.includeLogs},
      autoRetry: ${config.errorHandling.crashReport.autoRetry}
    }
  }
} satisfies KilatConfig;
`;
}
function getDefaultConfig() {
  return {
    name: "my-kilat-app",
    version: "1.0.0",
    platform: "web",
    theme: "cyberpunk",
    mode: "dark",
    presetScene: "galaxy",
    animation: {
      autoRotate: true,
      background: "#000000",
      interactive: true,
      ambientSound: false
    },
    router: {
      basePath: "/",
      middleware: [],
      fileBasedRouting: true,
      dynamicRoutes: true,
      layoutNesting: true
    },
    database: {
      driver: "sqlite",
      connection: {
        sqlite: {
          file: "./data.db",
          enableWAL: true,
          timeout: 5e3
        }
      },
      migrations: {
        directory: "./migrations",
        autoRun: true
      }
    },
    build: {
      engine: "kilatpack",
      target: "es2022",
      minify: true,
      sourcemap: true,
      debugOverlay: false,
      hotReload: true,
      analyze: false,
      outputDir: "dist",
      publicDir: "public",
      assetsDir: "assets"
    },
    backend: {
      enabled: true,
      port: 8080,
      autoStart: true,
      apiPrefix: "/api"
    },
    plugins: {},
    aiAssistant: {
      enabled: false,
      endpoint: "/api/ai",
      model: "gpt-4",
      features: []
    },
    platform: {
      web: {
        enabled: true,
        ssr: true,
        pwa: true
      },
      desktop: {
        enabled: false,
        electron: true,
        tauri: false
      },
      mobile: {
        enabled: false,
        expo: true,
        capacitor: false
      }
    },
    dev: {
      port: 3e3,
      host: "localhost",
      open: true,
      cors: true,
      proxy: {},
      hmr: {
        port: 24678,
        overlay: true
      }
    },
    performance: {
      bundleSplitting: true,
      treeshaking: true,
      compression: "gzip",
      caching: {
        enabled: true,
        strategy: "stale-while-revalidate"
      }
    },
    security: {
      csp: {
        enabled: true,
        directives: {
          "default-src": ["'self'"],
          "script-src": ["'self'", "'unsafe-inline'"],
          "style-src": ["'self'", "'unsafe-inline'"],
          "img-src": ["'self'", "data:", "https:"]
        }
      },
      cors: {
        origin: ["http://localhost:3000"],
        credentials: true
      }
    },
    errorHandling: {
      killSwitch: true,
      safeMode: true,
      crashReport: {
        enabled: true,
        includeLogs: true,
        autoRetry: true
      }
    }
  };
}
async function updateKilatConfig(updates, configPath = "kilat.config.ts") {
  const currentConfig = await loadKilatConfig(configPath);
  const updatedConfig = { ...currentConfig, ...updates };
  await saveKilatConfig(updatedConfig, configPath);
}
function validateKilatConfig(config) {
  const errors = [];
  if (!config.name)
    errors.push("Project name is required");
  if (!config.platform)
    errors.push("Platform is required");
  if (!config.theme)
    errors.push("Theme is required");
  const validPlatforms = ["web", "desktop", "mobile", "fullstack"];
  if (!validPlatforms.includes(config.platform)) {
    errors.push(`Invalid platform: ${config.platform}`);
  }
  if (config.database?.driver && !["sqlite", "mysql", "none"].includes(config.database.driver)) {
    errors.push(`Invalid database driver: ${config.database.driver}`);
  }
  if (config.build?.engine && !["kilatpack", "vite", "webpack"].includes(config.build.engine)) {
    errors.push(`Invalid build engine: ${config.build.engine}`);
  }
  return errors;
}
var init_config = __esm({
  "src/utils/config.ts"() {
    "use strict";
  }
});

// src/commands/create.ts
init_execa();
init_package_manager();
import inquirer from "inquirer";
import chalk from "chalk";
import ora2 from "ora";
import { join as join3 } from "path";
import { ensureDir as ensureDir2, pathExists as pathExists4 } from "fs-extra";

// src/utils/generator.ts
import { join } from "path";
import { ensureDir, writeFile } from "fs-extra";
async function generateProjectFiles(context) {
  const { projectPath, template, config, variables } = context;
  await createProjectStructure(projectPath, config.platform);
  await generatePackageJson(context);
  await generateKilatConfig(context);
  await generateAppFiles(context);
  await generateFeatureFiles(context);
  await copyStaticAssets(context);
}
async function createProjectStructure(projectPath, platform) {
  const directories = [
    "src",
    "src/components",
    "src/pages",
    "src/styles",
    "src/utils",
    "public",
    "public/assets",
    ".kilat",
    ".kilat/logs"
  ];
  if (platform === "fullstack" || platform === "web") {
    directories.push("api", "api/routes", "api/middleware");
  }
  if (platform === "desktop") {
    directories.push("electron", "electron/main", "electron/preload");
  }
  if (platform === "mobile") {
    directories.push("mobile", "mobile/components", "mobile/screens");
  }
  for (const dir of directories) {
    await ensureDir(join(projectPath, dir));
  }
}
async function generatePackageJson(context) {
  const { projectPath, config, template, variables } = context;
  const packageJson = {
    name: config.name,
    version: "0.1.0",
    description: `Kilat.js ${config.platform} application`,
    type: "module",
    scripts: {
      dev: "kilat dev",
      build: "kilat build",
      start: "kilat start",
      test: "kilat test",
      lint: "kilat lint",
      "type-check": "tsc --noEmit",
      ...template.scripts
    },
    dependencies: {
      "kilat-core": "^1.0.0",
      "kilatcss": "^1.0.0",
      "kilatanim.js": "^1.0.0",
      "kilat-router": "^1.0.0",
      "kilat-utils": "^1.0.0",
      ...getDependenciesForPlatform(config.platform),
      ...getDependenciesForFeatures(config.features),
      ...getDependenciesForPlugins(config.plugins)
    },
    devDependencies: {
      typescript: "^5.3.0",
      "@types/node": "^20.0.0",
      vite: "^5.0.0",
      vitest: "^1.0.0",
      ...template.devDependencies
    },
    keywords: [
      "kilat",
      "kilatjs",
      config.platform,
      config.theme,
      ...config.features
    ],
    author: "",
    license: "MIT",
    engines: {
      node: ">=18.0.0"
    }
  };
  await writeFile(
    join(projectPath, "package.json"),
    JSON.stringify(packageJson, null, 2)
  );
}
async function generateKilatConfig(context) {
  const { projectPath, config } = context;
  const configContent = `import type { KilatConfig } from 'kilat-core';

export default {
  // \u{1F3AF} Project Configuration
  name: "${config.name}",
  version: "0.1.0",
  platform: "${config.platform}",
  
  // \u{1F3A8} UI Theme
  theme: "${config.theme}",
  mode: "dark",
  
  // \u{1F30C} Animation Settings
  presetScene: "galaxy",
  animation: {
    autoRotate: true,
    background: "#000000",
    interactive: true,
    ambientSound: false
  },
  
  // \u{1F6E3}\uFE0F Router Configuration
  router: {
    basePath: "/",
    middleware: [],
    fileBasedRouting: true,
    dynamicRoutes: true,
    layoutNesting: true
  },
  
  // \u{1F5C4}\uFE0F Database Configuration
  database: {
    driver: "${config.database}",
    connection: ${config.database === "sqlite" ? `{
      sqlite: {
        file: "./data.db",
        enableWAL: true,
        timeout: 5000
      }
    }` : `{
      mysql: {
        host: "localhost",
        port: 3306,
        user: "root",
        password: "",
        database: "${config.name}"
      }
    }`},
    migrations: {
      directory: "./migrations",
      autoRun: true
    }
  },
  
  // \u{1F3D7}\uFE0F Build Configuration
  build: {
    engine: "kilatpack",
    target: "es2022",
    minify: true,
    sourcemap: true,
    debugOverlay: false,
    hotReload: true,
    analyze: false,
    outputDir: "dist",
    publicDir: "public",
    assetsDir: "assets"
  },
  
  // \u{1F680} Backend Configuration
  backend: {
    enabled: ${config.platform === "fullstack" || config.features.includes("api")},
    port: 8080,
    autoStart: true,
    apiPrefix: "/api"
  },
  
  // \u{1F50C} Plugin Configuration
  plugins: {${config.plugins.map((plugin) => `
    "${plugin}": {
      version: "^1.0.0",
      enabled: true
    }`).join(",")}
  },
  
  // \u{1F916} AI Assistant
  aiAssistant: {
    enabled: ${config.plugins.includes("ai")},
    endpoint: "/api/ai",
    model: "gpt-4",
    features: []
  },
  
  // \u{1F4F1} Platform Configuration
  platform: {
    web: {
      enabled: ${config.platform === "web" || config.platform === "fullstack"},
      ssr: ${config.features.includes("ssr")},
      pwa: ${config.features.includes("pwa")}
    },
    desktop: {
      enabled: ${config.platform === "desktop"},
      electron: true,
      tauri: false
    },
    mobile: {
      enabled: ${config.platform === "mobile"},
      expo: true,
      capacitor: false
    }
  },
  
  // \u{1F527} Development Configuration
  dev: {
    port: 3000,
    host: "localhost",
    open: true,
    cors: true,
    proxy: {},
    hmr: {
      port: 24678,
      overlay: true
    }
  },
  
  // \u26A1 Performance Configuration
  performance: {
    bundleSplitting: true,
    treeshaking: true,
    compression: "gzip",
    caching: {
      enabled: true,
      strategy: "stale-while-revalidate"
    }
  },
  
  // \u{1F512} Security Configuration
  security: {
    csp: {
      enabled: true,
      directives: {
        "default-src": ["'self'"],
        "script-src": ["'self'", "'unsafe-inline'"],
        "style-src": ["'self'", "'unsafe-inline'"],
        "img-src": ["'self'", "data:", "https:"]
      }
    },
    cors: {
      origin: ["http://localhost:3000"],
      credentials: true
    }
  },
  
  // \u{1F6E1}\uFE0F Error Handling
  errorHandling: {
    killSwitch: true,
    safeMode: true,
    crashReport: {
      enabled: true,
      includeLogs: true,
      autoRetry: true
    }
  }
} satisfies KilatConfig;
`;
  await writeFile(join(projectPath, "kilat.config.ts"), configContent);
}
async function generateAppFiles(context) {
  const { projectPath, config } = context;
  const appContent = generateAppComponent(config);
  await writeFile(join(projectPath, "src/App.tsx"), appContent);
  const mainContent = generateMainEntry(config);
  await writeFile(join(projectPath, "src/main.tsx"), mainContent);
  const htmlContent = generateIndexHtml(config);
  await writeFile(join(projectPath, "index.html"), htmlContent);
  const tsConfigContent = generateTsConfig(config);
  await writeFile(join(projectPath, "tsconfig.json"), tsConfigContent);
  const viteConfigContent = generateViteConfig(config);
  await writeFile(join(projectPath, "vite.config.ts"), viteConfigContent);
}
function generateAppComponent(config) {
  return `import React from 'react';
import { KilatProvider } from 'kilat-core';
import { KilatScene } from 'kilatanim.js';
import './styles/main.css';

function App() {
  return (
    <KilatProvider theme="${config.theme}">
      <div className="k-min-h-screen k-bg-background k-text-text">
        {/* \u{1F30C} 3D Background Animation */}
        <div className="k-fixed k-inset-0 k-z-0">
          <KilatScene 
            preset="galaxy" 
            interactive={true}
            autoRotate={true}
            background="#000000"
          />
        </div>
        
        {/* \u{1F4F1} Main Content */}
        <div className="k-relative k-z-10">
          <header className="k-p-8 k-text-center">
            <h1 className="k-text-4xl k-font-bold k-text-glow-${config.theme === "cyberpunk" ? "cyber" : "primary"} k-mb-4">
              \u26A1 Welcome to ${config.name}
            </h1>
            <p className="k-text-xl k-text-muted k-mb-8">
              Built with Kilat.js - Framework Glow Futuristik Nusantara
            </p>
          </header>
          
          <main className="k-container k-mx-auto k-px-4">
            <div className="k-grid k-grid-cols-1 k-md:grid-cols-3 k-gap-6">
              {/* Feature Cards */}
              <div className="k-card-${config.theme} k-p-6">
                <h3 className="k-text-xl k-font-semibold k-mb-3 k-text-glow-primary">
                  \u{1F3A8} ${config.theme.charAt(0).toUpperCase() + config.theme.slice(1)} Theme
                </h3>
                <p className="k-text-muted">
                  Beautiful ${config.theme} UI with glow effects and smooth animations.
                </p>
              </div>
              
              <div className="k-card-${config.theme} k-p-6">
                <h3 className="k-text-xl k-font-semibold k-mb-3 k-text-glow-secondary">
                  \u{1F30C} 3D Animations
                </h3>
                <p className="k-text-muted">
                  Interactive 3D scenes powered by Three.js and KilatAnim.js.
                </p>
              </div>
              
              <div className="k-card-${config.theme} k-p-6">
                <h3 className="k-text-xl k-font-semibold k-mb-3 k-text-glow-accent">
                  \u26A1 Fast Development
                </h3>
                <p className="k-text-muted">
                  Hot reload, TypeScript, and modern tooling for rapid development.
                </p>
              </div>
            </div>
            
            <div className="k-text-center k-mt-12">
              <button className="k-btn-${config.theme} k-px-8 k-py-3 k-text-lg">
                Get Started
              </button>
            </div>
          </main>
        </div>
      </div>
    </KilatProvider>
  );
}

export default App;
`;
}
function generateMainEntry(config) {
  return `import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

// Initialize Kilat.js
import 'kilatcss/dist/kilat.css';
import 'kilatcss/dist/themes/${config.theme}.css';

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
`;
}
function generateIndexHtml(config) {
  return `<!DOCTYPE html>
<html lang="en" data-kilat-theme="${config.theme}">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/kilat-logo.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>${config.name} - Kilat.js App</title>
    <meta name="description" content="Built with Kilat.js - Framework Glow Futuristik Nusantara" />
  </head>
  <body class="k-${config.theme}">
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
`;
}
function generateTsConfig(config) {
  return JSON.stringify({
    compilerOptions: {
      target: "ES2022",
      lib: ["ES2022", "DOM", "DOM.Iterable"],
      module: "ESNext",
      skipLibCheck: true,
      moduleResolution: "bundler",
      allowImportingTsExtensions: true,
      resolveJsonModule: true,
      isolatedModules: true,
      noEmit: true,
      jsx: "react-jsx",
      strict: true,
      noUnusedLocals: true,
      noUnusedParameters: true,
      noFallthroughCasesInSwitch: true,
      baseUrl: ".",
      paths: {
        "@/*": ["./src/*"],
        "@/components/*": ["./src/components/*"],
        "@/pages/*": ["./src/pages/*"],
        "@/utils/*": ["./src/utils/*"]
      }
    },
    include: ["src"],
    references: [{ path: "./tsconfig.node.json" }]
  }, null, 2);
}
function generateViteConfig(config) {
  return `import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@/components': resolve(__dirname, './src/components'),
      '@/pages': resolve(__dirname, './src/pages'),
      '@/utils': resolve(__dirname, './src/utils')
    }
  },
  server: {
    port: 3000,
    host: true,
    open: true
  },
  build: {
    target: 'es2022',
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          kilat: ['kilat-core', 'kilatcss', 'kilatanim.js']
        }
      }
    }
  }
});
`;
}
async function generateFeatureFiles(context) {
  const { projectPath, config } = context;
  if (config.features.includes("auth")) {
    await generateAuthFiles(projectPath);
  }
  if (config.platform === "fullstack" || config.features.includes("api")) {
    await generateApiFiles(projectPath);
  }
  if (config.features.includes("testing")) {
    await generateTestFiles(projectPath);
  }
  if (config.features.includes("docker")) {
    await generateDockerFiles(projectPath);
  }
}
async function generateAuthFiles(projectPath) {
  await writeFile(
    join(projectPath, "src/utils/auth.ts"),
    "// Auth utilities will be generated here"
  );
}
async function generateApiFiles(projectPath) {
  await writeFile(
    join(projectPath, "api/routes/health.ts"),
    `export async function GET() {
  return Response.json({ status: 'healthy', timestamp: new Date().toISOString() });
}`
  );
}
async function generateTestFiles(projectPath) {
  await writeFile(
    join(projectPath, "src/App.test.tsx"),
    `import { render, screen } from '@testing-library/react';
import App from './App';

test('renders welcome message', () => {
  render(<App />);
  const welcomeElement = screen.getByText(/Welcome to/i);
  expect(welcomeElement).toBeInTheDocument();
});`
  );
}
async function generateDockerFiles(projectPath) {
  await writeFile(
    join(projectPath, "Dockerfile"),
    `FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]`
  );
}
async function copyStaticAssets(context) {
  const { projectPath } = context;
  await writeFile(
    join(projectPath, "public/kilat-logo.svg"),
    '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y="50" font-size="50">\u26A1</text></svg>'
  );
}
function getDependenciesForPlatform(platform) {
  const deps = {};
  switch (platform) {
    case "web":
    case "fullstack":
      deps.react = "^18.2.0";
      deps["react-dom"] = "^18.2.0";
      break;
    case "desktop":
      deps.electron = "^27.0.0";
      break;
    case "mobile":
      deps.expo = "^49.0.0";
      deps["react-native"] = "^0.72.0";
      break;
  }
  return deps;
}
function getDependenciesForFeatures(features) {
  const deps = {};
  if (features.includes("auth")) {
    deps.jsonwebtoken = "^9.0.0";
    deps.bcryptjs = "^2.4.3";
  }
  if (features.includes("testing")) {
    deps["@testing-library/react"] = "^13.4.0";
    deps["@testing-library/jest-dom"] = "^6.1.0";
  }
  return deps;
}
function getDependenciesForPlugins(plugins) {
  const deps = {};
  plugins.forEach((plugin) => {
    deps[`kilat-plugin-${plugin}`] = "^1.0.0";
  });
  return deps;
}

// src/utils/validation.ts
var import_semver = __toESM(require_semver2(), 1);
import { pathExists as pathExists3, readFile as readFile3 } from "fs-extra";
function validateProjectName(name) {
  if (!name) {
    return "Project name is required";
  }
  if (name.length < 1) {
    return "Project name must be at least 1 character";
  }
  if (name.length > 214) {
    return "Project name must be less than 214 characters";
  }
  if (name.toLowerCase() !== name) {
    return "Project name must be lowercase";
  }
  if (/^[._]/.test(name)) {
    return "Project name cannot start with . or _";
  }
  if (!/^[a-z0-9._-]+$/.test(name)) {
    return "Project name can only contain lowercase letters, numbers, dots, hyphens, and underscores";
  }
  const reservedNames = [
    "node_modules",
    "favicon.ico",
    "package.json",
    "package-lock.json",
    "yarn.lock",
    "pnpm-lock.yaml",
    "bun.lockb",
    ".git",
    ".gitignore",
    ".env",
    "dist",
    "build",
    "public",
    "src",
    "index",
    "main",
    "app",
    "www",
    "web",
    "api",
    "server",
    "client",
    "admin",
    "dashboard",
    "config",
    "lib",
    "libs",
    "utils",
    "helpers",
    "components",
    "pages",
    "routes",
    "middleware",
    "plugins",
    "assets",
    "static",
    "uploads",
    "temp",
    "tmp",
    "cache",
    "logs",
    "test",
    "tests",
    "spec",
    "specs",
    "docs",
    "documentation"
  ];
  if (reservedNames.includes(name)) {
    return `Project name "${name}" is reserved`;
  }
  const npmReserved = [
    "kilat",
    "kilatjs",
    "kilat-core",
    "kilatcss",
    "kilatanim",
    "kilat-router",
    "kilat-cli",
    "kilat-backend",
    "kilat-db"
  ];
  if (npmReserved.includes(name)) {
    return `Project name "${name}" conflicts with Kilat.js package names`;
  }
  return true;
}
async function checkDependencies() {
  const issues = [];
  try {
    if (!await pathExists3("package.json")) {
      issues.push("package.json not found");
      return issues;
    }
    const packageJson = JSON.parse(await readFile3("package.json", "utf-8"));
    const dependencies = {
      ...packageJson.dependencies || {},
      ...packageJson.devDependencies || {}
    };
    const requiredPackages = [
      "kilat-core",
      "kilatcss",
      "kilat-router"
    ];
    for (const pkg of requiredPackages) {
      if (!dependencies[pkg]) {
        issues.push(`Missing required package: ${pkg}`);
      }
    }
    const kilatCoreVersion = dependencies["kilat-core"];
    if (kilatCoreVersion && !import_semver.default.satisfies("1.0.0", kilatCoreVersion)) {
      issues.push(`Incompatible kilat-core version: ${kilatCoreVersion}`);
    }
    const conflictingPackages = [
      "create-react-app",
      "vue-cli",
      "angular-cli",
      "@nuxt/cli"
    ];
    for (const pkg of conflictingPackages) {
      if (dependencies[pkg]) {
        issues.push(`Conflicting package detected: ${pkg}`);
      }
    }
    const nodeVersion = process.version;
    if (!import_semver.default.gte(nodeVersion, "18.0.0")) {
      issues.push(`Node.js version ${nodeVersion} is not supported. Minimum required: 18.0.0`);
    }
  } catch (error) {
    issues.push(`Failed to check dependencies: ${error.message}`);
  }
  return issues;
}
async function validateProjectStructure() {
  const issues = [];
  const requiredFiles = [
    "package.json",
    "kilat.config.ts"
  ];
  for (const file of requiredFiles) {
    if (!await pathExists3(file)) {
      issues.push(`Missing required file: ${file}`);
    }
  }
  const requiredDirs = [
    "src",
    "public"
  ];
  for (const dir of requiredDirs) {
    if (!await pathExists3(dir)) {
      issues.push(`Missing required directory: ${dir}`);
    }
  }
  if (await pathExists3("node_modules") && !(await pathExists3("package-lock.json") || await pathExists3("yarn.lock") || await pathExists3("pnpm-lock.yaml") || await pathExists3("bun.lockb"))) {
    issues.push("node_modules exists but no lock file found");
  }
  return issues;
}
async function validateConfiguration() {
  const issues = [];
  try {
    if (await pathExists3("kilat.config.ts")) {
      const { loadKilatConfig: loadKilatConfig2, validateKilatConfig: validateKilatConfig2 } = await Promise.resolve().then(() => (init_config(), config_exports));
      const config = await loadKilatConfig2();
      const configIssues = validateKilatConfig2(config);
      issues.push(...configIssues);
    }
    const packageJson = JSON.parse(await readFile3("package.json", "utf-8"));
    const scripts = packageJson.scripts || {};
    const recommendedScripts = [
      "dev",
      "build",
      "start",
      "test"
    ];
    for (const script of recommendedScripts) {
      if (!scripts[script]) {
        issues.push(`Missing recommended script: ${script}`);
      }
    }
  } catch (error) {
    issues.push(`Configuration validation failed: ${error.message}`);
  }
  return issues;
}
async function validateEnvironment() {
  const issues = [];
  const nodeVersion = process.version;
  if (!import_semver.default.gte(nodeVersion, "18.0.0")) {
    issues.push(`Node.js ${nodeVersion} is not supported. Please upgrade to 18.0.0 or higher.`);
  }
  const { execa: execa2 } = await Promise.resolve().then(() => (init_execa(), execa_exports));
  const tools = [
    { name: "git", command: "git --version" },
    { name: "node", command: "node --version" }
  ];
  for (const tool of tools) {
    try {
      await execa2(tool.name, ["--version"], { stdio: "pipe" });
    } catch {
      issues.push(`${tool.name} is not installed or not in PATH`);
    }
  }
  const { getAvailablePackageManagers: getAvailablePackageManagers2 } = await Promise.resolve().then(() => (init_package_manager(), package_manager_exports));
  const availableManagers = await getAvailablePackageManagers2();
  if (availableManagers.length === 0) {
    issues.push("No package manager found (npm, yarn, pnpm, or bun)");
  }
  try {
    const { statSync } = await import("fs");
    const stats = statSync(".");
  } catch {
  }
  return issues;
}
async function validateSecurity() {
  const issues = [];
  try {
    if (await pathExists3(".git") && await pathExists3(".env")) {
      const { execa: execa2 } = await Promise.resolve().then(() => (init_execa(), execa_exports));
      try {
        const result = await execa2("git", ["ls-files", ".env"], { stdio: "pipe" });
        if (result.stdout.includes(".env")) {
          issues.push(".env file is tracked by git (security risk)");
        }
      } catch {
      }
    }
    if (await pathExists3("kilat.config.ts")) {
      const configContent = await readFile3("kilat.config.ts", "utf-8");
      const secretPatterns = [
        /password\s*[:=]\s*['"][^'"]+['"]/i,
        /secret\s*[:=]\s*['"][^'"]+['"]/i,
        /key\s*[:=]\s*['"][^'"]+['"]/i,
        /token\s*[:=]\s*['"][^'"]+['"]/i
      ];
      for (const pattern of secretPatterns) {
        if (pattern.test(configContent)) {
          issues.push("Potential hardcoded secrets found in configuration");
          break;
        }
      }
    }
    const packageJson = JSON.parse(await readFile3("package.json", "utf-8"));
    const scripts = packageJson.scripts || {};
    const dangerousPatterns = [
      /rm\s+-rf/,
      /sudo/,
      /curl.*\|.*sh/,
      /wget.*\|.*sh/
    ];
    for (const [scriptName, scriptContent] of Object.entries(scripts)) {
      for (const pattern of dangerousPatterns) {
        if (pattern.test(scriptContent)) {
          issues.push(`Potentially dangerous script detected: ${scriptName}`);
        }
      }
    }
  } catch (error) {
    issues.push(`Security validation failed: ${error.message}`);
  }
  return issues;
}
async function validatePerformance() {
  const issues = [];
  try {
    const packageJson = JSON.parse(await readFile3("package.json", "utf-8"));
    const dependencies = {
      ...packageJson.dependencies || {},
      ...packageJson.devDependencies || {}
    };
    const heavyPackages = [
      "lodash",
      // Suggest lodash-es instead
      "moment",
      // Suggest date-fns or dayjs instead
      "jquery",
      // Usually not needed in modern frameworks
      "bootstrap"
      // Suggest using kilatcss instead
    ];
    for (const pkg of heavyPackages) {
      if (dependencies[pkg]) {
        issues.push(`Heavy dependency detected: ${pkg} (consider alternatives)`);
      }
    }
    const duplicates = [
      ["axios", "fetch"],
      // Both for HTTP requests
      ["lodash", "ramda"],
      // Both for utility functions
      ["moment", "date-fns", "dayjs"]
      // All for date manipulation
    ];
    for (const group of duplicates) {
      const found = group.filter((pkg) => dependencies[pkg]);
      if (found.length > 1) {
        issues.push(`Duplicate functionality: ${found.join(", ")}`);
      }
    }
  } catch (error) {
    issues.push(`Performance validation failed: ${error.message}`);
  }
  return issues;
}
async function runAllValidations() {
  const [
    dependencies,
    structure,
    configuration,
    environment,
    security,
    performance
  ] = await Promise.all([
    checkDependencies(),
    validateProjectStructure(),
    validateConfiguration(),
    validateEnvironment(),
    validateSecurity(),
    validatePerformance()
  ]);
  return {
    dependencies,
    structure,
    configuration,
    environment,
    security,
    performance
  };
}

// src/commands/create.ts
async function createCommand(projectName, options = {}) {
  console.log(chalk.cyan.bold("\n\u26A1 Welcome to Kilat.js Project Generator!\n"));
  try {
    const config = await getProjectConfig(projectName, options);
    await createProject(config, options);
    console.log(chalk.green.bold("\n\u{1F389} Project created successfully!\n"));
    showNextSteps(config);
  } catch (error) {
    console.error(chalk.red.bold("\n\u274C Failed to create project:"), error.message);
    process.exit(1);
  }
}
async function getProjectConfig(projectName, options) {
  const questions = [];
  if (!projectName) {
    questions.push({
      type: "input",
      name: "name",
      message: "What is your project name?",
      default: "my-kilat-app",
      validate: validateProjectName
    });
  }
  if (!options.template) {
    questions.push({
      type: "list",
      name: "platform",
      message: "Which platform do you want to target?",
      choices: [
        { name: "\u{1F310} Web Application (React + Vite)", value: "web" },
        { name: "\u{1F5A5}\uFE0F  Desktop Application (Electron)", value: "desktop" },
        { name: "\u{1F4F1} Mobile Application (Expo)", value: "mobile" },
        { name: "\u{1F680} Fullstack Application (Web + API)", value: "fullstack" }
      ],
      default: "web"
    });
  }
  if (!options.theme) {
    questions.push({
      type: "list",
      name: "theme",
      message: "Choose your UI theme:",
      choices: [
        { name: "\u{1F52E} Cyberpunk - Neon glow futuristic", value: "cyberpunk" },
        { name: "\u{1F1EE}\u{1F1E9} Nusantara - Indonesian cultural colors", value: "nusantara" },
        { name: "\u{1F579}\uFE0F  Retro - 90s arcade pixel art", value: "retro" },
        { name: "\u{1F9F1} Material - Google Material Design", value: "material" },
        { name: "\u{1F9FC} Neumorphism - Soft shadow inset", value: "neumorphism" },
        { name: "\u{1F308} Aurora - Gradient blur pastels", value: "aurora" },
        { name: "\u26AA Minimalist - Clean flat UI", value: "minimalist" }
      ],
      default: "cyberpunk"
    });
  }
  if (!options.database) {
    questions.push({
      type: "list",
      name: "database",
      message: "Choose your database:",
      choices: [
        { name: "\u{1F4E6} SQLite - Local file database (recommended)", value: "sqlite" },
        { name: "\u{1F42C} MySQL - Production database", value: "mysql" },
        { name: "\u{1F6AB} None - No database", value: "none" }
      ],
      default: "sqlite"
    });
  }
  if (!options.features) {
    questions.push({
      type: "checkbox",
      name: "features",
      message: "Select additional features:",
      choices: [
        { name: "\u{1F510} Authentication (JWT + bcrypt)", value: "auth", checked: true },
        { name: "\u{1F310} Server-Side Rendering (SSR)", value: "ssr" },
        { name: "\u{1F4F1} Progressive Web App (PWA)", value: "pwa" },
        { name: "\u{1F9EA} Testing Setup (Vitest + Playwright)", value: "testing" },
        { name: "\u{1F433} Docker Configuration", value: "docker" },
        { name: "\u{1F680} CI/CD GitHub Actions", value: "ci-cd" },
        { name: "\u{1F4CA} Analytics & Monitoring", value: "monitoring" },
        { name: "\u{1F30D} Internationalization (i18n)", value: "i18n" }
      ]
    });
  }
  if (!options.plugins) {
    questions.push({
      type: "checkbox",
      name: "plugins",
      message: "Select plugins to install:",
      choices: [
        { name: "\u{1F4DD} CMS Plugin - Content management", value: "cms" },
        { name: "\u{1F4B3} Payments Plugin - Xendit, Midtrans", value: "payments" },
        { name: "\u{1F4E4} Upload Plugin - File handling", value: "upload", checked: true },
        { name: "\u{1F916} AI Assistant Plugin - GPT integration", value: "ai" },
        { name: "\u{1F4AC} Chat Plugin - Real-time messaging", value: "chat" },
        { name: "\u{1F4E7} Email Plugin - SMTP & templates", value: "email" }
      ]
    });
  }
  const answers = await inquirer.prompt(questions);
  return {
    name: projectName || answers.name,
    platform: options.template?.includes("web") ? "web" : options.template?.includes("desktop") ? "desktop" : options.template?.includes("mobile") ? "mobile" : options.template?.includes("fullstack") ? "fullstack" : answers.platform,
    theme: options.theme || answers.theme,
    database: options.database || answers.database,
    plugins: options.plugins || answers.plugins || [],
    features: options.features || answers.features || [],
    template: options.template || `kilat-${answers.platform}`,
    directory: join3(process.cwd(), projectName || answers.name)
  };
}
async function createProject(config, options) {
  const spinner = ora2("Creating project structure...").start();
  try {
    if (await pathExists4(config.directory)) {
      if (!options.force) {
        spinner.fail("Directory already exists. Use --force to overwrite.");
        process.exit(1);
      }
    }
    await ensureDir2(config.directory);
    spinner.text = "Setting up project structure...";
    const template = await getTemplate(config.template, config.platform);
    const context = {
      projectName: config.name,
      projectPath: config.directory,
      template,
      config,
      packageManager: await detectPackageManager(),
      variables: {
        projectName: config.name,
        theme: config.theme,
        database: config.database,
        features: config.features,
        plugins: config.plugins,
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        kilatVersion: "1.0.0"
      }
    };
    await generateProjectFiles(context);
    spinner.text = "Installing dependencies...";
    if (!options.dry) {
      await installDependencies(config.directory, context.packageManager);
    }
    if (!options.dry) {
      await initGitRepository(config.directory);
    }
    await runPostInstallScripts(config, context);
    spinner.succeed("Project created successfully!");
  } catch (error) {
    spinner.fail("Failed to create project");
    throw error;
  }
}
async function getTemplate(templateName, platform) {
  const baseTemplate = {
    name: templateName,
    description: `Kilat.js ${platform} application`,
    platform,
    features: [],
    dependencies: [
      "kilat-core",
      "kilatcss",
      "kilatanim.js",
      "kilat-router",
      "kilat-utils"
    ],
    devDependencies: [
      "typescript",
      "vite",
      "@types/node"
    ],
    scripts: {
      "dev": "kilat dev",
      "build": "kilat build",
      "start": "kilat start",
      "test": "kilat test",
      "lint": "kilat lint"
    },
    files: []
  };
  switch (platform) {
    case "web":
      baseTemplate.dependencies.push("react", "react-dom");
      baseTemplate.devDependencies.push("@types/react", "@types/react-dom");
      break;
    case "desktop":
      baseTemplate.dependencies.push("electron");
      baseTemplate.devDependencies.push("@types/electron");
      break;
    case "mobile":
      baseTemplate.dependencies.push("expo", "react-native");
      break;
    case "fullstack":
      baseTemplate.dependencies.push("kilat-backend", "kilat-db");
      break;
  }
  return baseTemplate;
}
async function initGitRepository(projectPath) {
  try {
    await execa("git", ["init"], { cwd: projectPath });
    await execa("git", ["add", "."], { cwd: projectPath });
    await execa("git", ["commit", "-m", "Initial commit from Kilat.js CLI"], { cwd: projectPath });
  } catch (error) {
    console.log(chalk.yellow("\u26A0\uFE0F  Could not initialize git repository"));
  }
}
async function runPostInstallScripts(config, context) {
  if (config.database !== "none") {
    const spinner = ora2("Setting up database...").start();
    try {
      await createDatabaseConfig(config, context);
      if (config.database === "sqlite") {
        await execa("kilat", ["db", "migrate"], { cwd: config.directory });
      }
      spinner.succeed("Database configured");
    } catch (error) {
      spinner.warn("Database setup skipped (can be configured later)");
    }
  }
  if (config.plugins.length > 0) {
    const spinner = ora2("Installing plugins...").start();
    try {
      for (const plugin of config.plugins) {
        await execa("kilat", ["plugin", "add", plugin], { cwd: config.directory });
      }
      spinner.succeed("Plugins installed");
    } catch (error) {
      spinner.warn("Some plugins could not be installed");
    }
  }
}
async function createDatabaseConfig(config, context) {
  const dbConfig = {
    driver: config.database,
    connection: config.database === "sqlite" ? { sqlite: { file: "./data.db", enableWAL: true } } : { mysql: { host: "localhost", port: 3306, user: "root", password: "", database: config.name } },
    migrations: {
      directory: "./migrations",
      autoRun: true
    }
  };
  const configPath = join3(config.directory, "kilat.config.ts");
}
function showNextSteps(config) {
  console.log(chalk.cyan.bold("\u{1F680} Next Steps:\n"));
  console.log(chalk.white(`1. Navigate to your project:`));
  console.log(chalk.gray(`   cd ${config.name}
`));
  console.log(chalk.white(`2. Start development server:`));
  console.log(chalk.gray(`   kilat dev
`));
  console.log(chalk.white(`3. Open in browser:`));
  console.log(chalk.gray(`   http://localhost:3000
`));
  if (config.database !== "none") {
    console.log(chalk.white(`4. Configure database:`));
    console.log(chalk.gray(`   kilat db setup
`));
  }
  if (config.plugins.length > 0) {
    console.log(chalk.white(`5. Configure plugins:`));
    config.plugins.forEach((plugin) => {
      console.log(chalk.gray(`   kilat plugin configure ${plugin}`));
    });
    console.log();
  }
  console.log(chalk.cyan("\u{1F4DA} Documentation: https://kilat-js.pcode.my.id/docs"));
  console.log(chalk.cyan("\u{1F4AC} Community: https://github.com/kangpcode/kilatjs/discussions"));
  console.log(chalk.cyan("\u{1F41B} Issues: https://github.com/kangpcode/kilatjs/issues\n"));
}

// src/commands/dev.ts
init_config();
import chalk2 from "chalk";
import ora3 from "ora";
import { spawn } from "child_process";
import { pathExists as pathExists5 } from "fs-extra";
async function devCommand(options = {}) {
  console.log(chalk2.cyan.bold("\n\u26A1 Starting Kilat.js Development Server\n"));
  try {
    await validateProject();
    const config = await loadKilatConfig();
    await checkProjectDependencies();
    await startDevelopmentServices(config, options);
  } catch (error) {
    console.error(chalk2.red.bold("\n\u274C Failed to start development server:"), error.message);
    process.exit(1);
  }
}
async function validateProject() {
  const spinner = ora3("Validating project structure...").start();
  try {
    const configExists = await pathExists5("./kilat.config.ts") || await pathExists5("./kilat.config.js");
    if (!configExists) {
      throw new Error('Not a Kilat.js project. Run "kilat create" to create a new project.');
    }
    const packageJsonExists = await pathExists5("./package.json");
    if (!packageJsonExists) {
      throw new Error("package.json not found. This doesn't appear to be a valid Node.js project.");
    }
    spinner.succeed("Project structure validated");
  } catch (error) {
    spinner.fail("Project validation failed");
    throw error;
  }
}
async function checkProjectDependencies() {
  const spinner = ora3("Checking dependencies...").start();
  try {
    const issues = await checkDependencies();
    if (issues.length > 0) {
      spinner.warn("Some dependencies have issues");
      console.log(chalk2.yellow("\n\u26A0\uFE0F  Dependency Issues:"));
      issues.forEach((issue) => {
        console.log(chalk2.yellow(`   \u2022 ${issue}`));
      });
      console.log(chalk2.gray('\n   Run "kilat doctor" for detailed diagnostics\n'));
    } else {
      spinner.succeed("Dependencies checked");
    }
  } catch (error) {
    spinner.fail("Dependency check failed");
    throw error;
  }
}
async function startDevelopmentServices(config, options) {
  const services = [];
  services.push({
    name: "Frontend",
    command: "vite",
    args: ["--host", options.host || "localhost", "--port", (options.port || 3e3).toString()],
    color: "cyan",
    icon: "\u{1F310}"
  });
  if (config.backend?.enabled) {
    services.push({
      name: "Backend",
      command: "kilat-backend",
      args: ["dev", "--port", (config.backend.port || 8080).toString()],
      color: "green",
      icon: "\u{1F680}"
    });
  }
  if (config.database?.driver === "sqlite") {
    services.push({
      name: "Database",
      command: "echo",
      args: ["SQLite database ready"],
      color: "blue",
      icon: "\u{1F5C4}\uFE0F"
    });
  }
  console.log(chalk2.cyan("\u{1F504} Starting development services...\n"));
  const processes = [];
  for (const service of services) {
    const spinner = ora3(`Starting ${service.name}...`).start();
    try {
      const process7 = spawn(service.command, service.args, {
        stdio: ["pipe", "pipe", "pipe"],
        shell: true,
        cwd: process7.cwd()
      });
      process7.stdout?.on("data", (data) => {
        const output = data.toString().trim();
        if (output) {
          console.log(chalk2[service.color](`${service.icon} [${service.name}] ${output}`));
        }
      });
      process7.stderr?.on("data", (data) => {
        const output = data.toString().trim();
        if (output && !output.includes("warning")) {
          console.log(chalk2.red(`${service.icon} [${service.name}] ${output}`));
        }
      });
      process7.on("close", (code) => {
        if (code !== 0) {
          console.log(chalk2.red(`${service.icon} [${service.name}] Process exited with code ${code}`));
        }
      });
      processes.push({ name: service.name, process: process7 });
      spinner.succeed(`${service.name} started`);
      await new Promise((resolve) => setTimeout(resolve, 1e3));
    } catch (error) {
      spinner.fail(`Failed to start ${service.name}`);
      console.error(chalk2.red(`Error: ${error.message}`));
    }
  }
  showDevelopmentInfo(config, options);
  setupGracefulShutdown(processes);
  await new Promise(() => {
  });
}
function showDevelopmentInfo(config, options) {
  const host = options.host || "localhost";
  const port = options.port || 3e3;
  const backendPort = config.backend?.port || 8080;
  console.log(chalk2.green.bold("\n\u2705 Development server is ready!\n"));
  console.log(chalk2.white("\u{1F310} Frontend:"));
  console.log(chalk2.cyan(`   Local:    http://${host}:${port}`));
  console.log(chalk2.cyan(`   Network:  http://*************:${port}`));
  if (config.backend?.enabled) {
    console.log(chalk2.white("\n\u{1F680} Backend API:"));
    console.log(chalk2.green(`   Local:    http://${host}:${backendPort}${config.backend.apiPrefix || "/api"}`));
    console.log(chalk2.green(`   Health:   http://${host}:${backendPort}${config.backend.apiPrefix || "/api"}/health`));
  }
  if (config.database?.driver) {
    console.log(chalk2.white("\n\u{1F5C4}\uFE0F  Database:"));
    console.log(chalk2.blue(`   Driver:   ${config.database.driver}`));
    if (config.database.driver === "sqlite") {
      console.log(chalk2.blue(`   File:     ${config.database.connection.sqlite?.file || "./data.db"}`));
    }
  }
  console.log(chalk2.white("\n\u{1F6E0}\uFE0F  Development Tools:"));
  console.log(chalk2.gray(`   Config:   kilat.config.ts`));
  console.log(chalk2.gray(`   Logs:     .kilat/logs/`));
  console.log(chalk2.gray(`   Doctor:   kilat doctor`));
  console.log(chalk2.yellow("\n\u26A1 Press Ctrl+C to stop all services\n"));
  showDevelopmentTips(config);
}
function showDevelopmentTips(config) {
  const tips = [
    '\u{1F4A1} Use "kilat generate component MyComponent" to create new components',
    '\u{1F4A1} Use "kilat db migrate" to run database migrations',
    '\u{1F4A1} Use "kilat plugin list" to see available plugins',
    '\u{1F4A1} Use "kilat build --analyze" to analyze bundle size'
  ];
  if (config.features?.includes("testing")) {
    tips.push('\u{1F4A1} Use "kilat test" to run your test suite');
  }
  if (config.plugins?.includes("cms")) {
    tips.push("\u{1F4A1} Access CMS admin at /admin");
  }
  console.log(chalk2.cyan("\u{1F4A1} Development Tips:"));
  tips.slice(0, 3).forEach((tip) => {
    console.log(chalk2.gray(`   ${tip}`));
  });
  console.log();
}
function setupGracefulShutdown(processes) {
  const shutdown = async (signal) => {
    console.log(chalk2.yellow(`
\u{1F6D1} Received ${signal}, shutting down gracefully...`));
    for (const { name, process: process7 } of processes) {
      try {
        console.log(chalk2.gray(`   Stopping ${name}...`));
        process7.kill("SIGTERM");
        setTimeout(() => {
          if (!process7.killed) {
            process7.kill("SIGKILL");
          }
        }, 5e3);
      } catch (error) {
        console.log(chalk2.red(`   Failed to stop ${name}: ${error.message}`));
      }
    }
    console.log(chalk2.green("\u2705 All services stopped"));
    process.exit(0);
  };
  process.on("SIGINT", () => shutdown("SIGINT"));
  process.on("SIGTERM", () => shutdown("SIGTERM"));
  if (process.platform === "win32") {
    const rl = __require("readline").createInterface({
      input: process.stdin,
      output: process.stdout
    });
    rl.on("SIGINT", () => {
      process.emit("SIGINT");
    });
  }
}

// src/commands/build.ts
init_execa();
init_config();
import chalk3 from "chalk";
import ora4 from "ora";
import { join as join4 } from "path";
import { pathExists as pathExists6, remove, ensureDir as ensureDir3, stat } from "fs-extra";

// src/utils/format.ts
function formatBytes(bytes, decimals = 2) {
  if (bytes === 0)
    return "0 Bytes";
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
}
function formatDuration(ms) {
  if (ms < 1e3) {
    return `${ms}ms`;
  }
  const seconds = Math.floor(ms / 1e3);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  if (hours > 0) {
    const remainingMinutes = minutes % 60;
    const remainingSeconds = seconds % 60;
    return `${hours}h ${remainingMinutes}m ${remainingSeconds}s`;
  }
  if (minutes > 0) {
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  }
  return `${seconds}s`;
}
function formatPercentage(value, total, decimals = 1) {
  if (total === 0)
    return "0%";
  const percentage = value / total * 100;
  return `${percentage.toFixed(decimals)}%`;
}
function formatNumber(num) {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}
function formatDate(date, format = "short") {
  const d = typeof date === "string" ? new Date(date) : date;
  switch (format) {
    case "short":
      return d.toLocaleDateString();
    case "long":
      return d.toLocaleString();
    case "relative":
      return formatRelativeTime(d);
    default:
      return d.toISOString();
  }
}
function formatRelativeTime(date) {
  const now = /* @__PURE__ */ new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffSeconds = Math.floor(diffMs / 1e3);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);
  if (diffSeconds < 60) {
    return "just now";
  } else if (diffMinutes < 60) {
    return `${diffMinutes} minute${diffMinutes !== 1 ? "s" : ""} ago`;
  } else if (diffHours < 24) {
    return `${diffHours} hour${diffHours !== 1 ? "s" : ""} ago`;
  } else if (diffDays < 7) {
    return `${diffDays} day${diffDays !== 1 ? "s" : ""} ago`;
  } else {
    return date.toLocaleDateString();
  }
}
function formatPath(path3, maxLength = 50) {
  if (path3.length <= maxLength) {
    return path3;
  }
  const parts = path3.split("/");
  if (parts.length <= 2) {
    return `...${path3.slice(-(maxLength - 3))}`;
  }
  let result = parts[0];
  let remaining = parts.slice(1);
  while (remaining.length > 0 && result.length + remaining[remaining.length - 1].length + 4 <= maxLength) {
    result = `${result}/.../${remaining.pop()}`;
    if (remaining.length === 0) {
      result = result.replace("/.../", "/");
      break;
    }
  }
  return result;
}
function formatCommand(command, args = []) {
  const fullCommand = [command, ...args].join(" ");
  return `$ ${fullCommand}`;
}
function formatList(items, bullet = "\u2022") {
  return items.map((item) => `  ${bullet} ${item}`).join("\n");
}
function formatTable(data, options = {}) {
  if (data.length === 0)
    return "";
  const headers = options.headers || Object.keys(data[0]);
  const maxWidth = options.maxWidth || 80;
  const align = options.align || "left";
  const columnWidths = {};
  headers.forEach((header) => {
    columnWidths[header] = Math.max(
      header.length,
      ...data.map((row) => String(row[header] || "").length)
    );
  });
  const totalWidth = Object.values(columnWidths).reduce((sum, width) => sum + width, 0) + (headers.length - 1) * 3;
  if (totalWidth > maxWidth) {
    const scale = (maxWidth - (headers.length - 1) * 3) / (totalWidth - (headers.length - 1) * 3);
    headers.forEach((header) => {
      columnWidths[header] = Math.floor(columnWidths[header] * scale);
    });
  }
  const formatRow = (row) => {
    return headers.map((header) => {
      const value = String(row[header] || "");
      const width = columnWidths[header];
      if (value.length > width) {
        return value.slice(0, width - 3) + "...";
      }
      switch (align) {
        case "center":
          return value.padStart((width + value.length) / 2).padEnd(width);
        case "right":
          return value.padStart(width);
        default:
          return value.padEnd(width);
      }
    }).join(" | ");
  };
  const headerRow = formatRow(Object.fromEntries(headers.map((h) => [h, h])));
  const separator = headers.map((header) => "-".repeat(columnWidths[header])).join("-|-");
  const dataRows = data.map(formatRow);
  return [headerRow, separator, ...dataRows].join("\n");
}
function formatVersion(version, prefix = "v") {
  return version.startsWith(prefix) ? version : `${prefix}${version}`;
}
function formatUrl(url, maxLength = 50) {
  if (url.length <= maxLength) {
    return url;
  }
  try {
    const urlObj = new URL(url);
    const domain = urlObj.hostname;
    const path3 = urlObj.pathname + urlObj.search;
    if (domain.length + 10 >= maxLength) {
      return `${domain.slice(0, maxLength - 6)}...`;
    }
    const availableLength = maxLength - domain.length - 3;
    if (path3.length > availableLength) {
      return `${domain}...${path3.slice(-(availableLength - 3))}`;
    }
    return url;
  } catch {
    return url.slice(0, maxLength - 3) + "...";
  }
}
function formatProgressBar(current, total, width = 20, chars = { complete: "\u2588", incomplete: "\u2591" }) {
  const percentage = Math.min(current / total, 1);
  const completed = Math.floor(percentage * width);
  const remaining = width - completed;
  const bar = chars.complete.repeat(completed) + chars.incomplete.repeat(remaining);
  const percent = formatPercentage(current, total, 0);
  return `${bar} ${percent}`;
}
function formatStatus(status) {
  const icons = {
    success: "\u2705",
    error: "\u274C",
    warning: "\u26A0\uFE0F",
    info: "\u2139\uFE0F",
    pending: "\u23F3"
  };
  return icons[status] || "\u2022";
}
function formatCase(str, caseType) {
  const words = str.replace(/[^a-zA-Z0-9]/g, " ").split(/\s+/).filter(Boolean);
  switch (caseType) {
    case "camel":
      return words[0].toLowerCase() + words.slice(1).map((w) => w.charAt(0).toUpperCase() + w.slice(1).toLowerCase()).join("");
    case "pascal":
      return words.map((w) => w.charAt(0).toUpperCase() + w.slice(1).toLowerCase()).join("");
    case "snake":
      return words.map((w) => w.toLowerCase()).join("_");
    case "kebab":
      return words.map((w) => w.toLowerCase()).join("-");
    case "constant":
      return words.map((w) => w.toUpperCase()).join("_");
    default:
      return str;
  }
}
function formatCodeBlock(code, language = "") {
  const lines = code.split("\n");
  const maxLineLength = Math.max(...lines.map((line) => line.length));
  const border = "\u2500".repeat(Math.min(maxLineLength + 4, 80));
  return [
    `\u250C${border}\u2510`,
    ...lines.map((line) => `\u2502 ${line.padEnd(maxLineLength)} \u2502`),
    `\u2514${border}\u2518`
  ].join("\n");
}
function formatDiff(oldValue, newValue) {
  if (oldValue === newValue) {
    return `  ${oldValue}`;
  }
  return [
    `- ${oldValue}`,
    `+ ${newValue}`
  ].join("\n");
}

// src/commands/build.ts
async function buildCommand(options = {}) {
  console.log(chalk3.cyan.bold("\n\u26A1 Building Kilat.js Project for Production\n"));
  const startTime = Date.now();
  try {
    const config = await loadKilatConfig();
    if (!options.watch) {
      await cleanBuildDirectory(config);
    }
    await runBuildProcess(config, options);
    await showBuildSummary(config, startTime);
    console.log(chalk3.green.bold("\n\u{1F389} Build completed successfully!\n"));
  } catch (error) {
    console.error(chalk3.red.bold("\n\u274C Build failed:"), error.message);
    process.exit(1);
  }
}
async function cleanBuildDirectory(config) {
  const spinner = ora4("Cleaning build directory...").start();
  try {
    const outputDir = config.build?.outputDir || "dist";
    if (await pathExists6(outputDir)) {
      await remove(outputDir);
    }
    await ensureDir3(outputDir);
    spinner.succeed("Build directory cleaned");
  } catch (error) {
    spinner.fail("Failed to clean build directory");
    throw error;
  }
}
async function runBuildProcess(config, options) {
  const buildSteps = [];
  buildSteps.push({
    name: "Frontend",
    command: getBuildCommand(config, "frontend"),
    icon: "\u{1F310}"
  });
  if (config.backend?.enabled) {
    buildSteps.push({
      name: "Backend",
      command: getBuildCommand(config, "backend"),
      icon: "\u{1F680}"
    });
  }
  buildSteps.push({
    name: "Assets",
    command: getBuildCommand(config, "assets"),
    icon: "\u{1F3A8}"
  });
  for (const step of buildSteps) {
    await runBuildStep(step, config, options);
  }
  if (options.analyze) {
    await analyzeBuild(config);
  }
}
function getBuildCommand(config, type) {
  const engine = config.build?.engine || "kilatpack";
  switch (type) {
    case "frontend":
      switch (engine) {
        case "vite":
          return ["vite", "build"];
        case "webpack":
          return ["webpack", "--mode=production"];
        case "kilatpack":
        default:
          return ["kilatpack", "build", "--frontend"];
      }
    case "backend":
      return ["kilatpack", "build", "--backend"];
    case "assets":
      return ["kilatpack", "optimize", "--assets"];
    default:
      return ["echo", "Unknown build type"];
  }
}
async function runBuildStep(step, config, options) {
  const spinner = ora4(`Building ${step.name}...`).start();
  const stepStartTime = Date.now();
  try {
    const result = await execa(step.command[0], step.command.slice(1), {
      stdio: options.verbose ? "inherit" : "pipe",
      cwd: process.cwd()
    });
    const duration = Date.now() - stepStartTime;
    spinner.succeed(`${step.icon} ${step.name} built (${formatDuration(duration)})`);
    if (options.verbose && result.stdout) {
      console.log(chalk3.gray(result.stdout));
    }
  } catch (error) {
    spinner.fail(`${step.icon} ${step.name} build failed`);
    if (error.stdout) {
      console.log(chalk3.red("\nStdout:"));
      console.log(error.stdout);
    }
    if (error.stderr) {
      console.log(chalk3.red("\nStderr:"));
      console.log(error.stderr);
    }
    throw new Error(`${step.name} build failed: ${error.message}`);
  }
}
async function analyzeBuild(config) {
  const spinner = ora4("Analyzing build...").start();
  try {
    await execa("kilatpack", ["analyze"], {
      stdio: "pipe",
      cwd: process.cwd()
    });
    spinner.succeed("Build analysis completed");
    console.log(chalk3.cyan("\n\u{1F4CA} Bundle analysis saved to ./build-analysis.html"));
  } catch (error) {
    spinner.warn("Build analysis skipped (analyzer not available)");
  }
}
async function showBuildSummary(config, startTime) {
  const totalDuration = Date.now() - startTime;
  const outputDir = config.build?.outputDir || "dist";
  console.log(chalk3.green.bold("\n\u{1F4CB} Build Summary"));
  console.log(chalk3.green("================\n"));
  console.log(chalk3.white(`\u23F1\uFE0F  Total build time: ${formatDuration(totalDuration)}`));
  if (await pathExists6(outputDir)) {
    const stats = await getBuildStats(outputDir);
    console.log(chalk3.white(`\u{1F4C1} Output directory: ${outputDir}`));
    console.log(chalk3.white(`\u{1F4E6} Total size: ${formatBytes(stats.totalSize)}`));
    console.log(chalk3.white(`\u{1F4C4} Files created: ${stats.fileCount}`));
    if (stats.largestFiles.length > 0) {
      console.log(chalk3.white("\n\u{1F4CA} Largest files:"));
      stats.largestFiles.slice(0, 5).forEach((file) => {
        console.log(chalk3.gray(`   ${file.name.padEnd(30)} ${formatBytes(file.size)}`));
      });
    }
  }
  showPerformanceRecommendations(config);
  showNextSteps2(config);
}
async function getBuildStats(outputDir) {
  const stats = {
    totalSize: 0,
    fileCount: 0,
    largestFiles: []
  };
  async function scanDirectory(dir, relativePath = "") {
    const { readdir } = await import("fs/promises");
    const entries = await readdir(dir, { withFileTypes: true });
    for (const entry of entries) {
      const fullPath = join4(dir, entry.name);
      const relativeFilePath = join4(relativePath, entry.name);
      if (entry.isDirectory()) {
        await scanDirectory(fullPath, relativeFilePath);
      } else {
        const fileStat = await stat(fullPath);
        stats.totalSize += fileStat.size;
        stats.fileCount++;
        stats.largestFiles.push({
          name: relativeFilePath,
          size: fileStat.size
        });
      }
    }
  }
  await scanDirectory(outputDir);
  stats.largestFiles.sort((a, b) => b.size - a.size);
  return stats;
}
function showPerformanceRecommendations(config) {
  const recommendations = [];
  if (!config.performance?.compression) {
    recommendations.push("Enable compression (gzip/brotli) for smaller bundle sizes");
  }
  if (!config.performance?.treeshaking) {
    recommendations.push("Enable tree shaking to remove unused code");
  }
  if (!config.performance?.bundleSplitting) {
    recommendations.push("Enable bundle splitting for better caching");
  }
  if (recommendations.length > 0) {
    console.log(chalk3.yellow("\n\u{1F4A1} Performance Recommendations:"));
    recommendations.forEach((rec) => {
      console.log(chalk3.yellow(`   \u2022 ${rec}`));
    });
  }
}
function showNextSteps2(config) {
  console.log(chalk3.cyan("\n\u{1F680} Next Steps:"));
  console.log(chalk3.white("1. Test the production build:"));
  console.log(chalk3.gray("   kilat preview\n"));
  console.log(chalk3.white("2. Deploy to production:"));
  console.log(chalk3.gray("   kilat deploy\n"));
  console.log(chalk3.white("3. Monitor performance:"));
  console.log(chalk3.gray("   kilat analyze\n"));
  if (config.platform === "web") {
    console.log(chalk3.white("4. Serve static files:"));
    console.log(chalk3.gray("   Serve the dist/ directory with any static file server"));
  }
  if (config.platform === "desktop") {
    console.log(chalk3.white("4. Package desktop app:"));
    console.log(chalk3.gray("   kilat package --platform desktop"));
  }
  if (config.platform === "mobile") {
    console.log(chalk3.white("4. Build mobile app:"));
    console.log(chalk3.gray("   kilat build --platform mobile"));
  }
}
async function buildWatchCommand(options = {}) {
  console.log(chalk3.cyan.bold("\n\u26A1 Starting Kilat.js Build Watcher\n"));
  const config = await loadKilatConfig();
  console.log(chalk3.yellow("\u{1F504} Watching for file changes...\n"));
  try {
    await execa("kilatpack", ["build", "--watch"], {
      stdio: "inherit",
      cwd: process.cwd()
    });
  } catch (error) {
    console.error(chalk3.red("\u274C Watch mode failed:"), error.message);
    process.exit(1);
  }
}

// src/commands/doctor.ts
init_execa();
import chalk4 from "chalk";
import ora5 from "ora";
import { existsSync, readFileSync as readFileSync2 } from "fs";
import { join as join5 } from "path";
async function doctorCommand(options = {}) {
  console.log(chalk4.cyan.bold("\n\u{1FA7A} Kilat.js Doctor - System Diagnostics\n"));
  const spinner = ora5("Running diagnostics...").start();
  try {
    const results = await runDiagnostics();
    spinner.stop();
    displayResults(results);
    const hasErrors = results.some((r) => r.status === "error");
    const hasWarnings = results.some((r) => r.status === "warning");
    if (hasErrors) {
      console.log(chalk4.red.bold("\n\u274C Critical issues found! Please fix the errors above."));
      process.exit(1);
    } else if (hasWarnings) {
      console.log(chalk4.yellow.bold("\n\u26A0\uFE0F  Some warnings found. Consider addressing them for optimal performance."));
    } else {
      console.log(chalk4.green.bold("\n\u2705 All checks passed! Your environment is ready for Kilat.js development."));
    }
  } catch (error) {
    spinner.stop();
    console.error(chalk4.red.bold("\n\u274C Diagnostics failed:"), error.message);
    process.exit(1);
  }
}
async function runDiagnostics() {
  const results = [];
  results.push(await checkNodeVersion());
  results.push(await checkBunInstallation());
  results.push(await checkPackageManager());
  results.push(await checkGitInstallation());
  if (isKilatProject()) {
    results.push(await checkKilatConfig());
    results.push(await checkDependencies2());
    results.push(await checkProjectStructure());
    results.push(await checkEnvironmentFiles());
  }
  results.push(await checkVSCodeExtensions());
  results.push(await checkPortAvailability());
  return results;
}
function displayResults(results) {
  console.log(chalk4.bold("\n\u{1F4CB} Diagnostic Results:\n"));
  results.forEach((result) => {
    const icon = getStatusIcon(result.status);
    const color = getStatusColor(result.status);
    console.log(`${icon} ${chalk4[color](result.name)}`);
    if (result.message) {
      console.log(`   ${chalk4.gray(result.message)}`);
    }
    if (result.details) {
      result.details.forEach((detail) => {
        console.log(`   ${chalk4.gray("\u2022")} ${chalk4.gray(detail)}`);
      });
    }
    if (result.suggestion) {
      console.log(`   ${chalk4.blue("\u{1F4A1} Suggestion:")} ${chalk4.blue(result.suggestion)}`);
    }
    console.log();
  });
}
function getStatusIcon(status) {
  switch (status) {
    case "success":
      return "\u2705";
    case "warning":
      return "\u26A0\uFE0F";
    case "error":
      return "\u274C";
    case "info":
      return "\u2139\uFE0F";
    default:
      return "\u2753";
  }
}
function getStatusColor(status) {
  switch (status) {
    case "success":
      return "green";
    case "warning":
      return "yellow";
    case "error":
      return "red";
    case "info":
      return "blue";
    default:
      return "gray";
  }
}
async function checkNodeVersion() {
  try {
    const { stdout } = await execa("node", ["--version"]);
    const version = stdout.trim();
    const majorVersion = parseInt(version.slice(1).split(".")[0]);
    if (majorVersion >= 18) {
      return {
        name: "Node.js Version",
        status: "success",
        message: `${version} (\u2713 Compatible)`,
        details: ["Node.js 18+ is recommended for Kilat.js"]
      };
    } else {
      return {
        name: "Node.js Version",
        status: "warning",
        message: `${version} (Outdated)`,
        suggestion: "Update to Node.js 18+ for better performance and compatibility"
      };
    }
  } catch (error) {
    return {
      name: "Node.js Version",
      status: "error",
      message: "Node.js not found",
      suggestion: "Install Node.js from https://nodejs.org"
    };
  }
}
async function checkBunInstallation() {
  try {
    const { stdout } = await execa("bun", ["--version"]);
    return {
      name: "Bun Runtime",
      status: "success",
      message: `v${stdout.trim()} (\u2713 Available)`,
      details: ["Bun provides faster package management and runtime"]
    };
  } catch (error) {
    return {
      name: "Bun Runtime",
      status: "warning",
      message: "Bun not found",
      suggestion: "Install Bun for faster development: curl -fsSL https://bun.sh/install | bash"
    };
  }
}
async function checkPackageManager() {
  try {
    const managers = [];
    try {
      const { stdout } = await execa("npm", ["--version"]);
      managers.push(`npm v${stdout.trim()}`);
    } catch {
    }
    try {
      const { stdout } = await execa("yarn", ["--version"]);
      managers.push(`yarn v${stdout.trim()}`);
    } catch {
    }
    try {
      const { stdout } = await execa("pnpm", ["--version"]);
      managers.push(`pnpm v${stdout.trim()}`);
    } catch {
    }
    if (managers.length > 0) {
      return {
        name: "Package Managers",
        status: "success",
        message: "Available package managers found",
        details: managers
      };
    } else {
      return {
        name: "Package Managers",
        status: "error",
        message: "No package managers found",
        suggestion: "Install npm, yarn, or pnpm"
      };
    }
  } catch (error) {
    return {
      name: "Package Managers",
      status: "error",
      message: "Failed to check package managers",
      suggestion: "Ensure npm, yarn, or pnpm is installed"
    };
  }
}
async function checkGitInstallation() {
  try {
    const { stdout } = await execa("git", ["--version"]);
    return {
      name: "Git Version Control",
      status: "success",
      message: stdout.trim(),
      details: ["Git is available for version control"]
    };
  } catch (error) {
    return {
      name: "Git Version Control",
      status: "warning",
      message: "Git not found",
      suggestion: "Install Git for version control: https://git-scm.com"
    };
  }
}
function isKilatProject() {
  return existsSync(join5(process.cwd(), "kilat.config.ts")) || existsSync(join5(process.cwd(), "kilat.config.js"));
}
async function checkKilatConfig() {
  const configPaths = [
    join5(process.cwd(), "kilat.config.ts"),
    join5(process.cwd(), "kilat.config.js")
  ];
  const configPath = configPaths.find((path3) => existsSync(path3));
  if (configPath) {
    try {
      const content = readFileSync2(configPath, "utf-8");
      const hasTheme = content.includes("theme:");
      const hasDatabase = content.includes("database:");
      const hasPlugins = content.includes("plugins:");
      const features = [];
      if (hasTheme)
        features.push("Theme configuration");
      if (hasDatabase)
        features.push("Database configuration");
      if (hasPlugins)
        features.push("Plugin configuration");
      return {
        name: "Kilat Configuration",
        status: "success",
        message: "Configuration file found",
        details: features.length > 0 ? features : ["Basic configuration detected"]
      };
    } catch (error) {
      return {
        name: "Kilat Configuration",
        status: "warning",
        message: "Configuration file exists but may have syntax errors",
        suggestion: "Check your kilat.config.ts file for syntax errors"
      };
    }
  } else {
    return {
      name: "Kilat Configuration",
      status: "error",
      message: "No kilat.config.ts found",
      suggestion: "Create a kilat.config.ts file in your project root"
    };
  }
}
async function checkDependencies2() {
  const packageJsonPath = join5(process.cwd(), "package.json");
  if (!existsSync(packageJsonPath)) {
    return {
      name: "Project Dependencies",
      status: "error",
      message: "No package.json found",
      suggestion: "Initialize your project with npm init or yarn init"
    };
  }
  try {
    const packageJson = JSON.parse(readFileSync2(packageJsonPath, "utf-8"));
    const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
    const kilatPackages = Object.keys(deps).filter((dep) => dep.startsWith("kilat-"));
    if (kilatPackages.length > 0) {
      return {
        name: "Project Dependencies",
        status: "success",
        message: `${kilatPackages.length} Kilat packages found`,
        details: kilatPackages
      };
    } else {
      return {
        name: "Project Dependencies",
        status: "warning",
        message: "No Kilat packages found in dependencies",
        suggestion: "Install Kilat packages: npm install kilat-core kilat-css"
      };
    }
  } catch (error) {
    return {
      name: "Project Dependencies",
      status: "error",
      message: "Invalid package.json",
      suggestion: "Fix syntax errors in package.json"
    };
  }
}
async function checkProjectStructure() {
  const expectedDirs = ["src", "public"];
  const optionalDirs = ["pages", "components", "styles", "api"];
  const existingDirs = expectedDirs.filter((dir) => existsSync(join5(process.cwd(), dir)));
  const existingOptional = optionalDirs.filter((dir) => existsSync(join5(process.cwd(), dir)));
  if (existingDirs.length === expectedDirs.length) {
    return {
      name: "Project Structure",
      status: "success",
      message: "Standard project structure found",
      details: [...existingDirs, ...existingOptional].map((dir) => `${dir}/ directory`)
    };
  } else {
    const missing = expectedDirs.filter((dir) => !existingDirs.includes(dir));
    return {
      name: "Project Structure",
      status: "warning",
      message: "Some standard directories missing",
      suggestion: `Create missing directories: ${missing.join(", ")}`
    };
  }
}
async function checkEnvironmentFiles() {
  const envFiles = [".env", ".env.local", ".env.development", ".env.production"];
  const existingEnvFiles = envFiles.filter((file) => existsSync(join5(process.cwd(), file)));
  if (existingEnvFiles.length > 0) {
    return {
      name: "Environment Files",
      status: "success",
      message: "Environment files found",
      details: existingEnvFiles
    };
  } else {
    return {
      name: "Environment Files",
      status: "info",
      message: "No environment files found",
      suggestion: "Create .env files for environment-specific configuration"
    };
  }
}
async function checkVSCodeExtensions() {
  const vscodeDir = join5(process.cwd(), ".vscode");
  const extensionsFile = join5(vscodeDir, "extensions.json");
  if (existsSync(extensionsFile)) {
    return {
      name: "VS Code Extensions",
      status: "success",
      message: "Recommended extensions configured",
      details: ["Extensions configuration found in .vscode/extensions.json"]
    };
  } else {
    return {
      name: "VS Code Extensions",
      status: "info",
      message: "No VS Code extensions configuration",
      suggestion: "Create .vscode/extensions.json for recommended extensions"
    };
  }
}
async function checkPortAvailability() {
  const commonPorts = [3e3, 3001, 8080, 8e3];
  const availablePorts = [];
  for (const port of commonPorts) {
    try {
      const { createServer } = await import("net");
      const server = createServer();
      await new Promise((resolve, reject) => {
        server.listen(port, () => {
          server.close();
          availablePorts.push(port);
          resolve(true);
        });
        server.on("error", reject);
      });
    } catch {
    }
  }
  if (availablePorts.length > 0) {
    return {
      name: "Port Availability",
      status: "success",
      message: "Development ports available",
      details: availablePorts.map((port) => `Port ${port} is available`)
    };
  } else {
    return {
      name: "Port Availability",
      status: "warning",
      message: "Common development ports are in use",
      suggestion: "Stop other development servers or use different ports"
    };
  }
}

// src/commands/plugin.ts
init_execa();
import inquirer2 from "inquirer";
import chalk5 from "chalk";
import ora6 from "ora";
import { join as join6 } from "path";
import { existsSync as existsSync2, readFileSync as readFileSync3 } from "fs";
async function pluginCommand(action, pluginName, options = {}) {
  console.log(chalk5.cyan.bold("\n\u{1F50C} Kilat.js Plugin Manager\n"));
  if (!isKilatProject2()) {
    console.error(chalk5.red("\u274C Not in a Kilat.js project directory"));
    process.exit(1);
  }
  try {
    switch (action) {
      case "install":
      case "add":
        await installPlugin(pluginName, options);
        break;
      case "remove":
      case "uninstall":
        await removePlugin(pluginName, options);
        break;
      case "list":
        await listPlugins(options);
        break;
      case "search":
        await searchPlugins(pluginName, options);
        break;
      case "info":
        await showPluginInfo(pluginName, options);
        break;
      case "enable":
        await togglePlugin(pluginName, true, options);
        break;
      case "disable":
        await togglePlugin(pluginName, false, options);
        break;
      case "update":
        await updatePlugin(pluginName, options);
        break;
      default:
        await showPluginMenu();
    }
  } catch (error) {
    console.error(chalk5.red.bold("\n\u274C Plugin operation failed:"), error.message);
    process.exit(1);
  }
}
async function showPluginMenu() {
  const { action } = await inquirer2.prompt([
    {
      type: "list",
      name: "action",
      message: "What would you like to do?",
      choices: [
        { name: "\u{1F4E6} Install a plugin", value: "install" },
        { name: "\u{1F5D1}\uFE0F  Remove a plugin", value: "remove" },
        { name: "\u{1F4CB} List installed plugins", value: "list" },
        { name: "\u{1F50D} Search for plugins", value: "search" },
        { name: "\u{1F527} Configure plugins", value: "configure" },
        { name: "\u{1F504} Update plugins", value: "update" }
      ]
    }
  ]);
  switch (action) {
    case "install":
      await promptInstallPlugin();
      break;
    case "remove":
      await promptRemovePlugin();
      break;
    case "list":
      await listPlugins();
      break;
    case "search":
      await promptSearchPlugins();
      break;
    case "configure":
      await configurePlugins();
      break;
    case "update":
      await promptUpdatePlugins();
      break;
  }
}
async function installPlugin(pluginName, options = {}) {
  if (!pluginName) {
    pluginName = await promptPluginName("install");
  }
  const spinner = ora6(`Installing plugin: ${pluginName}`).start();
  try {
    const pluginInfo = await getPluginInfo(pluginName);
    if (!pluginInfo) {
      spinner.fail(`Plugin "${pluginName}" not found in registry`);
      return;
    }
    await execa("npm", ["install", pluginInfo.package], { cwd: process.cwd() });
    await addPluginToConfig(pluginName, pluginInfo);
    spinner.succeed(`Plugin "${pluginName}" installed successfully`);
    console.log(chalk5.green("\n\u2705 Plugin installed and configured!"));
    if (pluginInfo.postInstall) {
      console.log(chalk5.blue("\n\u{1F4DD} Post-installation notes:"));
      console.log(chalk5.gray(pluginInfo.postInstall));
    }
  } catch (error) {
    spinner.fail(`Failed to install plugin: ${error.message}`);
    throw error;
  }
}
async function removePlugin(pluginName, options = {}) {
  if (!pluginName) {
    pluginName = await promptPluginName("remove");
  }
  const config = getKilatConfig();
  if (!config.plugins || !config.plugins[pluginName]) {
    console.log(chalk5.yellow(`Plugin "${pluginName}" is not installed`));
    return;
  }
  const { confirm } = await inquirer2.prompt([
    {
      type: "confirm",
      name: "confirm",
      message: `Are you sure you want to remove "${pluginName}"?`,
      default: false
    }
  ]);
  if (!confirm) {
    console.log(chalk5.gray("Operation cancelled"));
    return;
  }
  const spinner = ora6(`Removing plugin: ${pluginName}`).start();
  try {
    const pluginInfo = config.plugins[pluginName];
    await removePluginFromConfig(pluginName);
    if (pluginInfo.package) {
      await execa("npm", ["uninstall", pluginInfo.package], { cwd: process.cwd() });
    }
    spinner.succeed(`Plugin "${pluginName}" removed successfully`);
  } catch (error) {
    spinner.fail(`Failed to remove plugin: ${error.message}`);
    throw error;
  }
}
async function listPlugins(options = {}) {
  const config = getKilatConfig();
  if (!config.plugins || Object.keys(config.plugins).length === 0) {
    console.log(chalk5.yellow("No plugins installed"));
    return;
  }
  console.log(chalk5.bold("\u{1F4CB} Installed Plugins:\n"));
  for (const [name, plugin] of Object.entries(config.plugins)) {
    const status = plugin.enabled !== false ? "\u2705 Enabled" : "\u274C Disabled";
    const version = plugin.version || "unknown";
    console.log(`${chalk5.cyan(name)} ${chalk5.gray(`(v${version})`)}`);
    console.log(`   Status: ${status}`);
    if (plugin.description) {
      console.log(`   ${chalk5.gray(plugin.description)}`);
    }
    console.log();
  }
}
async function searchPlugins(query, options = {}) {
  if (!query) {
    const { searchQuery } = await inquirer2.prompt([
      {
        type: "input",
        name: "searchQuery",
        message: "Search for plugins:",
        validate: (input) => input.trim().length > 0 || "Please enter a search term"
      }
    ]);
    query = searchQuery;
  }
  const spinner = ora6(`Searching for plugins: ${query}`).start();
  try {
    const results = await searchPluginRegistry(query);
    spinner.stop();
    if (results.length === 0) {
      console.log(chalk5.yellow(`No plugins found matching "${query}"`));
      return;
    }
    console.log(chalk5.bold(`
\u{1F50D} Search Results for "${query}":
`));
    results.forEach((plugin) => {
      console.log(`${chalk5.cyan(plugin.name)} ${chalk5.gray(`(v${plugin.version})`)}`);
      console.log(`   ${chalk5.gray(plugin.description)}`);
      console.log(`   ${chalk5.blue("Package:")} ${plugin.package}`);
      console.log(`   ${chalk5.green("Downloads:")} ${plugin.downloads || "N/A"}`);
      console.log();
    });
  } catch (error) {
    spinner.fail(`Search failed: ${error.message}`);
  }
}
async function showPluginInfo(pluginName, options = {}) {
  if (!pluginName) {
    pluginName = await promptPluginName("info");
  }
  const spinner = ora6(`Getting plugin info: ${pluginName}`).start();
  try {
    const info = await getPluginInfo(pluginName);
    spinner.stop();
    if (!info) {
      console.log(chalk5.yellow(`Plugin "${pluginName}" not found`));
      return;
    }
    console.log(chalk5.bold(`
\u2139\uFE0F  Plugin Information: ${info.name}
`));
    console.log(`${chalk5.blue("Version:")} ${info.version}`);
    console.log(`${chalk5.blue("Description:")} ${info.description}`);
    console.log(`${chalk5.blue("Package:")} ${info.package}`);
    console.log(`${chalk5.blue("Author:")} ${info.author || "Unknown"}`);
    console.log(`${chalk5.blue("License:")} ${info.license || "Unknown"}`);
    if (info.homepage) {
      console.log(`${chalk5.blue("Homepage:")} ${info.homepage}`);
    }
    if (info.repository) {
      console.log(`${chalk5.blue("Repository:")} ${info.repository}`);
    }
    if (info.dependencies && info.dependencies.length > 0) {
      console.log(`${chalk5.blue("Dependencies:")} ${info.dependencies.join(", ")}`);
    }
    if (info.features && info.features.length > 0) {
      console.log(`${chalk5.blue("Features:")}`);
      info.features.forEach((feature) => {
        console.log(`   \u2022 ${feature}`);
      });
    }
  } catch (error) {
    spinner.fail(`Failed to get plugin info: ${error.message}`);
  }
}
async function togglePlugin(pluginName, enabled, options = {}) {
  if (!pluginName) {
    pluginName = await promptPluginName(enabled ? "enable" : "disable");
  }
  const config = getKilatConfig();
  if (!config.plugins || !config.plugins[pluginName]) {
    console.log(chalk5.yellow(`Plugin "${pluginName}" is not installed`));
    return;
  }
  config.plugins[pluginName].enabled = enabled;
  await updateKilatConfig2(config);
  const action = enabled ? "enabled" : "disabled";
  console.log(chalk5.green(`\u2705 Plugin "${pluginName}" ${action} successfully`));
}
async function updatePlugin(pluginName, options = {}) {
  if (!pluginName) {
    pluginName = await promptPluginName("update");
  }
  const config = getKilatConfig();
  if (!config.plugins || !config.plugins[pluginName]) {
    console.log(chalk5.yellow(`Plugin "${pluginName}" is not installed`));
    return;
  }
  const spinner = ora6(`Updating plugin: ${pluginName}`).start();
  try {
    const pluginInfo = config.plugins[pluginName];
    if (pluginInfo.package) {
      await execa("npm", ["update", pluginInfo.package], { cwd: process.cwd() });
    }
    const latestInfo = await getPluginInfo(pluginName);
    if (latestInfo) {
      config.plugins[pluginName].version = latestInfo.version;
      await updateKilatConfig2(config);
    }
    spinner.succeed(`Plugin "${pluginName}" updated successfully`);
  } catch (error) {
    spinner.fail(`Failed to update plugin: ${error.message}`);
    throw error;
  }
}
function isKilatProject2() {
  return existsSync2(join6(process.cwd(), "kilat.config.ts")) || existsSync2(join6(process.cwd(), "kilat.config.js"));
}
function getKilatConfig() {
  const configPaths = [
    join6(process.cwd(), "kilat.config.ts"),
    join6(process.cwd(), "kilat.config.js")
  ];
  const configPath = configPaths.find((path3) => existsSync2(path3));
  if (!configPath) {
    throw new Error("kilat.config.ts not found");
  }
  const content = readFileSync3(configPath, "utf-8");
  return {
    plugins: {}
  };
}
async function updateKilatConfig2(config) {
  const configPath = join6(process.cwd(), "kilat.config.ts");
  console.log(chalk5.gray("Config updated"));
}
async function addPluginToConfig(pluginName, pluginInfo) {
  const config = getKilatConfig();
  if (!config.plugins) {
    config.plugins = {};
  }
  config.plugins[pluginName] = {
    version: pluginInfo.version,
    enabled: true,
    package: pluginInfo.package
  };
  await updateKilatConfig2(config);
}
async function removePluginFromConfig(pluginName) {
  const config = getKilatConfig();
  if (config.plugins && config.plugins[pluginName]) {
    delete config.plugins[pluginName];
    await updateKilatConfig2(config);
  }
}
async function getPluginInfo(pluginName) {
  const mockPlugins = {
    "auth": {
      name: "auth",
      version: "1.0.0",
      description: "Authentication and authorization plugin",
      package: "kilat-plugin-auth",
      author: "Kilat Team",
      features: ["JWT authentication", "Role-based access", "OAuth integration"]
    },
    "cms": {
      name: "cms",
      version: "0.9.1",
      description: "Content management system plugin",
      package: "kilat-plugin-cms",
      author: "Kilat Team",
      features: ["Content editor", "Media management", "SEO optimization"]
    }
  };
  return mockPlugins[pluginName] || null;
}
async function searchPluginRegistry(query) {
  const allPlugins = [
    {
      name: "auth",
      version: "1.0.0",
      description: "Authentication and authorization plugin",
      package: "kilat-plugin-auth",
      downloads: "10k+"
    },
    {
      name: "cms",
      version: "0.9.1",
      description: "Content management system plugin",
      package: "kilat-plugin-cms",
      downloads: "5k+"
    },
    {
      name: "payments",
      version: "1.2.0",
      description: "Payment processing plugin",
      package: "kilat-plugin-payments",
      downloads: "8k+"
    }
  ];
  return allPlugins.filter(
    (plugin) => plugin.name.includes(query.toLowerCase()) || plugin.description.toLowerCase().includes(query.toLowerCase())
  );
}
async function promptPluginName(action) {
  const { pluginName } = await inquirer2.prompt([
    {
      type: "input",
      name: "pluginName",
      message: `Enter plugin name to ${action}:`,
      validate: (input) => input.trim().length > 0 || "Please enter a plugin name"
    }
  ]);
  return pluginName;
}
async function promptInstallPlugin() {
  const { searchFirst } = await inquirer2.prompt([
    {
      type: "confirm",
      name: "searchFirst",
      message: "Would you like to search for plugins first?",
      default: true
    }
  ]);
  if (searchFirst) {
    await promptSearchPlugins();
  }
  const pluginName = await promptPluginName("install");
  await installPlugin(pluginName);
}
async function promptRemovePlugin() {
  const config = getKilatConfig();
  const installedPlugins = Object.keys(config.plugins || {});
  if (installedPlugins.length === 0) {
    console.log(chalk5.yellow("No plugins installed"));
    return;
  }
  const { pluginName } = await inquirer2.prompt([
    {
      type: "list",
      name: "pluginName",
      message: "Select plugin to remove:",
      choices: installedPlugins
    }
  ]);
  await removePlugin(pluginName);
}
async function promptSearchPlugins() {
  const { query } = await inquirer2.prompt([
    {
      type: "input",
      name: "query",
      message: "Search for plugins:",
      validate: (input) => input.trim().length > 0 || "Please enter a search term"
    }
  ]);
  await searchPlugins(query);
}
async function promptUpdatePlugins() {
  const config = getKilatConfig();
  const installedPlugins = Object.keys(config.plugins || {});
  if (installedPlugins.length === 0) {
    console.log(chalk5.yellow("No plugins installed"));
    return;
  }
  const { plugins } = await inquirer2.prompt([
    {
      type: "checkbox",
      name: "plugins",
      message: "Select plugins to update:",
      choices: installedPlugins.map((name) => ({ name, checked: true }))
    }
  ]);
  for (const pluginName of plugins) {
    await updatePlugin(pluginName);
  }
}
async function configurePlugins() {
  console.log(chalk5.blue("\u{1F527} Plugin configuration is managed in kilat.config.ts"));
  console.log(chalk5.gray("Edit your configuration file to customize plugin settings."));
}

// src/index.ts
init_package_manager();
init_config();

// src/types.ts
var AVAILABLE_THEMES = [
  "cyberpunk",
  "nusantara",
  "retro",
  "material",
  "neumorphism",
  "carbon",
  "minimalist",
  "asymetric",
  "elemen3d",
  "dana",
  "ark",
  "aurora",
  "unix",
  "classic"
];
var PLUGIN_CATEGORIES = [
  "auth",
  "database",
  "ui",
  "animation",
  "deployment",
  "testing",
  "monitoring",
  "cms",
  "ecommerce",
  "ai",
  "social",
  "analytics"
];
var PROJECT_FEATURES = [
  "ssr",
  "pwa",
  "auth",
  "database",
  "api",
  "testing",
  "docker",
  "ci-cd",
  "monitoring",
  "analytics",
  "i18n",
  "cms",
  "ecommerce",
  "blog",
  "dashboard",
  "landing-page"
];

// src/index.ts
var KILAT_CLI_VERSION = "1.0.0";
var src_default = {
  version: KILAT_CLI_VERSION
};
export {
  AVAILABLE_THEMES,
  KILAT_CLI_VERSION,
  PLUGIN_CATEGORIES,
  PROJECT_FEATURES,
  addPackage,
  buildCommand,
  buildWatchCommand,
  checkDependencies,
  createCommand,
  src_default as default,
  detectPackageManager,
  devCommand,
  doctorCommand,
  formatBytes,
  formatCase,
  formatCodeBlock,
  formatCommand,
  formatDate,
  formatDiff,
  formatDuration,
  formatList,
  formatNumber,
  formatPath,
  formatPercentage,
  formatProgressBar,
  formatRelativeTime,
  formatStatus,
  formatTable,
  formatUrl,
  formatVersion,
  getAvailablePackageManagers,
  getDefaultConfig,
  getInstalledPackages,
  getOutdatedPackages,
  getPackageInfo,
  getPackageManagerRecommendations,
  installDependencies,
  isPackageInstalled,
  loadKilatConfig,
  pluginCommand,
  removePackage,
  runAllValidations,
  runScript,
  saveKilatConfig,
  updateKilatConfig,
  updatePackages,
  validateConfiguration,
  validateEnvironment,
  validateKilatConfig,
  validatePerformance,
  validateProjectName,
  validateProjectStructure,
  validateSecurity
};
