{"name": "kilat-backend", "version": "1.0.0", "description": "⚡ Kilat Backend - Internal API server with file-based routing and middleware", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "bin": {"kilat-backend": "./bin/kilat-backend.js"}, "exports": {".": {"import": "./dist/index.esm.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./server": {"import": "./dist/server.esm.js", "require": "./dist/server.js", "types": "./dist/server.d.ts"}}, "files": ["dist", "bin", "README.md"], "scripts": {"build": "bun run build:esm && bun run build:cjs && bun run build:types", "build:esm": "esbuild src/index.ts --bundle --format=esm --outfile=dist/index.esm.js --external:kilat-db --external:kilat-utils --platform=node", "build:cjs": "esbuild src/index.ts --bundle --format=cjs --outfile=dist/index.js --external:kilat-db --external:kilat-utils --platform=node", "build:types": "tsc --emitDeclarationOnly --outDir dist", "dev": "bun run src/server.ts --watch", "start": "bun run dist/server.js", "test": "bun test", "clean": "rm -rf dist"}, "keywords": ["kilat", "backend", "api", "server", "routing", "middleware", "bun"], "author": "Kilat.js Team", "license": "MIT", "dependencies": {"cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.0", "multer": "^1.4.5-lts.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "validator": "^13.11.0", "uuid": "^9.0.1"}, "devDependencies": {"@types/cors": "^2.8.0", "@types/compression": "^1.7.0", "@types/multer": "^1.4.0", "@types/jsonwebtoken": "^9.0.0", "@types/bcryptjs": "^2.4.0", "@types/validator": "^13.11.0", "@types/uuid": "^9.0.0", "esbuild": "^0.19.0", "typescript": "^5.3.0"}, "peerDependencies": {"kilat-db": "^1.0.0", "kilat-utils": "^1.0.0"}}