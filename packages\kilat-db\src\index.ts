// 🗃️ Kilat.js Database - Universal ORM
// Export all types
export * from './types';

// Export core classes
export { KilatDB } from './core/KilatDB';
export { Model } from './core/Model';
export { QueryBuilder } from './core/QueryBuilder';

// Export connection adapters
export { SQLiteAdapter } from './adapters/SQLiteAdapter';
export { MySQLAdapter } from './adapters/MySQLAdapter';

// Export migration system
export { MigrationManager } from './migrations/MigrationManager';
export { Migration } from './migrations/Migration';

// Export utilities
export { defineModel } from './utils/defineModel';
export { createConnection } from './utils/connection';
export { validateSchema } from './utils/validation';
export { generateMigration } from './utils/migration';

// Export hooks
export { useKilatDB } from './hooks/useKilatDB';
export { useModel } from './hooks/useModel';
export { useQuery } from './hooks/useQuery';

// Version info
export const KILAT_DB_VERSION = '1.0.0';

// 🏭 Main factory function
import { KilatDB } from './core/KilatDB';
import type { DatabaseConfig } from './types';

export function createKilatDB(config: DatabaseConfig): KilatDB {
  return new KilatDB(config);
}

// 🎯 Quick setup functions
export function createSQLiteDB(file: string, options?: Partial<DatabaseConfig>): KilatDB {
  return createKilatDB({
    driver: 'sqlite',
    connection: {
      sqlite: { file, enableWAL: true, timeout: 5000 }
    },
    migrations: {
      directory: './migrations',
      autoRun: true
    },
    logging: {
      enabled: process.env.NODE_ENV === 'development',
      level: 'info',
      queries: false
    },
    cache: {
      enabled: true,
      ttl: 300000, // 5 minutes
      maxSize: 100
    },
    ...options
  });
}

export function createMySQLDB(connection: {
  host: string;
  user: string;
  password: string;
  database: string;
  port?: number;
}, options?: Partial<DatabaseConfig>): KilatDB {
  return createKilatDB({
    driver: 'mysql',
    connection: {
      mysql: {
        host: connection.host,
        port: connection.port || 3306,
        user: connection.user,
        password: connection.password,
        database: connection.database,
        connectionLimit: 10,
        acquireTimeout: 60000,
        timeout: 60000
      }
    },
    migrations: {
      directory: './migrations',
      autoRun: true
    },
    logging: {
      enabled: process.env.NODE_ENV === 'development',
      level: 'info',
      queries: false
    },
    cache: {
      enabled: true,
      ttl: 300000, // 5 minutes
      maxSize: 100
    },
    ...options
  });
}

// 🎨 Model definition helpers
export function defineSchema<T extends Record<string, any>>(definition: T): T {
  return definition;
}

// 📊 Common field types
export const Fields = {
  id: () => ({ type: 'uuid' as const, required: true, unique: true }),
  string: (length = 255) => ({ type: 'string' as const, length }),
  text: () => ({ type: 'text' as const }),
  number: () => ({ type: 'number' as const }),
  boolean: () => ({ type: 'boolean' as const }),
  date: () => ({ type: 'date' as const }),
  json: () => ({ type: 'json' as const }),
  email: () => ({ type: 'email' as const }),
  url: () => ({ type: 'url' as const }),
  enum: (values: string[]) => ({ type: 'enum' as const, enum: values }),
  
  // Common patterns
  timestamps: () => ({
    createdAt: { type: 'date' as const, default: () => new Date() },
    updatedAt: { type: 'date' as const, default: () => new Date() }
  }),
  
  softDelete: () => ({
    deletedAt: { type: 'date' as const, default: null }
  }),
  
  // Foreign key helper
  foreignKey: (model: string, field = 'id') => ({
    type: 'uuid' as const,
    references: { model, field }
  })
};

// 🔍 Query helpers
export const Query = {
  eq: (value: any) => ({ $eq: value }),
  ne: (value: any) => ({ $ne: value }),
  gt: (value: any) => ({ $gt: value }),
  gte: (value: any) => ({ $gte: value }),
  lt: (value: any) => ({ $lt: value }),
  lte: (value: any) => ({ $lte: value }),
  in: (values: any[]) => ({ $in: values }),
  nin: (values: any[]) => ({ $nin: values }),
  like: (pattern: string) => ({ $like: pattern }),
  ilike: (pattern: string) => ({ $ilike: pattern }),
  between: (min: any, max: any) => ({ $between: [min, max] }),
  isNull: () => ({ $null: true }),
  isNotNull: () => ({ $null: false }),
  and: (...conditions: any[]) => ({ $and: conditions }),
  or: (...conditions: any[]) => ({ $or: conditions })
};

// 🎯 Migration helpers
export const Schema = {
  createTable: (name: string, callback: (table: any) => void) => {
    // Implementation would be in migration system
    return { type: 'createTable', name, callback };
  },
  
  dropTable: (name: string) => {
    return { type: 'dropTable', name };
  },
  
  addColumn: (table: string, name: string, definition: any) => {
    return { type: 'addColumn', table, name, definition };
  },
  
  dropColumn: (table: string, name: string) => {
    return { type: 'dropColumn', table, name };
  },
  
  addIndex: (table: string, fields: string[], options?: any) => {
    return { type: 'addIndex', table, fields, options };
  },
  
  dropIndex: (table: string, name: string) => {
    return { type: 'dropIndex', table, name };
  }
};

// Default export
export default {
  version: KILAT_DB_VERSION,
  createKilatDB,
  createSQLiteDB,
  createMySQLDB,
  defineSchema,
  Fields,
  Query,
  Schema
};
