import { ReactNode, ComponentType } from 'react';

// 🎯 Route Types
export interface KilatRoute {
  path: string;
  component: ComponentType<any>;
  layout?: ComponentType<any>;
  middleware?: KilatMiddleware[];
  meta?: RouteMeta;
  children?: KilatRoute[];
  exact?: boolean;
  caseSensitive?: boolean;
}

export interface RouteMeta {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  canonical?: string;
  robots?: string;
  auth?: boolean;
  roles?: string[];
  permissions?: string[];
  theme?: string;
  layout?: string;
  cache?: boolean | number;
  preload?: boolean;
  [key: string]: any;
}

// 🔀 Router Configuration
export interface KilatRouterConfig {
  basePath: string;
  mode: 'browser' | 'hash' | 'memory';
  caseSensitive: boolean;
  trailingSlash: 'always' | 'never' | 'preserve';
  middleware: KilatMiddleware[];
  layouts: {
    default?: ComponentType<any>;
    error?: ComponentType<any>;
    loading?: ComponentType<any>;
  };
  transitions?: {
    enabled: boolean;
    duration: number;
    type: 'fade' | 'slide' | 'scale' | 'none';
  };
  preload?: {
    enabled: boolean;
    strategy: 'hover' | 'visible' | 'intent';
  };
  cache?: {
    enabled: boolean;
    ttl: number;
    maxSize: number;
  };
}

// 🛡️ Middleware Types
export interface KilatMiddlewareContext {
  route: KilatRoute;
  params: Record<string, string>;
  query: URLSearchParams;
  pathname: string;
  search: string;
  hash: string;
  state: any;
  navigate: (to: string, options?: NavigateOptions) => void;
  redirect: (to: string, options?: NavigateOptions) => void;
  abort: (reason?: string) => void;
}

export type KilatMiddleware = (
  context: KilatMiddlewareContext
) => void | Promise<void> | boolean | Promise<boolean>;

// 🧭 Navigation Types
export interface NavigateOptions {
  replace?: boolean;
  state?: any;
  preventScrollReset?: boolean;
  relative?: 'route' | 'path';
}

export interface NavigationGuard {
  beforeEach?: (to: KilatRoute, from: KilatRoute) => boolean | Promise<boolean>;
  afterEach?: (to: KilatRoute, from: KilatRoute) => void | Promise<void>;
  beforeResolve?: (to: KilatRoute) => boolean | Promise<boolean>;
}

// 📁 File-based Routing Types
export interface FileRoute {
  filePath: string;
  routePath: string;
  component: ComponentType<any>;
  layout?: ComponentType<any>;
  middleware?: string[];
  meta?: RouteMeta;
  isDynamic: boolean;
  segments: RouteSegment[];
}

export interface RouteSegment {
  type: 'static' | 'dynamic' | 'catch-all' | 'optional';
  value: string;
  name?: string;
}

// 🎨 Layout Types
export interface LayoutProps {
  children: ReactNode;
  route?: KilatRoute;
  params?: Record<string, string>;
  query?: URLSearchParams;
}

export interface NestedLayoutProps extends LayoutProps {
  level: number;
  parentLayout?: ComponentType<any>;
}

// 🔄 Route Loading Types
export interface RouteLoader<T = any> {
  load: () => Promise<T>;
  cache?: boolean;
  timeout?: number;
  retry?: number;
}

export interface LoaderData<T = any> {
  data: T;
  loading: boolean;
  error: Error | null;
  reload: () => Promise<void>;
}

// 📊 Route Analytics
export interface RouteAnalytics {
  path: string;
  visits: number;
  lastVisit: Date;
  averageTime: number;
  bounceRate: number;
  referrers: Record<string, number>;
}

// 🎯 Router Context
export interface KilatRouterContext {
  config: KilatRouterConfig;
  currentRoute: KilatRoute | null;
  params: Record<string, string>;
  query: URLSearchParams;
  navigate: (to: string, options?: NavigateOptions) => void;
  back: () => void;
  forward: () => void;
  refresh: () => void;
  preload: (path: string) => Promise<void>;
  isLoading: boolean;
  error: Error | null;
}

// 🔍 Route Matching
export interface RouteMatch {
  route: KilatRoute;
  params: Record<string, string>;
  score: number;
  exact: boolean;
}

// 🎭 Transition Types
export interface RouteTransition {
  from: string;
  to: string;
  duration: number;
  type: 'fade' | 'slide' | 'scale' | 'none';
  direction?: 'left' | 'right' | 'up' | 'down';
}

// 📱 Mobile-specific Types
export interface MobileRouterConfig {
  swipeGestures: boolean;
  backButton: boolean;
  statusBar: {
    style: 'light' | 'dark' | 'auto';
    backgroundColor?: string;
  };
  orientation: 'portrait' | 'landscape' | 'auto';
}

// 🔐 Auth Integration
export interface AuthRoute extends KilatRoute {
  requiresAuth: boolean;
  roles?: string[];
  permissions?: string[];
  redirectTo?: string;
  onUnauthorized?: (route: KilatRoute) => void;
}

// 📈 Performance Types
export interface RoutePerformance {
  loadTime: number;
  renderTime: number;
  bundleSize: number;
  cacheHit: boolean;
  preloaded: boolean;
}

// 🌐 Internationalization
export interface I18nRoute extends KilatRoute {
  locales?: string[];
  defaultLocale?: string;
  localizedPaths?: Record<string, string>;
}
