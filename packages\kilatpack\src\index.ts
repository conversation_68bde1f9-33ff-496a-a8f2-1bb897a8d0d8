// ⚡ KilatPack - Fast Build Engine
export * from './types';

// Export core builders
export { KilatBuilder } from './core/KilatBuilder';
export { DevServer } from './dev/DevServer';
export { HMRManager } from './dev/HMRManager';

// Export plugins
export { createKilatPlugin } from './plugins/createPlugin';
export { reactPlugin } from './plugins/react';
export { cssPlugin } from './plugins/css';
export { assetsPlugin } from './plugins/assets';
export { typescriptPlugin } from './plugins/typescript';

// Export utilities
export { createConfig } from './utils/config';
export { bundleAnalyzer } from './utils/analyzer';
export { optimizeBundle } from './utils/optimizer';
export { createLogger } from './utils/logger';

// Export build functions
export { build } from './build';
export { serve } from './serve';
export { preview } from './preview';

import type { KilatPackConfig } from './types';
import { KilatBuilder } from './core/KilatBuilder';

// 🎯 Default Configuration
export const defaultConfig: KilatPackConfig = {
  entry: './src/index.ts',
  outDir: './dist',
  publicDir: './public',
  assetsDir: 'assets',
  mode: 'development',
  target: 'web',
  platform: 'browser',
  minify: false,
  sourcemap: true,
  splitting: true,
  treeshaking: true,
  bundle: true,
  server: {
    port: 3000,
    host: 'localhost',
    https: false,
    open: true,
    cors: true,
    hmr: true,
    liveReload: true
  },
  hmr: {
    enabled: true,
    overlay: true
  },
  css: {
    modules: false,
    postcss: true,
    preprocessor: 'none',
    extract: true,
    minify: false
  },
  assets: {
    limit: 4096,
    publicPath: '/',
    hash: true,
    extensions: ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp', '.ico']
  },
  transform: {
    jsx: 'react',
    typescript: true,
    decorators: false,
    define: {},
    inject: []
  },
  analyze: {
    enabled: false,
    bundleSize: true,
    dependencies: true,
    duplicates: true,
    treemap: false
  },
  debug: {
    enabled: false,
    overlay: true,
    verbose: false,
    profile: false
  },
  performance: {
    cache: true,
    parallel: true,
    workers: 4,
    memoryLimit: 512
  }
};

// 🏭 Main KilatPack Factory
export function createKilatPack(userConfig: Partial<KilatPackConfig> = {}): KilatBuilder {
  const config: KilatPackConfig = {
    ...defaultConfig,
    ...userConfig,
    server: {
      ...defaultConfig.server,
      ...userConfig.server
    },
    hmr: {
      ...defaultConfig.hmr,
      ...userConfig.hmr
    },
    css: {
      ...defaultConfig.css,
      ...userConfig.css
    },
    assets: {
      ...defaultConfig.assets,
      ...userConfig.assets
    },
    transform: {
      ...defaultConfig.transform,
      ...userConfig.transform
    },
    analyze: {
      ...defaultConfig.analyze,
      ...userConfig.analyze
    },
    debug: {
      ...defaultConfig.debug,
      ...userConfig.debug
    },
    performance: {
      ...defaultConfig.performance,
      ...userConfig.performance
    }
  };

  return new KilatBuilder(config);
}

// 🚀 Quick Build Functions
export async function quickBuild(entry: string, outDir: string = './dist') {
  const builder = createKilatPack({
    entry,
    outDir,
    mode: 'production',
    minify: true,
    sourcemap: false
  });

  return builder.build();
}

export async function quickDev(entry: string, port: number = 3000) {
  const builder = createKilatPack({
    entry,
    mode: 'development',
    server: { port }
  });

  return builder.serve();
}

// 🎨 Preset Configurations
export const presets = {
  // React SPA
  react: (entry: string = './src/index.tsx'): Partial<KilatPackConfig> => ({
    entry,
    transform: {
      jsx: 'react',
      typescript: true,
      decorators: false,
      define: {
        'process.env.NODE_ENV': '"development"'
      },
      inject: []
    },
    plugins: [
      reactPlugin(),
      cssPlugin(),
      assetsPlugin(),
      typescriptPlugin()
    ]
  }),

  // Vue SPA
  vue: (entry: string = './src/main.ts'): Partial<KilatPackConfig> => ({
    entry,
    transform: {
      jsx: 'vue',
      typescript: true,
      decorators: false,
      define: {
        '__VUE_OPTIONS_API__': 'true',
        '__VUE_PROD_DEVTOOLS__': 'false'
      },
      inject: []
    }
  }),

  // Node.js Library
  node: (entry: string = './src/index.ts'): Partial<KilatPackConfig> => ({
    entry,
    target: 'node',
    platform: 'node',
    splitting: false,
    external: ['fs', 'path', 'os', 'crypto', 'http', 'https', 'url', 'util'],
    transform: {
      jsx: 'react',
      typescript: true,
      decorators: true,
      define: {},
      inject: []
    }
  }),

  // Electron App
  electron: (entry: string = './src/main.ts'): Partial<KilatPackConfig> => ({
    entry,
    target: 'electron',
    platform: 'node',
    external: ['electron'],
    transform: {
      jsx: 'react',
      typescript: true,
      decorators: false,
      define: {
        'process.env.NODE_ENV': '"production"'
      },
      inject: []
    }
  }),

  // Library Bundle
  library: (entry: string = './src/index.ts', name: string = 'MyLibrary'): Partial<KilatPackConfig> => ({
    entry,
    splitting: false,
    globals: {
      react: 'React',
      'react-dom': 'ReactDOM'
    },
    external: ['react', 'react-dom'],
    transform: {
      jsx: 'react',
      typescript: true,
      decorators: false,
      define: {},
      inject: []
    }
  })
};

// 🔧 Plugin Helpers
export function defineConfig(config: Partial<KilatPackConfig>): Partial<KilatPackConfig> {
  return config;
}

export function mergeConfig(base: Partial<KilatPackConfig>, override: Partial<KilatPackConfig>): Partial<KilatPackConfig> {
  return {
    ...base,
    ...override,
    server: {
      ...base.server,
      ...override.server
    },
    hmr: {
      ...base.hmr,
      ...override.hmr
    },
    css: {
      ...base.css,
      ...override.css
    },
    assets: {
      ...base.assets,
      ...override.assets
    },
    transform: {
      ...base.transform,
      ...override.transform
    },
    plugins: [
      ...(base.plugins || []),
      ...(override.plugins || [])
    ]
  };
}

// Version info
export const KILATPACK_VERSION = '1.0.0';

// Default export
export default {
  version: KILATPACK_VERSION,
  createKilatPack,
  quickBuild,
  quickDev,
  presets,
  defineConfig,
  mergeConfig
};
