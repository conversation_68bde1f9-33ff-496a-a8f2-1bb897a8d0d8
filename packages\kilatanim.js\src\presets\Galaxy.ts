import * as THREE from 'three';
import type { PresetFactory, GalaxyConfig } from '../types';

/**
 * Galaxy Preset - Spiral galaxy with stars and cosmic dust
 * Creates a beautiful rotating galaxy with multiple spiral arms
 */
export const GalaxyPreset: PresetFactory = {
  create: (config: GalaxyConfig = {}) => {
    const {
      starCount = 10000,
      galaxyRadius = 15,
      spiralArms = 4,
      coreSize = 2,
      starSizes = { min: 0.1, max: 0.5 },
      colors = ['#ffffff', '#ffaa00', '#ff6600', '#0099ff', '#aa00ff'],
      particleCount = starCount
    } = config;

    const group = new THREE.Group();
    group.name = 'galaxy';

    // 🌟 Create star field
    const starGeometry = new THREE.BufferGeometry();
    const starPositions = new Float32Array(starCount * 3);
    const starColors = new Float32Array(starCount * 3);
    const starSizesArray = new Float32Array(starCount);

    for (let i = 0; i < starCount; i++) {
      const i3 = i * 3;
      
      // Generate spiral galaxy distribution
      const radius = Math.random() * galaxyRadius;
      const spinAngle = radius * 0.3; // Spiral tightness
      const branchAngle = ((i % spiralArms) / spiralArms) * Math.PI * 2;
      const angle = branchAngle + spinAngle;
      
      // Add randomness for natural look
      const randomX = (Math.random() - 0.5) * 2;
      const randomY = (Math.random() - 0.5) * 2;
      const randomZ = (Math.random() - 0.5) * 2;
      
      // Position
      starPositions[i3] = Math.cos(angle) * radius + randomX;
      starPositions[i3 + 1] = randomY * 0.5; // Flatten the galaxy
      starPositions[i3 + 2] = Math.sin(angle) * radius + randomZ;
      
      // Color based on distance from center (core is warmer)
      const distanceFromCenter = radius / galaxyRadius;
      const colorIndex = Math.floor(distanceFromCenter * (colors.length - 1));
      const color = new THREE.Color(colors[colorIndex] || colors[0]);
      
      starColors[i3] = color.r;
      starColors[i3 + 1] = color.g;
      starColors[i3 + 2] = color.b;
      
      // Size based on distance (core stars are larger)
      const sizeMultiplier = Math.max(0.1, 1 - distanceFromCenter);
      starSizesArray[i] = (starSizes.min + Math.random() * (starSizes.max - starSizes.min)) * sizeMultiplier;
    }

    starGeometry.setAttribute('position', new THREE.BufferAttribute(starPositions, 3));
    starGeometry.setAttribute('color', new THREE.BufferAttribute(starColors, 3));
    starGeometry.setAttribute('size', new THREE.BufferAttribute(starSizesArray, 1));

    // 🌟 Star material with glow effect
    const starMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        pixelRatio: { value: Math.min(window.devicePixelRatio, 2) }
      },
      vertexShader: `
        attribute float size;
        varying vec3 vColor;
        uniform float time;
        uniform float pixelRatio;
        
        void main() {
          vColor = color;
          vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
          
          // Twinkling effect
          float twinkle = sin(time * 2.0 + position.x * 100.0) * 0.5 + 0.5;
          gl_PointSize = size * pixelRatio * (300.0 / -mvPosition.z) * (0.8 + twinkle * 0.4);
          gl_Position = projectionMatrix * mvPosition;
        }
      `,
      fragmentShader: `
        varying vec3 vColor;
        
        void main() {
          float distanceToCenter = distance(gl_PointCoord, vec2(0.5));
          float strength = 0.05 / distanceToCenter - 0.1;
          
          // Glow effect
          vec3 glowColor = vColor * strength;
          gl_FragColor = vec4(glowColor, strength);
        }
      `,
      transparent: true,
      blending: THREE.AdditiveBlending,
      depthWrite: false,
      vertexColors: true
    });

    const stars = new THREE.Points(starGeometry, starMaterial);
    group.add(stars);

    // 🌌 Galaxy core
    const coreGeometry = new THREE.SphereGeometry(coreSize, 32, 32);
    const coreMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        color1: { value: new THREE.Color('#ffaa00') },
        color2: { value: new THREE.Color('#ff6600') }
      },
      vertexShader: `
        varying vec2 vUv;
        varying vec3 vPosition;
        
        void main() {
          vUv = uv;
          vPosition = position;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform float time;
        uniform vec3 color1;
        uniform vec3 color2;
        varying vec2 vUv;
        varying vec3 vPosition;
        
        void main() {
          float noise = sin(vPosition.x * 10.0 + time) * sin(vPosition.y * 10.0 + time) * sin(vPosition.z * 10.0 + time);
          vec3 color = mix(color1, color2, noise * 0.5 + 0.5);
          
          // Radial gradient
          float radial = 1.0 - length(vUv - 0.5) * 2.0;
          gl_FragColor = vec4(color * radial, radial * 0.8);
        }
      `,
      transparent: true,
      blending: THREE.AdditiveBlending
    });

    const core = new THREE.Mesh(coreGeometry, coreMaterial);
    group.add(core);

    // 🌫️ Nebula clouds
    const nebulaGeometry = new THREE.PlaneGeometry(galaxyRadius * 2, galaxyRadius * 2);
    const nebulaMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        opacity: { value: 0.1 }
      },
      vertexShader: `
        varying vec2 vUv;
        
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform float time;
        uniform float opacity;
        varying vec2 vUv;
        
        // Simple noise function
        float noise(vec2 p) {
          return sin(p.x * 10.0 + time * 0.5) * sin(p.y * 10.0 + time * 0.3) * 0.5 + 0.5;
        }
        
        void main() {
          vec2 center = vUv - 0.5;
          float dist = length(center);
          
          float n1 = noise(vUv * 3.0);
          float n2 = noise(vUv * 6.0 + vec2(time * 0.1));
          float n3 = noise(vUv * 12.0 - vec2(time * 0.05));
          
          float nebula = (n1 * 0.5 + n2 * 0.3 + n3 * 0.2) * (1.0 - dist * 2.0);
          
          vec3 color1 = vec3(0.2, 0.1, 0.8); // Purple
          vec3 color2 = vec3(0.8, 0.2, 0.4); // Pink
          vec3 color = mix(color1, color2, nebula);
          
          gl_FragColor = vec4(color, nebula * opacity);
        }
      `,
      transparent: true,
      blending: THREE.AdditiveBlending,
      side: THREE.DoubleSide
    });

    const nebula = new THREE.Mesh(nebulaGeometry, nebulaMaterial);
    nebula.rotation.x = -Math.PI / 2;
    group.add(nebula);

    return group;
  },

  update: (object: THREE.Object3D, deltaTime: number) => {
    const galaxy = object as THREE.Group;
    const time = performance.now() * 0.001;

    // Rotate the entire galaxy slowly
    galaxy.rotation.y += deltaTime * 0.1;

    // Update shader uniforms
    galaxy.traverse((child) => {
      if (child instanceof THREE.Points || child instanceof THREE.Mesh) {
        const material = child.material as THREE.ShaderMaterial;
        if (material.uniforms?.time) {
          material.uniforms.time.value = time;
        }
      }
    });

    // Add subtle wobble to the galaxy
    galaxy.rotation.x = Math.sin(time * 0.2) * 0.05;
    galaxy.rotation.z = Math.cos(time * 0.15) * 0.03;
  },

  dispose: (object: THREE.Object3D) => {
    object.traverse((child) => {
      if (child instanceof THREE.Mesh || child instanceof THREE.Points) {
        child.geometry?.dispose();
        if (Array.isArray(child.material)) {
          child.material.forEach(material => material.dispose());
        } else {
          child.material?.dispose();
        }
      }
    });
  }
};
