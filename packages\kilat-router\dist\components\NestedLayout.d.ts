import React from 'react';

/**
 * 🏗️ NestedLayout Component
 * Layout component for nested routes
 */
export interface NestedLayoutProps {
  children?: React.ReactNode;
  header?: React.ComponentType;
  footer?: React.ComponentType;
  sidebar?: React.ComponentType;
  className?: string;
}

export declare function NestedLayout(props: NestedLayoutProps): React.ReactElement;
export default NestedLayout;
