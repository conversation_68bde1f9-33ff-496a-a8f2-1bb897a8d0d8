{"expo": {"name": "Kilat.js Mobile", "slug": "kilat-mobile-demo", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#000011"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "org.kilatjs.mobile.demo", "buildNumber": "1.0.0"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#000011"}, "package": "org.kilatjs.mobile.demo", "versionCode": 1}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": ["expo-router", ["expo-splash-screen", {"backgroundColor": "#000011", "image": "./assets/splash.png", "imageWidth": 200}], ["expo-font", {"fonts": ["./assets/fonts/SpaceMono-Regular.ttf"]}]], "scheme": "kilat-mobile", "extra": {"router": {"origin": false}, "eas": {"projectId": "kilat-mobile-demo"}}}}