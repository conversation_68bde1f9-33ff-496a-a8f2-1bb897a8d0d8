import React from 'react';
import { Link, LinkProps } from 'react-router-dom';

/**
 * 🔗 KilatLink Component
 * Enhanced Link component with Kilat.js features
 */
export interface KilatLinkProps extends LinkProps {
  prefetch?: boolean;
  external?: boolean;
  variant?: 'default' | 'button' | 'nav';
}

export function KilatLink({ 
  prefetch = false, 
  external = false, 
  variant = 'default',
  className = '',
  ...props 
}: KilatLinkProps) {
  const baseClasses = {
    default: 'k-link',
    button: 'k-btn k-btn-primary',
    nav: 'k-nav-link'
  };

  const classes = `${baseClasses[variant]} ${className}`.trim();

  if (external) {
    return (
      <a 
        href={props.to as string}
        className={classes}
        target="_blank"
        rel="noopener noreferrer"
        {...(props as any)}
      />
    );
  }

  return <Link {...props} className={classes} />;
}

export default KilatLink;
